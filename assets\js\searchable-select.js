/**
 * Searchable Select Component
 * إضافة إمكانية البحث لجميع القوائم المنسدلة في النظام
 * 
 * الاستخدام:
 * 1. أضف class "searchable-select" للعنصر select
 * 2. لف العنصر في div بـ class "searchable-select-wrapper"
 * 3. أضف data-placeholder للنص التوضيحي
 */

class SearchableSelect {
    constructor(selectElement) {
        this.select = selectElement;
        this.wrapper = selectElement.closest('.searchable-select-wrapper');
        this.options = Array.from(selectElement.options);
        this.filteredOptions = [...this.options];
        this.selectedIndex = -1;
        this.isOpen = false;
        this.placeholder = selectElement.dataset.placeholder || 'ابحث...';
        
        this.init();
    }
    
    init() {
        // إنشاء حقل البحث
        this.createSearchInput();
        
        // إنشاء حاوية الخيارات
        this.createOptionsContainer();
        
        // إدراج العناصر
        this.wrapper.appendChild(this.searchInput);
        this.wrapper.appendChild(this.optionsContainer);
        
        // ملء الخيارات
        this.populateOptions();
        
        // ربط الأحداث
        this.bindEvents();
        
        // إضافة class للتنسيق
        this.wrapper.classList.add('searchable-select-initialized');
    }
    
    createSearchInput() {
        this.searchInput = document.createElement('input');
        this.searchInput.type = 'text';
        this.searchInput.className = 'select-search-input form-control';
        this.searchInput.placeholder = 'ابحث...';
        this.searchInput.autocomplete = 'off';
    }
    
    createOptionsContainer() {
        this.optionsContainer = document.createElement('div');
        this.optionsContainer.className = 'select-options';
    }
    
    populateOptions() {
        this.optionsContainer.innerHTML = '';
        
        if (this.filteredOptions.length === 0 || 
            (this.filteredOptions.length === 1 && this.filteredOptions[0].value === '')) {
            const noResults = document.createElement('div');
            noResults.className = 'no-results';
            noResults.innerHTML = '<i class="fas fa-search text-muted me-2"></i>لا توجد نتائج';
            this.optionsContainer.appendChild(noResults);
            return;
        }
        
        this.filteredOptions.forEach((option, index) => {
            if (option.value === '') return; // تخطي الخيار الفارغ
            
            const optionElement = document.createElement('div');
            optionElement.className = 'select-option';
            optionElement.innerHTML = `
                <div class="option-content">
                    <span class="option-text">${this.highlightMatch(option.text)}</span>
                    ${option.selected ? '<i class="fas fa-check text-success ms-2"></i>' : ''}
                </div>
            `;
            optionElement.dataset.value = option.value;
            optionElement.dataset.index = index;
            
            if (option.selected) {
                optionElement.classList.add('selected');
            }
            
            optionElement.addEventListener('click', () => {
                this.selectOption(option.value, option.text);
            });
            
            this.optionsContainer.appendChild(optionElement);
        });
    }
    
    highlightMatch(text) {
        const searchTerm = this.searchInput.value.toLowerCase().trim();
        if (!searchTerm) return text;
        
        const regex = new RegExp(`(${searchTerm})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }
    
    bindEvents() {
        // نقرة على القائمة المنسدلة
        this.select.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggle();
        });
        
        // منع فتح القائمة الأصلية
        this.select.addEventListener('mousedown', (e) => {
            e.preventDefault();
        });
        
        // البحث
        this.searchInput.addEventListener('input', (e) => {
            this.filter(e.target.value);
        });
        
        // التنقل بالكيبورد
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });
        
        // إغلاق عند النقر خارج العنصر
        document.addEventListener('click', (e) => {
            if (!this.wrapper.contains(e.target)) {
                this.close();
            }
        });
        
        // منع إرسال النموذج عند الضغط على Enter
        this.searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
            }
        });
        
        // تحديث النص عند تغيير القيمة برمجياً
        this.select.addEventListener('change', () => {
            this.updateSelectedOption();
        });
    }
    
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }
    
    open() {
        // إغلاق أي قوائم أخرى مفتوحة
        document.querySelectorAll('.searchable-select.open').forEach(select => {
            if (select !== this.select) {
                select.classList.remove('open');
            }
        });
        
        this.isOpen = true;
        this.select.classList.add('open');
        this.searchInput.style.display = 'block';
        this.optionsContainer.style.display = 'block';
        
        // تركيز على حقل البحث
        setTimeout(() => {
            this.searchInput.focus();
            this.searchInput.select();
        }, 50);
        
        this.searchInput.value = '';
        this.filter('');
        this.selectedIndex = -1;
    }
    
    close() {
        this.isOpen = false;
        this.select.classList.remove('open');
        this.searchInput.style.display = 'none';
        this.optionsContainer.style.display = 'none';
        this.selectedIndex = -1;
    }
    
    filter(searchTerm) {
        const term = searchTerm.toLowerCase().trim();
        
        this.filteredOptions = this.options.filter(option => {
            if (option.value === '') return false;
            return option.text.toLowerCase().includes(term) || 
                   option.value.toLowerCase().includes(term);
        });
        
        this.populateOptions();
        this.selectedIndex = -1;
    }
    
    selectOption(value, text) {
        // تحديث القائمة المنسدلة الأصلية
        this.select.value = value;
        
        // تحديث الحالة البصرية
        this.updateSelectedOption();
        
        // إطلاق حدث التغيير
        const changeEvent = new Event('change', { bubbles: true });
        this.select.dispatchEvent(changeEvent);
        
        // إضافة تأثير بصري
        this.select.classList.add('just-selected');
        setTimeout(() => {
            this.select.classList.remove('just-selected');
        }, 300);
        
        this.close();
    }
    
    updateSelectedOption() {
        const options = this.optionsContainer.querySelectorAll('.select-option');
        options.forEach(opt => {
            const isSelected = opt.dataset.value === this.select.value;
            opt.classList.toggle('selected', isSelected);
            
            const checkIcon = opt.querySelector('.fa-check');
            if (isSelected && !checkIcon) {
                opt.querySelector('.option-content').innerHTML += '<i class="fas fa-check text-success ms-2"></i>';
            } else if (!isSelected && checkIcon) {
                checkIcon.remove();
            }
        });
    }
    
    handleKeydown(e) {
        const visibleOptions = this.optionsContainer.querySelectorAll('.select-option:not(.hidden)');
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.selectedIndex = Math.min(this.selectedIndex + 1, visibleOptions.length - 1);
                this.highlightOption(visibleOptions);
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                this.selectedIndex = Math.max(this.selectedIndex - 1, 0);
                this.highlightOption(visibleOptions);
                break;
                
            case 'Enter':
                e.preventDefault();
                if (this.selectedIndex >= 0 && visibleOptions[this.selectedIndex]) {
                    const option = visibleOptions[this.selectedIndex];
                    this.selectOption(option.dataset.value, option.querySelector('.option-text').textContent);
                }
                break;
                
            case 'Escape':
                e.preventDefault();
                this.close();
                break;
                
            case 'Tab':
                this.close();
                break;
        }
    }
    
    highlightOption(visibleOptions) {
        // إزالة التمييز من جميع الخيارات
        visibleOptions.forEach(opt => opt.classList.remove('highlighted'));
        
        // تمييز الخيار المحدد
        if (this.selectedIndex >= 0 && visibleOptions[this.selectedIndex]) {
            const highlightedOption = visibleOptions[this.selectedIndex];
            highlightedOption.classList.add('highlighted');
            
            // التمرير للخيار المحدد
            highlightedOption.scrollIntoView({
                block: 'nearest',
                behavior: 'smooth'
            });
        }
    }
    
    // تدمير المكون
    destroy() {
        if (this.searchInput) this.searchInput.remove();
        if (this.optionsContainer) this.optionsContainer.remove();
        this.select.classList.remove('open', 'searchable-select');
        this.wrapper.classList.remove('searchable-select-initialized');
    }
}

// مدير المكونات
class SearchableSelectManager {
    constructor() {
        this.instances = new Map();
        this.init();
    }
    
    init() {
        // تهيئة المكونات عند تحميل الصفحة
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeAll());
        } else {
            this.initializeAll();
        }
        
        // مراقبة التغييرات في DOM
        this.observeChanges();
    }
    
    initializeAll() {
        const selects = document.querySelectorAll('.searchable-select:not(.initialized)');
        selects.forEach(select => this.initialize(select));
    }
    
    initialize(selectElement) {
        if (this.instances.has(selectElement)) return;
        
        // التأكد من وجود wrapper
        let wrapper = selectElement.closest('.searchable-select-wrapper');
        if (!wrapper) {
            wrapper = document.createElement('div');
            wrapper.className = 'searchable-select-wrapper';
            selectElement.parentNode.insertBefore(wrapper, selectElement);
            wrapper.appendChild(selectElement);
        }
        
        const instance = new SearchableSelect(selectElement);
        this.instances.set(selectElement, instance);
        selectElement.classList.add('initialized');
    }
    
    destroy(selectElement) {
        const instance = this.instances.get(selectElement);
        if (instance) {
            instance.destroy();
            this.instances.delete(selectElement);
            selectElement.classList.remove('initialized');
        }
    }
    
    observeChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // البحث عن عناصر select جديدة
                        const newSelects = node.querySelectorAll ? 
                            node.querySelectorAll('.searchable-select:not(.initialized)') : [];
                        newSelects.forEach(select => this.initialize(select));
                        
                        // إذا كان العنصر نفسه select
                        if (node.classList && node.classList.contains('searchable-select') && 
                            !node.classList.contains('initialized')) {
                            this.initialize(node);
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
}

// تهيئة المدير العام
window.SearchableSelectManager = new SearchableSelectManager();

// دوال مساعدة عامة
window.SearchableSelect = {
    // تهيئة عنصر محدد
    init: function(selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => window.SearchableSelectManager.initialize(el));
    },
    
    // تدمير عنصر محدد
    destroy: function(selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => window.SearchableSelectManager.destroy(el));
    },
    
    // إعادة تهيئة جميع العناصر
    reinit: function() {
        window.SearchableSelectManager.initializeAll();
    }
};
