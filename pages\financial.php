<?php
// التحقق من الصلاحيات
if (!hasPermission('financial')) {
    $_SESSION['error'] = 'ليس لديك صلاحية الوصول إلى هذه الصفحة';
    echo '<script>window.location.href = "index.php?page=unauthorized";</script>';
    return;
}

// تحديد الإجراء
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$documentId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$documentType = isset($_GET['type']) ? $_GET['type'] : '';

// التحقق من صحة نوع المستند
$validTypes = ['receipt', 'payment', 'expense', 'transfer', 'capital', 'profit_distribution'];
if (!empty($documentType) && !in_array($documentType, $validTypes)) {
    $_SESSION['error'] = 'نوع المستند غير صحيح';
    echo '<script>window.location.href = "index.php?page=financial";</script>';
    return;
}

// معالجة الإجراءات
switch ($action) {
    case 'add':
        include 'pages/financial_add.php';
        break;
    case 'add_new':
        include 'pages/financial_add_new.php';
        break;
    case 'edit':
        include 'pages/financial_edit.php';
        break;

    case 'view':
        include 'pages/financial_view.php';
        break;

    case 'print':
        include 'pages/financial_print.php';
        break;

    case 'delete':
        // التحقق من وجود المستند
        if ($documentId > 0) {
            try {
                $db->beginTransaction();

                // التحقق من نوع المستند قبل الحذف
                $documentStmt = $db->prepare("SELECT penalty_id, document_type FROM financial_documents WHERE id = :id");
                $documentStmt->execute(['id' => $documentId]);
                $document = $documentStmt->fetch();

                if ($document) {
                    // حذف الحاويات المرتبطة بالمستند من جدول document_containers
                    $deleteContainersStmt = $db->prepare("DELETE FROM document_containers WHERE document_id = :document_id");
                    $deleteContainersStmt->execute(['document_id' => $documentId]);

                    // إذا كان المستند مرتبط بغرامة، تحديث حالة الغرامة
                    if ($document['penalty_id']) {
                        $updatePenaltyStmt = $db->prepare("UPDATE penalties SET is_paid = 0, payment_date = NULL, payment_reference = NULL WHERE id = :penalty_id");
                        $updatePenaltyStmt->execute(['penalty_id' => $document['penalty_id']]);
                    }

                    // حذف المستند
                    $stmt = $db->prepare("DELETE FROM financial_documents WHERE id = :id");
                    $stmt->execute(['id' => $documentId]);

                    // تسجيل النشاط
                    $activityStmt = $db->prepare("
                        INSERT INTO activity_log (user_id, activity_type, description, ip_address)
                        VALUES (:user_id, 'delete_document', :description, :ip_address)
                    ");
                    $activityStmt->execute([
                        'user_id' => getCurrentUserId(),
                        'description' => 'تم حذف المستند المالي رقم: ' . $documentId . ' مع الحاويات المرتبطة',
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    ]);

                    $db->commit();
                    $_SESSION['success'] = 'تم حذف المستند والحاويات المرتبطة بنجاح';
                } else {
                    $db->rollback();
                    $_SESSION['error'] = 'المستند غير موجود';
                }
            } catch (PDOException $e) {
                $db->rollback();
                $_SESSION['error'] = 'حدث خطأ أثناء حذف المستند: ' . $e->getMessage();
            }
        } else {
            $_SESSION['error'] = 'معرف المستند غير صحيح';
        }

        // إعادة التوجيه إلى قائمة المستندات
        echo '<script>window.location.href = "index.php?page=financial";</script>';
        return;
        break;

    case 'list':
    default:
        // استعلام البحث
        $searchTerm = isset($_GET['search']) ? $_GET['search'] : '';
        $typeFilter = isset($_GET['type']) ? $_GET['type'] : '';
        $customerFilter = isset($_GET['customer_id']) ? (int)$_GET['customer_id'] : 0;
        $dateFrom = isset($_GET['date_from']) ? convertDateToMysql($_GET['date_from']) : '';
        $dateTo = isset($_GET['date_to']) ? convertDateToMysql($_GET['date_to']) : '';

        // بناء استعلام البحث
        $query = "
            SELECT fd.*, t.name as customer_name
            FROM financial_documents fd
            LEFT JOIN traders t ON fd.customer_id = t.id
            WHERE 1=1
        ";
        $params = [];

        if (!empty($searchTerm)) {
            $query .= " AND (fd.document_number LIKE :search OR fd.description LIKE :search OR t.name LIKE :search)";
            $params['search'] = "%$searchTerm%";
        }

        if (!empty($typeFilter)) {
            $query .= " AND fd.document_type = :type";
            $params['type'] = $typeFilter;
        }

        if ($customerFilter > 0) {
            $query .= " AND fd.customer_id = :customer_id";
            $params['customer_id'] = $customerFilter;
        }

        if (!empty($dateFrom)) {
            $query .= " AND fd.document_date >= :date_from";
            $params['date_from'] = $dateFrom;
        }

        if (!empty($dateTo)) {
            $query .= " AND fd.document_date <= :date_to";
            $params['date_to'] = $dateTo;
        }

        $query .= " ORDER BY fd.document_date DESC, fd.created_at DESC";

        try {
            // تنفيذ الاستعلا
            $stmt = $db->prepare($query);
            $stmt->execute($params);
            $documents = $stmt->fetchAll();

            // الحصول على قائمة الزبائن للفلتر
            $customersStmt = $db->query("SELECT id, name FROM traders ORDER BY name");
            $customers = $customersStmt->fetchAll();

            // حساب الإجماليات
            $totalReceipts = 0;
            $totalPayments = 0;

            foreach ($documents as $doc) {
                if (in_array($doc['document_type'], ['receipt', 'capital'])) {
                    $totalReceipts += $doc['amount'];
                } elseif (in_array($doc['document_type'], ['payment', 'expense'])) {
                    $totalPayments += $doc['amount'];
                }
            }

            $balance = $totalReceipts - $totalPayments;

        } catch (PDOException $e) {
            $_SESSION['error'] = 'حدث خطأ أثناء استرجاع بيانات المستندات: ' . $e->getMessage();
            $documents = [];
            $customers = [];
            $totalReceipts = 0;
            $totalPayments = 0;
            $balance = 0;
        }

        // عرض قائمة المستندات
        ?>
        <!-- Content Header (Page header) -->
        <div class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1 class="m-0">المستندات المالية</h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                            <li class="breadcrumb-item active">المستندات المالية</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <!-- Action Buttons -->
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-plus"></i> إضافة مستند جديد
                            </button>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="index.php?page=financial&action=add_new&type=receipt">
                                    <i class="fas fa-arrow-down text-success"></i> سند قبض
                                </a>
                                <a class="dropdown-item" href="index.php?page=financial&action=add_new&type=payment">
                                    <i class="fas fa-arrow-up text-danger"></i> سند دفع
                                </a>
                                <a class="dropdown-item" href="index.php?page=financial&action=add_new&type=expense">
                                    <i class="fas fa-hand-holding-usd text-warning"></i> سند صرف
                                </a>
                                <a class="dropdown-item" href="index.php?page=financial&action=add_new&type=transfer">
                                    <i class="fas fa-exchange-alt text-info"></i> حوالة مالية
                                </a>
                                <a class="dropdown-item" href="index.php?page=financial&action=add_new&type=capital">
                                    <i class="fas fa-piggy-bank text-primary"></i> رأس المال
                                </a>
                                <a class="dropdown-item" href="index.php?page=financial&action=add_new&type=profit_distribution">
                                    <i class="fas fa-chart-pie text-purple"></i> توزيع أرباح
                                </a>
                            </div>
                        </div>
                        <a href="index.php?page=reports&type=financial" class="btn btn-info">
                            <i class="fas fa-chart-line"></i> التقارير المالية
                        </a>
                        <button type="button" class="btn btn-secondary" onclick="window.print()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                    </div>
                </div>

                <!-- Alerts -->
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        <h5><i class="icon fas fa-check"></i> نجح!</h5>
                        <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        <h5><i class="icon fas fa-ban"></i> خطأ!</h5>
                        <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                    </div>
                <?php endif; ?>

                <!-- Search and Filter Card -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-search"></i> بحث وتصفية
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <form method="get" action="index.php">
                            <input type="hidden" name="page" value="financial">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="search">
                                            <i class="fas fa-search"></i> بحث
                                        </label>
                                        <input type="text" class="form-control" id="search" name="search"
                                               value="<?php echo htmlspecialchars($searchTerm); ?>"
                                               placeholder="رقم المستند، الوصف، اسم الزبون">
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="type">
                                            <i class="fas fa-file-alt"></i> نوع المستند
                                        </label>
                                        <div class="searchable-select-wrapper">
                                            <select class="form-control searchable-select" id="type" name="type" data-placeholder="اختر نوع المستند...">
                                                <option value="">جميع الأنواع</option>
                                                <option value="receipt" <?php echo $typeFilter == 'receipt' ? 'selected' : ''; ?>>سند قبض</option>
                                                <option value="payment" <?php echo $typeFilter == 'payment' ? 'selected' : ''; ?>>سند دفع</option>
                                                <option value="expense" <?php echo $typeFilter == 'expense' ? 'selected' : ''; ?>>سند صرف</option>
                                                <option value="transfer" <?php echo $typeFilter == 'transfer' ? 'selected' : ''; ?>>حوالة مالية</option>
                                                <option value="capital" <?php echo $typeFilter == 'capital' ? 'selected' : ''; ?>>رأس المال</option>
                                                <option value="profit_distribution" <?php echo $typeFilter == 'profit_distribution' ? 'selected' : ''; ?>>توزيع أرباح</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="customer_id">
                                            <i class="fas fa-user"></i> الزبون
                                        </label>
                                        <div class="searchable-select-wrapper">
                                            <select class="form-control searchable-select" id="customer_id" name="customer_id" data-placeholder="اختر الزبون أو ابحث...">
                                                <option value="">جميع الزبائن</option>
                                                <?php foreach ($customers as $customer): ?>
                                                    <option value="<?php echo $customer['id']; ?>" <?php echo $customerFilter == $customer['id'] ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($customer['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="date_from">
                                            <i class="fas fa-calendar-alt"></i> من تاريخ
                                        </label>
                                        <input type="date" class="form-control" id="date_from" name="date_from"
                                               value="<?php echo !empty($dateFrom) ? $dateFrom : ''; ?>">
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="date_to">
                                            <i class="fas fa-calendar-alt"></i> إلى تاريخ
                                        </label>
                                        <input type="date" class="form-control" id="date_to" name="date_to"
                                               value="<?php echo !empty($dateTo) ? $dateTo : ''; ?>">
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <div class="d-block">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search"></i> بحث
                                            </button>
                                            <a href="index.php?page=financial" class="btn btn-default">
                                                <i class="fas fa-redo"></i> إعادة تعيين
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row">
                    <div class="col-lg-4 col-6">
                        <!-- small box -->
                        <div class="small-box bg-success">
                            <div class="inner">
                                <h3><?php echo number_format($totalReceipts, 0); ?></h3>
                                <p>إجمالي الإيرادات</p>
                            </div>
                            <div class="icon">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <a href="index.php?page=financial&type=receipt" class="small-box-footer">
                                المزيد من التفاصيل <i class="fas fa-arrow-circle-right"></i>
                            </a>
                        </div>
                    </div>
                    <!-- ./col -->
                    <div class="col-lg-4 col-6">
                        <!-- small box -->
                        <div class="small-box bg-danger">
                            <div class="inner">
                                <h3><?php echo number_format($totalPayments, 0); ?></h3>
                                <p>إجمالي المصروفات</p>
                            </div>
                            <div class="icon">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <a href="index.php?page=financial&type=payment" class="small-box-footer">
                                المزيد من التفاصيل <i class="fas fa-arrow-circle-right"></i>
                            </a>
                        </div>
                    </div>
                    <!-- ./col -->
                    <div class="col-lg-4 col-6">
                        <!-- small box -->
                        <div class="small-box <?php echo $balance >= 0 ? 'bg-info' : 'bg-warning'; ?>">
                            <div class="inner">
                                <h3><?php echo number_format(abs($balance), 0); ?></h3>
                                <p>الرصيد <?php echo $balance >= 0 ? '(موجب)' : '(سالب)'; ?></p>
                            </div>
                            <div class="icon">
                                <i class="fas fa-balance-scale"></i>
                            </div>
                            <a href="index.php?page=reports&type=financial" class="small-box-footer">
                                عرض التقرير <i class="fas fa-arrow-circle-right"></i>
                            </a>
                        </div>
                    </div>
                    <!-- ./col -->
                </div>
                <!-- /.row -->

                <!-- Documents Table -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-file-invoice-dollar"></i> قائمة المستندات المالية
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button type="button" class="btn btn-tool" data-card-widget="maximize">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body table-responsive p-0">
                        <?php if (empty($documents)): ?>
                            <div class="callout callout-info m-3">
                                <h5><i class="fas fa-info"></i> ملاحظة:</h5>
                                لا توجد مستندات مالية مطابقة للبحث.
                            </div>
                        <?php else: ?>
                            <table class="table table-hover text-nowrap">
                                <thead>
                                    <tr>
                                        <th style="width: 50px">#</th>
                                        <th>رقم المستند</th>
                                        <th>النوع</th>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                        <th>الزبون</th>
                                        <th>طريقة الدفع</th>
                                        <th>الوصف</th>
                                        <th style="width: 150px">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php foreach ($documents as $index => $document): ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td><?php echo htmlspecialchars($document['document_number']); ?></td>
                                        <td>
                                            <?php
                                            $typeClass = '';
                                            $typeText = '';

                                            switch ($document['document_type']) {
                                                case 'receipt':
                                                    $typeClass = 'success';
                                                    $typeText = 'سند قبض';
                                                    break;
                                                case 'payment':
                                                    $typeClass = 'danger';
                                                    $typeText = 'سند دفع';
                                                    break;
                                                case 'expense':
                                                    $typeClass = 'warning';
                                                    $typeText = 'سند صرف';
                                                    break;
                                                case 'transfer':
                                                    $typeClass = 'info';
                                                    $typeText = 'حوالة مالية';
                                                    break;
                                                case 'capital':
                                                    $typeClass = 'primary';
                                                    $typeText = 'رأس المال';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge badge-<?php echo $typeClass; ?>"><?php echo $typeText; ?></span>
                                        </td>
                                        <td class="number"><?php echo number_format($document['amount'], 2); ?></td>
                                        <td><?php echo convertDateFromMysql($document['document_date']); ?></td>
                                        <td><?php echo $document['customer_id'] ? htmlspecialchars($document['customer_name']) : '-'; ?></td>
                                        <td>
                                            <?php
                                            $methodText = '';

                                            switch ($document['payment_method']) {
                                                case 'cash':
                                                    $methodText = 'نقدي';
                                                    break;
                                                case 'bank_transfer':
                                                    $methodText = 'تحويل بنكي';
                                                    break;
                                                case 'check':
                                                    $methodText = 'شيك';
                                                    break;
                                                case 'other':
                                                    $methodText = 'أخرى';
                                                    break;
                                            }
                                            ?>
                                            <?php echo $methodText; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($document['description']); ?></td>
                                        <td>
                                            <div class="btn-group">
                                                 <a href="index.php?page=financial&action=print&id=<?php echo $document['id']; ?>"
                                                   class="btn btn-info btn-sm" title="عرض" target="_blank">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="index.php?page=financial&action=edit&id=<?php echo $document['id']; ?>"
                                                   class="btn btn-primary btn-sm" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                               
                                                <a href="index.php?page=financial&action=delete&id=<?php echo $document['id']; ?>"
                                                   class="btn btn-danger btn-sm" title="حذف"
                                                   onclick="return confirmDelete(event, 'هل أنت متأكد من حذف هذا المستند؟')">
                                                    <i class="fas fa-trash-alt"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </table>
                        <?php endif; ?>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.container-fluid -->
        </section>
        <!-- /.content -->
        <?php
        break;
}
?>

