<?php
// التحقق من الصلاحيات (مؤقت - معطل)
/*
if (!hasPermission('transfers')) {
    $_SESSION['error'] = 'ليس لديك صلاحية الوصول إلى هذه الصفحة';
    echo '<script>window.location.href = "index.php?page=unauthorized";</script>';
    return;
}
*/

// معالجة حذف التحويل
if (isset($_POST['delete_transfer']) && isset($_POST['transfer_id'])) {
    try {
        $transferId = (int)$_POST['transfer_id'];
        
        // التحقق من عدم وجود تحويل في حالة "في التقدم" أو "مكتمل"
        $checkStmt = $db->prepare("SELECT status FROM container_transfers WHERE id = ?");
        $checkStmt->execute([$transferId]);
        $status = $checkStmt->fetchColumn();
        
        if (in_array($status, ['in_progress', 'completed'])) {
            $_SESSION['error'] = 'لا يمكن حذف التحويل في هذه الحالة. يمكنك إلغاؤه بدلاً من ذلك.';
        } else {
            // حذف التحويل والحاويات المرتبطة به (cascade delete)
            $stmt = $db->prepare("DELETE FROM container_transfers WHERE id = ?");
            $stmt->execute([$transferId]);
            $_SESSION['success'] = 'تم حذف التحويل بنجاح';
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = 'حدث خطأ أثناء حذف التحويل: ' . $e->getMessage();
    }
    
    echo '<script>window.location.href = "index.php?page=transfers";</script>';
    return;
}

// معالجة تغيير حالة التحويل
if (isset($_POST['update_status']) && isset($_POST['transfer_id']) && isset($_POST['new_status'])) {
    try {
        $transferId = (int)$_POST['transfer_id'];
        $newStatus = $_POST['new_status'];
        $notes = trim($_POST['status_notes'] ?? '');
        
        // تحديث حالة التحويل
        $updateStmt = $db->prepare("UPDATE container_transfers SET status = ?, updated_at = NOW() WHERE id = ?");
        $updateStmt->execute([$newStatus, $transferId]);
        
        // إضافة سجل تتبع
        $trackingStmt = $db->prepare("
            INSERT INTO transfer_tracking (transfer_id, status, update_time, notes, updated_by) 
            VALUES (?, ?, NOW(), ?, ?)
        ");
        $trackingStmt->execute([$transferId, $newStatus, $notes, $_SESSION['user_id']]);
        
        // تحديث وقت الاستلام/التسليم حسب الحالة
        if ($newStatus === 'in_progress') {
            $db->prepare("UPDATE container_transfers SET pickup_time = NOW() WHERE id = ?")->execute([$transferId]);
        } elseif ($newStatus === 'completed') {
            $db->prepare("UPDATE container_transfers SET delivery_time = NOW() WHERE id = ?")->execute([$transferId]);
        }
        
        $_SESSION['success'] = 'تم تحديث حالة التحويل بنجاح';
    } catch (PDOException $e) {
        $_SESSION['error'] = 'حدث خطأ أثناء تحديث الحالة: ' . $e->getMessage();
    }
    
    echo '<script>window.location.href = "index.php?page=transfers";</script>';
    return;
}

// معاملات البحث والفلترة
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$trader_filter = isset($_GET['trader_id']) ? (int)$_GET['trader_id'] : 0;
$driver_filter = isset($_GET['driver_id']) ? (int)$_GET['driver_id'] : 0;
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'created_at';
$order = isset($_GET['order']) && $_GET['order'] === 'asc' ? 'ASC' : 'DESC';

// بناء الاستعلام
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(ct.transfer_number LIKE ? OR t.name LIKE ? OR d.driver_name LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($status_filter)) {
    $where_conditions[] = "ct.status = ?";
    $params[] = $status_filter;
}

if (!empty($date_from)) {
    $where_conditions[] = "ct.transfer_date >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "ct.transfer_date <= ?";
    $params[] = $date_to;
}

if ($trader_filter > 0) {
    $where_conditions[] = "ct.trader_id = ?";
    $params[] = $trader_filter;
}

if ($driver_filter > 0) {
    $where_conditions[] = "ct.driver_id = ?";
    $params[] = $driver_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// الحصول على إجمالي عدد السجلات
$count_sql = "
    SELECT COUNT(*) 
    FROM container_transfers ct
    LEFT JOIN traders t ON ct.trader_id = t.id
    LEFT JOIN drivers d ON ct.driver_id = d.id
    $where_clause
";
$count_stmt = $db->prepare($count_sql);
$count_stmt->execute($params);
$total_records = $count_stmt->fetchColumn();

// إعداد الترقيم
$records_per_page = 15;
$total_pages = ceil($total_records / $records_per_page);
$current_page = isset($_GET['page_num']) ? max(1, (int)$_GET['page_num']) : 1;
$offset = ($current_page - 1) * $records_per_page;

// الاستعلام الرئيسي مع الترقيم
$allowed_sorts = ['transfer_number', 'transfer_date', 'status', 'total_containers', 'total_amount', 'created_at'];
$sort = in_array($sort, $allowed_sorts) ? $sort : 'created_at';

$sql = "
    SELECT ct.*, 
           t.name as trader_name,
           t.phone as trader_phone,
           d.driver_name,
           d.phone as driver_phone,
           d.vehicle_number,
           u.full_name as created_by_name,
           (SELECT COUNT(*) FROM transfer_containers WHERE transfer_id = ct.id) as actual_containers
    FROM container_transfers ct
    LEFT JOIN traders t ON ct.trader_id = t.id
    LEFT JOIN drivers d ON ct.driver_id = d.id
    LEFT JOIN users u ON ct.created_by = u.id
    $where_clause
    ORDER BY ct.$sort $order
    LIMIT $records_per_page OFFSET $offset
";

$stmt = $db->prepare($sql);
$stmt->execute($params);
$transfers = $stmt->fetchAll();

// إحصائيات سريعة
$stats_sql = "
    SELECT 
        COUNT(*) as total_transfers,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_transfers,
        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as inprogress_transfers,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_transfers,
        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_transfers,
        COALESCE(SUM(total_amount), 0) as total_revenue,
        COALESCE(SUM(total_containers), 0) as total_containers_moved
    FROM container_transfers
";
$stats_stmt = $db->query($stats_sql);
$stats = $stats_stmt->fetch();

// قائمة التجار للفلترة
$traders_sql = "SELECT id, name FROM traders WHERE active = 1 ORDER BY name";
$traders_stmt = $db->query($traders_sql);
$traders = $traders_stmt->fetchAll();

// قائمة السائقين للفلترة
$drivers_sql = "SELECT id, driver_name FROM drivers WHERE status = 'active' ORDER BY driver_name";
$drivers_stmt = $db->query($drivers_sql);
$drivers = $drivers_stmt->fetchAll();
?>

<div class="content-header bg-gradient-primary">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-8">
                <div class="d-flex align-items-center">
                    <div class="page-icon bg-white bg-opacity-25 rounded-circle p-3 me-3">
                        <i class="fas fa-shipping-fast text-white fa-2x"></i>
                    </div>
                    <div>
                        <h1 class="m-0 text-white">إدارة تحويلات الحاويات</h1>
                        <p class="text-white-50 mb-0">إدارة وتتبع تحويلات الحاويات للتجار مع السائقين</p>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <ol class="breadcrumb float-sm-right bg-transparent">
                    <li class="breadcrumb-item"><a href="index.php" class="text-white-50">الرئيسية</a></li>
                    <li class="breadcrumb-item"><span class="text-white-50">النقل والتحويلات</span></li>
                    <li class="breadcrumb-item active text-white">تحويلات الحاويات</li>
                </ol>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center flex-wrap gap-2">
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="index.php?page=drivers" class="btn btn-light btn-sm shadow-sm">
                            <i class="fas fa-user-tie me-2"></i>إدارة السائقين
                        </a>
                        <a href="index.php?page=containers" class="btn btn-light btn-sm shadow-sm">
                            <i class="fas fa-boxes me-2"></i>إدارة الحاويات
                        </a>
                    </div>
                    <a href="index.php?page=transfers&action=add" class="btn btn-warning btn-lg shadow-sm pulse-animation">
                        <i class="fas fa-plus me-2"></i>إضافة تحويل جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="content">
    <div class="container-fluid">

        <!-- الإحصائيات السريعة -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100 card-hover">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="text-primary mb-1">
                                    <i class="fas fa-shipping-fast fa-lg"></i>
                                </div>
                                <h3 class="mb-1 text-primary fw-bold"><?php echo number_format($stats['total_transfers']); ?></h3>
                                <p class="mb-0 text-muted small">إجمالي التحويلات</p>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-primary" style="width: 100%"></div>
                                </div>
                            </div>
                            <div class="stat-icon bg-primary bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-shipping-fast text-primary fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100 card-hover">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="text-warning mb-1">
                                    <i class="fas fa-clock fa-lg"></i>
                                </div>
                                <h3 class="mb-1 text-warning fw-bold"><?php echo number_format($stats['pending_transfers']); ?></h3>
                                <p class="mb-0 text-muted small">في الانتظار</p>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-warning" style="width: <?php echo $stats['total_transfers'] > 0 ? ($stats['pending_transfers'] / $stats['total_transfers']) * 100 : 0; ?>%"></div>
                                </div>
                            </div>
                            <div class="stat-icon bg-warning bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-clock text-warning fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100 card-hover">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="text-info mb-1">
                                    <i class="fas fa-spinner fa-lg"></i>
                                </div>
                                <h3 class="mb-1 text-info fw-bold"><?php echo number_format($stats['inprogress_transfers']); ?></h3>
                                <p class="mb-0 text-muted small">جاري التنفيذ</p>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-info" style="width: <?php echo $stats['total_transfers'] > 0 ? ($stats['inprogress_transfers'] / $stats['total_transfers']) * 100 : 0; ?>%"></div>
                                </div>
                            </div>
                            <div class="stat-icon bg-info bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-spinner text-info fa-2x fa-spin"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100 card-hover">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="text-success mb-1">
                                    <i class="fas fa-check-circle fa-lg"></i>
                                </div>
                                <h3 class="mb-1 text-success fw-bold"><?php echo number_format($stats['completed_transfers']); ?></h3>
                                <p class="mb-0 text-muted small">مكتملة</p>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-success" style="width: <?php echo $stats['total_transfers'] > 0 ? ($stats['completed_transfers'] / $stats['total_transfers']) * 100 : 0; ?>%"></div>
                                </div>
                            </div>
                            <div class="stat-icon bg-success bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-check-circle text-success fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- بطاقات إضافية -->
        <div class="row mb-4">
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100 card-hover">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="text-danger mb-1">
                                    <i class="fas fa-times-circle fa-lg"></i>
                                </div>
                                <h3 class="mb-1 text-danger fw-bold"><?php echo number_format($stats['cancelled_transfers']); ?></h3>
                                <p class="mb-0 text-muted small">تحويلات ملغاة</p>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-danger" style="width: <?php echo $stats['total_transfers'] > 0 ? ($stats['cancelled_transfers'] / $stats['total_transfers']) * 100 : 0; ?>%"></div>
                                </div>
                            </div>
                            <div class="stat-icon bg-danger bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-times-circle text-danger fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100 card-hover">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="text-secondary mb-1">
                                    <i class="fas fa-boxes fa-lg"></i>
                                </div>
                                <h3 class="mb-1 text-secondary fw-bold"><?php echo number_format($stats['total_containers_moved']); ?></h3>
                                <p class="mb-0 text-muted small">حاويات منقولة</p>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-secondary" style="width: 85%"></div>
                                </div>
                            </div>
                            <div class="stat-icon bg-secondary bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-boxes text-secondary fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100 card-hover">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="text-dark mb-1">
                                    <i class="fas fa-dollar-sign fa-lg"></i>
                                </div>
                                <h3 class="mb-1 text-dark fw-bold"><?php echo number_format($stats['total_amount'] ?? 0); ?></h3>
                                <p class="mb-0 text-muted small">إجمالي القيمة (د.ع)</p>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-dark" style="width: 90%"></div>
                                </div>
                            </div>
                            <div class="stat-icon bg-dark bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-dollar-sign text-dark fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أدوات البحث والفلترة -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-light border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-filter me-2"></i>البحث والفلترة المتقدمة
                    </h5>
                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="collapse" data-bs-target="#filtersCollapse">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>
            <div class="collapse show" id="filtersCollapse">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <input type="hidden" name="page" value="transfers">

                        <div class="col-lg-3 col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-search text-primary me-1"></i>البحث العام
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control border-2" name="search"
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="رقم التحويل، اسم التاجر، السائق...">
                                <span class="input-group-text bg-primary text-white">
                                    <i class="fas fa-search"></i>
                                </span>
                            </div>
                        </div>

                        <div class="col-lg-2 col-md-3">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-traffic-light text-warning me-1"></i>الحالة
                            </label>
                            <select class="form-select border-2" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>
                                    <i class="fas fa-clock"></i> في الانتظار
                                </option>
                                <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>
                                    <i class="fas fa-spinner"></i> جاري التنفيذ
                                </option>
                                <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>
                                    <i class="fas fa-check"></i> مكتمل
                                </option>
                                <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>
                                    <i class="fas fa-times"></i> ملغى
                                </option>
                            </select>
                        </div>

                        <div class="col-lg-2 col-md-3">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-user-tie text-info me-1"></i>التاجر
                            </label>
                            <select class="form-select border-2" name="trader_id">
                                <option value="">جميع التجار</option>
                                <?php foreach ($traders as $trader): ?>
                                <option value="<?php echo $trader['id']; ?>" <?php echo $trader_filter == $trader['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($trader['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-lg-2 col-md-3">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-truck text-success me-1"></i>السائق
                            </label>
                            <select class="form-select border-2" name="driver_id">
                                <option value="">جميع السائقين</option>
                                <?php foreach ($drivers as $driver): ?>
                                <option value="<?php echo $driver['id']; ?>" <?php echo $driver_filter == $driver['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($driver['driver_name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-lg-2 col-md-3">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-calendar-alt text-secondary me-1"></i>من تاريخ
                            </label>
                            <input type="date" class="form-control border-2" name="date_from" value="<?php echo $date_from; ?>">
                        </div>

                        <div class="col-lg-2 col-md-3">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-calendar-check text-secondary me-1"></i>إلى تاريخ
                            </label>
                            <input type="date" class="form-control border-2" name="date_to" value="<?php echo $date_to; ?>">
                        </div>

                        <div class="col-lg-1 col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary shadow-sm">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                                <a href="index.php?page=transfers" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-redo me-1"></i>إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

    <!-- جدول التحويلات -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-gradient-light border-0">
            <div class="d-flex justify-content-between align-items-center flex-wrap gap-2">
                <div class="d-flex align-items-center">
                   
                    <div>
                        <h5 class="mb-0  fw-bold">قائمة التحويلات</h5>
                        <small class="">إجمالي <?php echo number_format($total_records); ?> تحويل</small>
                    </div>
                </div>

                <div class="d-flex align-items-center gap-2 flex-wrap">
                    <!-- زر طباعة التقرير -->
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-success btn-sm dropdown-toggle shadow-sm"
                                data-bs-toggle="dropdown" title="طباعة التقرير">
                            <i class="fas fa-print me-1"></i>طباعة التقرير
                        </button>
                        <ul class="dropdown-menu shadow">
                            <li>
                                <a class="dropdown-item" href="#" onclick="printCurrentReport()">
                                    <i class="fas fa-file-alt me-2 text-primary"></i>طباعة التقرير الحالي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="printCustomReport()">
                                    <i class="fas fa-calendar-alt me-2 text-info"></i>تقرير مخصص
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="printCurrentReport(true)">
                                    <i class="fas fa-print me-2 text-success"></i>طباعة مباشرة
                                </a>
                            </li>
                        </ul>
                    </div>

                    <?php if ($total_pages > 1): ?>
                    <div class="pagination-info bg-light rounded px-3 py-1">
                        <small class="text-muted">
                            <i class="fas fa-file-alt me-1"></i>
                            صفحة <?php echo $current_page; ?> من <?php echo $total_pages; ?>
                        </small>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="card-body p-0">
            <?php if (empty($transfers)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-truck text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">لا توجد تحويلات</h4>
                    <p class="text-muted">لم يتم العثور على تحويلات بالمعايير المحددة</p>
                    <a href="index.php?page=transfers&action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة أول تحويل
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>رقم التحويل</th>
                                <th>التاجر</th>
                                <th>السائق</th>
                                <th>التاريخ</th>
                                <th>الحاويات</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>الوقت</th>
                                <th width="120">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($transfers as $transfer): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                      
                                        <div>
                                            <h6 class="mb-0 font-monospace"><?php echo htmlspecialchars($transfer['transfer_number']); ?></h6>
                                            <small class="text-muted">
                                                من: <?php echo htmlspecialchars($transfer['pickup_location']); ?>
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                
                                <td>
                                    <div>
                                        <h6 class="mb-0"><?php echo htmlspecialchars($transfer['trader_name']); ?></h6>
                                        <?php if ($transfer['trader_phone']): ?>
                                        <small class="text-muted font-monospace" dir="ltr"><?php echo htmlspecialchars($transfer['trader_phone']); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                
                                <td>
                                    <div>
                                        <h6 class="mb-0"><?php echo htmlspecialchars($transfer['driver_name']); ?></h6>
                                        <div class="d-flex gap-2">
                                            <?php if ($transfer['driver_phone']): ?>
                                            <small class="text-muted font-monospace" dir="ltr"><?php echo htmlspecialchars($transfer['driver_phone']); ?></small>
                                            <?php endif; ?>
                                            <?php if ($transfer['vehicle_number']): ?>
                                            <small class="text-muted font-monospace"><?php echo htmlspecialchars($transfer['vehicle_number']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                
                                <td>
                                    <div>
                                        <?php echo convertDateFromMysql($transfer['transfer_date']); ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo convertDateFromMysql($transfer['created_at'], true); ?>
                                    </small>
                                </td>
                                
                                <td>
                                    <div class="text-center">
                                        <div class="fw-bold text-primary"><?php echo number_format($transfer['actual_containers']); ?></div>
                                        <small class="text-muted">حاوية</small>
                                    </div>
                                </td>
                                
                                <td>
                                    <div class="text-center">
                                        <div class="fw-bold text-success"><?php echo number_format($transfer['total_amount'], 0); ?></div>
                                        <small class="text-muted">د.ع</small>
                                    </div>
                                </td>
                                
                                <td>
                                    <?php
                                    $status_classes = [
                                        'pending' => 'bg-warning',
                                        'in_progress' => 'bg-info',
                                        'completed' => 'bg-success',
                                        'cancelled' => 'bg-danger'
                                    ];
                                    $status_texts = [
                                        'pending' => 'في الانتظار',
                                        'in_progress' => 'جاري التنفيذ',
                                        'completed' => 'مكتمل',
                                        'cancelled' => 'ملغى'
                                    ];
                                    ?>
                                    <span class="badge <?php echo $status_classes[$transfer['status']] ?? 'bg-secondary'; ?>">
                                        <?php echo $status_texts[$transfer['status']] ?? $transfer['status']; ?>
                                    </span>
                                </td>
                                
                                <td>
                                    <div class="small">
                                        <?php if ($transfer['pickup_time']): ?>
                                        <div class="text-info">
                                            <i class="fas fa-play me-1"></i>
                                            <?php echo date('H:i', strtotime($transfer['pickup_time'])); ?>
                                        </div>
                                        <?php endif; ?>
                                        <?php if ($transfer['delivery_time']): ?>
                                        <div class="text-success">
                                            <i class="fas fa-check me-1"></i>
                                            <?php echo date('H:i', strtotime($transfer['delivery_time'])); ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="index.php?page=transfers&action=view&id=<?php echo $transfer['id']; ?>"
                                           class="btn btn-outline-info" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>

                                        <div class="btn-group">
                                            <button type="button" class="btn btn-outline-success btn-sm dropdown-toggle"
                                                    data-bs-toggle="dropdown" title="طباعة">
                                                <i class="fas fa-print"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="index.php?page=transfer_print&id=<?php echo $transfer['id']; ?>" target="_blank">
                                                        <i class="fas fa-file-alt me-2"></i>طباعة مفصلة
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="index.php?page=transfer_print_simple&id=<?php echo $transfer['id']; ?>" target="_blank">
                                                        <i class="fas fa-receipt me-2"></i>طباعة مبسطة
                                                    </a>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item" href="index.php?page=transfer_print_simple&id=<?php echo $transfer['id']; ?>&auto_print=1" target="_blank">
                                                        <i class="fas fa-print me-2"></i>طباعة مباشرة
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>

                                        <?php if ($transfer['status'] !== 'completed'): ?>
                                        <a href="index.php?page=transfers&action=edit&id=<?php echo $transfer['id']; ?>"
                                           class="btn btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php endif; ?>
                                        
                                        <!-- تحديث الحالة -->
                                        <div class="btn-group">
                                            <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" title="تحديث الحالة">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <?php if ($transfer['status'] === 'pending'): ?>
                                                <li>
                                                    <button class="dropdown-item text-info" onclick="updateStatus(<?php echo $transfer['id']; ?>, 'in_progress', 'بدء التحويل')">
                                                        <i class="fas fa-play me-2"></i>بدء التحويل
                                                    </button>
                                                </li>
                                                <?php endif; ?>
                                                
                                                <?php if ($transfer['status'] === 'in_progress'): ?>
                                                <li>
                                                    <button class="dropdown-item text-success" onclick="updateStatus(<?php echo $transfer['id']; ?>, 'completed', 'إكمال التحويل')">
                                                        <i class="fas fa-check me-2"></i>إكمال التحويل
                                                    </button>
                                                </li>
                                                <?php endif; ?>
                                                
                                                <?php if (in_array($transfer['status'], ['pending', 'in_progress'])): ?>
                                                <li>
                                                    <button class="dropdown-item text-danger" onclick="updateStatus(<?php echo $transfer['id']; ?>, 'cancelled', 'إلغاء التحويل')">
                                                        <i class="fas fa-times me-2"></i>إلغاء التحويل
                                                    </button>
                                                </li>
                                                <?php endif; ?>
                                                
                                                <?php if ($transfer['status'] === 'pending'): ?>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <button class="dropdown-item text-danger" onclick="confirmDelete(<?php echo $transfer['id']; ?>, '<?php echo htmlspecialchars($transfer['transfer_number'], ENT_QUOTES); ?>')">
                                                        <i class="fas fa-trash me-2"></i>حذف
                                                    </button>
                                                </li>
                                                <?php endif; ?>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- الترقيم -->
        <?php if ($total_pages > 1): ?>
        <div class="card-footer">
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    <?php
                    $base_url = "index.php?page=transfers";
                    if (!empty($search)) $base_url .= "&search=" . urlencode($search);
                    if (!empty($status_filter)) $base_url .= "&status=" . urlencode($status_filter);
                    if (!empty($date_from)) $base_url .= "&date_from=" . urlencode($date_from);
                    if (!empty($date_to)) $base_url .= "&date_to=" . urlencode($date_to);
                    if ($trader_filter > 0) $base_url .= "&trader_id=$trader_filter";
                    if ($driver_filter > 0) $base_url .= "&driver_id=$driver_filter";
                    $base_url .= "&sort=$sort&order=" . strtolower($order);
                    ?>
                    
                    <!-- الصفحة الأولى -->
                    <li class="page-item <?php echo $current_page == 1 ? 'disabled' : ''; ?>">
                        <a class="page-link" href="<?php echo $base_url; ?>&page_num=1">الأولى</a>
                    </li>
                    
                    <!-- الصفحة السابقة -->
                    <li class="page-item <?php echo $current_page == 1 ? 'disabled' : ''; ?>">
                        <a class="page-link" href="<?php echo $base_url; ?>&page_num=<?php echo $current_page - 1; ?>">السابق</a>
                    </li>
                    
                    <!-- أرقام الصفحات -->
                    <?php
                    $start_page = max(1, $current_page - 2);
                    $end_page = min($total_pages, $current_page + 2);
                    
                    for ($i = $start_page; $i <= $end_page; $i++):
                    ?>
                    <li class="page-item <?php echo $i == $current_page ? 'active' : ''; ?>">
                        <a class="page-link" href="<?php echo $base_url; ?>&page_num=<?php echo $i; ?>"><?php echo $i; ?></a>
                    </li>
                    <?php endfor; ?>
                    
                    <!-- الصفحة التالية -->
                    <li class="page-item <?php echo $current_page == $total_pages ? 'disabled' : ''; ?>">
                        <a class="page-link" href="<?php echo $base_url; ?>&page_num=<?php echo $current_page + 1; ?>">التالي</a>
                    </li>
                    
                    <!-- الصفحة الأخيرة -->
                    <li class="page-item <?php echo $current_page == $total_pages ? 'disabled' : ''; ?>">
                        <a class="page-link" href="<?php echo $base_url; ?>&page_num=<?php echo $total_pages; ?>">الأخيرة</a>
                    </li>
                </ul>
            </nav>
        </div>
        <?php endif; ?>
        </div>
    </div>

    </div>
    <!-- /.container-fluid -->
</div>
<!-- /.content -->

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف التحويل <strong id="transferNumber"></strong>؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> سيتم حذف جميع الحاويات المرتبطة بهذا التحويل أيضاً.
                </div>
            </div>
            <div class="modal-footer">
                <form method="POST" id="deleteForm">
                    <input type="hidden" name="transfer_id" id="deleteTransferId">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="delete_transfer" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تحديث الحالة -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث حالة التحويل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="statusForm">
                <div class="modal-body">
                    <input type="hidden" name="transfer_id" id="statusTransferId">
                    <input type="hidden" name="new_status" id="statusNewStatus">
                    
                    <p>سيتم تحديث الحالة إلى: <strong id="statusText"></strong></p>
                    
                    <div class="mb-3">
                        <label for="status_notes" class="form-label">ملاحظات (اختيارية)</label>
                        <textarea class="form-control" id="status_notes" name="status_notes" rows="3" 
                                  placeholder="أضف أي ملاحظات حول التحديث..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="update_status" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>تحديث الحالة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function confirmDelete(transferId, transferNumber) {
    document.getElementById('deleteTransferId').value = transferId;
    document.getElementById('transferNumber').textContent = transferNumber;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

function updateStatus(transferId, newStatus, statusText) {
    document.getElementById('statusTransferId').value = transferId;
    document.getElementById('statusNewStatus').value = newStatus;
    document.getElementById('statusText').textContent = statusText;
    document.getElementById('status_notes').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('statusModal'));
    modal.show();
}

// تحديث الصفحة تلقائياً كل 2 دقيقة للحصول على آخر التحديثات
setTimeout(() => {
    window.location.reload();
}, 120000);
</script>

<style>
/* تحسينات التصميم لصفحة التحويلات */

/* Header Gradient */
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    position: relative;
    overflow: hidden;
}

.bg-gradient-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.page-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Cards Hover Effects */
.card-hover {
    transition: all 0.3s ease;
    cursor: pointer;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.stat-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.card-hover:hover .stat-icon {
    transform: scale(1.1);
}

/* Pulse Animation */
.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Enhanced Table */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: scale(1.01);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Avatar Improvements */
.avatar-sm {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, rgba(var(--bs-primary-rgb), 0.1) 0%, rgba(var(--bs-primary-rgb), 0.2) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(var(--bs-primary-rgb), 0.1);
}

/* Enhanced Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-group-sm > .btn, .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Enhanced Badges */
.badge {
    font-size: 0.75em;
    font-weight: 500;
    padding: 0.5em 0.75em;
    border-radius: 6px;
}

/* Form Enhancements */
.form-control, .form-select {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-1px);
}

.border-2 {
    border-width: 2px !important;
}

/* Progress Bars */
.progress {
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* Dropdown Enhancements */
.dropdown-menu {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.75rem 1.25rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: translateX(5px);
}

/* Pagination Info */
.pagination-info {
    border: 1px solid #dee2e6;
}

/* Empty State */
.empty-state {
    padding: 4rem 2rem;
    text-align: center;
}

.empty-state i {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .content-header {
        padding: 1rem 0;
    }

    .page-icon {
        width: 50px;
        height: 50px;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-group-sm > .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .avatar-sm {
        width: 35px;
        height: 35px;
    }

    .card-body {
        padding: 1rem;
    }

    h1 {
        font-size: 1.5rem;
    }

    h3 {
        font-size: 1.25rem;
    }
}

@media (max-width: 576px) {
    .d-flex.gap-2 {
        flex-direction: column;
    }

    .btn-lg {
        font-size: 1rem;
        padding: 0.75rem 1rem;
    }
}
</style>

<script>
// وظائف طباعة التقرير
function printCurrentReport(autoPrint = false) {
    // جمع معاملات البحث الحالية
    const urlParams = new URLSearchParams(window.location.search);
    const params = new URLSearchParams();

    // إضافة معاملات البحث الحالية
    if (urlParams.get('search')) params.set('search', urlParams.get('search'));
    if (urlParams.get('status_filter')) params.set('status', urlParams.get('status_filter'));
    if (urlParams.get('trader_filter')) params.set('trader_id', urlParams.get('trader_filter'));
    if (urlParams.get('date_from')) params.set('date_from', urlParams.get('date_from'));
    if (urlParams.get('date_to')) params.set('date_to', urlParams.get('date_to'));

    // إضافة معامل الطباعة التلقائية
    if (autoPrint) params.set('auto_print', '1');

    // فتح صفحة التقرير
    const reportUrl = `index.php?page=transfers_report_print&${params.toString()}`;
    window.open(reportUrl, '_blank');
}

function printCustomReport() {
    // إنشاء نموذج مخصص للتقرير
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تقرير مخصص</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="customReportForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" name="date_from" value="<?php echo date('Y-m-01'); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" name="date_to" value="<?php echo date('Y-m-d'); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الحالة</label>
                                    <select class="form-control" name="status">
                                        <option value="">جميع الحالات</option>
                                        <option value="pending">قيد الانتظار</option>
                                        <option value="in_progress">قيد التنفيذ</option>
                                        <option value="completed">مكتمل</option>
                                        <option value="cancelled">ملغى</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">التاجر</label>
                                    <select class="form-control" name="trader_id">
                                        <option value="">جميع التجار</option>
                                        <?php foreach ($traders as $trader): ?>
                                        <option value="<?php echo $trader['id']; ?>"><?php echo htmlspecialchars($trader['name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="generateCustomReport()">إنشاء التقرير</button>
                    <button type="button" class="btn btn-success" onclick="generateCustomReport(true)">طباعة مباشرة</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();

    // إزالة المودال عند الإغلاق
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

function generateCustomReport(autoPrint = false) {
    const form = document.getElementById('customReportForm');
    const formData = new FormData(form);
    const params = new URLSearchParams();

    // إضافة معاملات النموذج
    for (let [key, value] of formData.entries()) {
        if (value) params.set(key, value);
    }

    // إضافة معامل الطباعة التلقائية
    if (autoPrint) params.set('auto_print', '1');

    // فتح صفحة التقرير
    const reportUrl = `index.php?page=transfers_report_print&${params.toString()}`;
    window.open(reportUrl, '_blank');

    // إغلاق المودال
    const modal = bootstrap.Modal.getInstance(document.querySelector('.modal.show'));
    if (modal) modal.hide();
}

// تحسين أزرار الطباعة في الجدول
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات بصرية لأزرار الطباعة
    document.querySelectorAll('[title="طباعة"]').forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
});
</script>