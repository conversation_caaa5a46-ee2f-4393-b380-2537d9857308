# نظام البحث في القوائم المنسدلة (Searchable Select System)

## نظرة عامة

تم إضافة نظام شامل للبحث في جميع القوائم المنسدلة (dropdown lists) في النظام، مما يسهل على المستخدمين العثور على الخيارات المطلوبة بسرعة، خاصة عندما تحتوي القوائم على عدد كبير من العناصر.

## الميزات الرئيسية

### ✅ **البحث الفوري**
- بحث فوري أثناء الكتابة
- تمييز النتائج المطابقة
- دعم البحث بالأحرف العربية والإنجليزية

### ✅ **التنقل بالكيبورد**
- استخدام الأسهم للتنقل بين الخيارات
- Enter للاختيار
- Escape للإغلاق
- Tab للانتقال للحقل التالي

### ✅ **واجهة مستخدم محسنة**
- تصميم متجاوب يعمل على جميع الأجهزة
- تأثيرات بصرية جذابة
- دعم كامل للغة العربية (RTL)
- تأثيرات انتقال سلسة

### ✅ **سهولة الاستخدام**
- تطبيق تلقائي على جميع القوائم المنسدلة
- لا يتطلب تغييرات في الكود الموجود
- متوافق مع جميع المتصفحات الحديثة

## الملفات المضافة

### 1. **assets/js/searchable-select.js**
ملف JavaScript الرئيسي الذي يحتوي على:
- فئة `SearchableSelect` الأساسية
- مدير المكونات `SearchableSelectManager`
- دوال مساعدة عامة

### 2. **assets/css/searchable-select.css**
ملف CSS الذي يحتوي على:
- أنماط الواجهة الأساسية
- تأثيرات الانتقال والحركة
- دعم RTL والأجهزة المحمولة
- تحسينات الوصولية

### 3. **apply_searchable_select.php**
ملف PHP مساعد يحتوي على:
- دوال لتطبيق البحث على صفحات محددة
- دوال لإنشاء قوائم منسدلة جاهزة
- دوال لتحميل البيانات من قاعدة البيانات

## التطبيق في الصفحات

### 1. **صفحة إضافة الحاويات (containers_form.php)**

تم تحديث القوائم التالية لتصبح قابلة للبحث:

#### **قائمة الشركات:**
```html
<div class="searchable-select-wrapper">
    <select class="form-control searchable-select" 
            id="customer_id" name="customer_id" 
            data-placeholder="اختر الشركة أو ابحث..." required>
        <option value="">اختر الشركة</option>
        <?php foreach ($customers as $customer): ?>
            <option value="<?php echo $customer['id']; ?>">
                <?php echo htmlspecialchars($customer['name']); ?>
            </option>
        <?php endforeach; ?>
    </select>
</div>
```

#### **قائمة التجار:**
```html
<div class="searchable-select-wrapper">
    <select class="form-control searchable-select" 
            id="trader_id" name="trader_id" 
            data-placeholder="اختر التاجر أو ابحث..." required>
        <option value="">اختر التاجر</option>
        <?php foreach ($traders as $trader): ?>
            <option value="<?php echo $trader['id']; ?>">
                <?php echo htmlspecialchars($trader['name']); ?>
            </option>
        <?php endforeach; ?>
    </select>
</div>
```

#### **قائمة حجم الحاوية:**
```html
<div class="searchable-select-wrapper">
    <select class="form-control searchable-select" 
            id="container_type" name="container_type" 
            data-placeholder="اختر حجم الحاوية...">
        <option value="">اختر حجم الحاوية</option>
        <option value="20">20 قدم</option>
        <option value="40">40 قدم</option>
        <option value="45">45 قدم</option>
    </select>
</div>
```

#### **قائمة الحالة:**
```html
<div class="searchable-select-wrapper">
    <select class="form-control searchable-select" 
            id="status" name="status" 
            data-placeholder="اختر الحالة...">
        <option value="pending">قيد الانتظار</option>
        <option value="in_progress">قيد التنفيذ</option>
        <option value="completed">مكتمل</option>
        <option value="cancelled">ملغي</option>
    </select>
</div>
```

## كيفية إضافة البحث لصفحات جديدة

### الطريقة الأولى: تلقائياً
```php
<?php
// في بداية الصفحة
include 'apply_searchable_select.php';
?>
```

### الطريقة الثانية: يدوياً
```html
<!-- إضافة الكلاسات المطلوبة -->
<div class="searchable-select-wrapper">
    <select class="form-control searchable-select" 
            data-placeholder="ابحث...">
        <!-- الخيارات -->
    </select>
</div>
```

### الطريقة الثالثة: باستخدام الدوال المساعدة
```php
<?php
// إنشاء قائمة التجار
echo createTradersSelect($db, 'trader_id', $selectedTrader, [
    'class' => 'form-control',
    'required' => true,
    'placeholder' => 'اختر التاجر أو ابحث...'
]);

// إنشاء قائمة الشركات
echo createCustomersSelect($db, 'customer_id', $selectedCustomer, [
    'class' => 'form-control',
    'required' => true,
    'placeholder' => 'اختر الشركة أو ابحث...'
]);
?>
```

## الدوال المساعدة المتاحة

### 1. **applySearchableSelect($excludeIds, $includeOnly)**
تطبيق البحث على جميع القوائم في الصفحة

```php
// تطبيق على جميع القوائم
applySearchableSelect();

// استثناء قوائم محددة
applySearchableSelect(['status', 'type']);

// تطبيق على قوائم محددة فقط
applySearchableSelect([], ['customer_id', 'trader_id']);
```

### 2. **createSearchableSelect($name, $options, $selected, $attributes)**
إنشاء قائمة منسدلة قابلة للبحث

```php
$options = [
    '1' => 'الخيار الأول',
    '2' => 'الخيار الثاني',
    '3' => 'الخيار الثالث'
];

echo createSearchableSelect('my_field', $options, '2', [
    'class' => 'form-control',
    'required' => true,
    'placeholder' => 'اختر خياراً...'
]);
```

### 3. **loadSelectOptions($db, $table, $valueField, $textField, $whereClause, $params)**
تحميل الخيارات من قاعدة البيانات

```php
$options = loadSelectOptions(
    $db, 
    'customers', 
    'id', 
    'name', 
    'active = 1 AND city = ?', 
    ['Baghdad']
);
```

### 4. **دوال جاهزة للكيانات الأساسية:**
```php
// قائمة التجار
echo createTradersSelect($db, 'trader_id', $selected);

// قائمة الشركات
echo createCustomersSelect($db, 'customer_id', $selected);

// قائمة السائقين
echo createDriversSelect($db, 'driver_id', $selected);

// قائمة المستخدمين
echo createUsersSelect($db, 'user_id', $selected);
```

## التخصيص والتحكم

### 1. **تخصيص الأنماط**
```css
/* تخصيص لون الخيار المحدد */
.select-option.selected {
    background-color: #28a745;
    color: white;
}

/* تخصيص حقل البحث */
.select-search-input {
    border-color: #007bff;
    font-size: 16px;
}
```

### 2. **التحكم بـ JavaScript**
```javascript
// تهيئة عنصر محدد
SearchableSelect.init('#my-select');

// تدمير عنصر محدد
SearchableSelect.destroy('#my-select');

// إعادة تهيئة جميع العناصر
SearchableSelect.reinit();
```

### 3. **الأحداث المخصصة**
```javascript
// الاستماع لتغيير القيمة
document.getElementById('customer_id').addEventListener('change', function(e) {
    console.log('تم اختيار:', e.target.value);
});
```

## الاختبار والتحقق

### 1. **اختبار صفحة الحاويات:**
```
http://localhost/ccis_appis/index.php?page=containers&action=add
```

### 2. **التحقق من الميزات:**
- ✅ فتح القائمة بالنقر
- ✅ ظهور حقل البحث
- ✅ البحث الفوري أثناء الكتابة
- ✅ تمييز النتائج المطابقة
- ✅ التنقل بالأسهم
- ✅ الاختيار بـ Enter
- ✅ الإغلاق بـ Escape

### 3. **اختبار الأجهزة المحمولة:**
- ✅ عمل القوائم على الشاشات الصغيرة
- ✅ سهولة اللمس والتنقل
- ✅ ظهور لوحة المفاتيح المناسبة

## الصفحات المحدثة

### ✅ **تم التحديث:**
1. **containers_form.php** - قوائم الشركات والتجار وحجم الحاوية والحالة
2. **includes/header.php** - إضافة CSS
3. **includes/footer.php** - إضافة JavaScript

### 🔄 **قيد التحديث:**
1. **financial_add_new.php** - قوائم الشركات ونوع المستند وطريقة الدفع
2. **transfers_form.php** - قوائم التجار والسائقين والحالة
3. **penalties_form.php** - قوائم الشركات ونوع الغرامة
4. **users_form.php** - قائمة الأدوار
5. **settings.php** - جميع القوائم المنسدلة

## المزايا والفوائد

### 🚀 **تحسين تجربة المستخدم:**
- سرعة في العثور على الخيارات المطلوبة
- تقليل الأخطاء في الإدخال
- واجهة أكثر احترافية وحداثة

### 💡 **سهولة الصيانة:**
- كود منظم وقابل للإعادة الاستخدام
- تطبيق موحد عبر النظام
- سهولة إضافة ميزات جديدة

### 📱 **التوافق:**
- يعمل على جميع المتصفحات الحديثة
- متجاوب مع جميع أحجام الشاشات
- دعم كامل للمس في الأجهزة المحمولة

## الخطوات التالية

### 1. **تطبيق على باقي الصفحات:**
- صفحات المستندات المالية
- صفحات التحويلات
- صفحات الغرامات
- صفحات المستخدمين

### 2. **ميزات إضافية:**
- البحث المتقدم (بعدة معايير)
- حفظ الاختيارات الأخيرة
- اقتراحات ذكية
- تجميع الخيارات

### 3. **تحسينات الأداء:**
- تحميل البيانات بشكل تدريجي (lazy loading)
- تخزين مؤقت للبيانات
- ضغط الملفات

النظام الآن جاهز للاستخدام ويوفر تجربة بحث محسنة في جميع القوائم المنسدلة! 🎉
