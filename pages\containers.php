<?php
// التحقق من الصلاحيات
if (!hasPermission('containers')) {
    header('Location: index.php?page=unauthorized');
    exit;
}

// تحديد الإجراء
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$containerId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// معالجة الإجراءات
switch ($action) {
    case 'add':
    case 'edit':
        include 'pages/containers_form.php';
        break;

    case 'view':
        include 'pages/containers_view.php';
        break;

    case 'delete':
        // التحقق من وجود الحاوية
        if ($containerId > 0) {
            try {
                // التحقق من عدم وجود مستندات مالية مرتبطة بالحاوية
                $checkStmt = $db->prepare("SELECT COUNT(*) as count FROM financial_documents WHERE container_id = :id");
                $checkStmt->execute(['id' => $containerId]);
                $hasDocuments = $checkStmt->fetch()['count'] > 0;

                if ($hasDocuments) {
                    $_SESSION['error'] = 'لا يمكن حذف الحاوية لوجود مستندات مالية مرتبطة بها';
                } else {
                    // حذف الحاوية
                    $stmt = $db->prepare("DELETE FROM containers WHERE id = :id");
                    $stmt->execute(['id' => $containerId]);

                    // تسجيل النشاط
                    $activityStmt = $db->prepare("
                        INSERT INTO activity_log (user_id, activity_type, description, ip_address)
                        VALUES (:user_id, 'delete_container', :description, :ip_address)
                    ");
                    $activityStmt->execute([
                        'user_id' => getCurrentUserId(),
                        'description' => 'تم حذف الحاوية رقم: ' . $containerId,
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    ]);

                    $_SESSION['success'] = 'تم حذف الحاوية بنجاح';
                }
            } catch (PDOException $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء حذف الحاوية: ' . $e->getMessage();
            }
        }

        // إعادة التوجيه إلى قائمة الحاويات
        echo '<script>
    setTimeout(function() {
        window.location.href = "index.php?page=containers";
    }, 1000); 
</script>';
        exit;
        break;

    case 'list':
    default:
        // استعلام البحث
        $searchTerm = isset($_GET['search']) ? trim($_GET['search']) : '';
        $statusFilter = isset($_GET['status']) ? $_GET['status'] : '';
        $customerFilter = isset($_GET['customer_id']) ? (int)$_GET['customer_id'] : 0;
        $traderName = isset($_GET['trader_name']) ? trim($_GET['trader_name']) : '';
        $dateFrom = isset($_GET['date_from']) ? $_GET['date_from'] : '';
        $dateTo = isset($_GET['date_to']) ? $_GET['date_to'] : '';

        // بناء استعلام البحث
        $query = "
            SELECT c.*,
                   cu.name as customer_name
            FROM containers c
            JOIN customers cu ON c.customer_id = cu.id
            WHERE 1=1
        ";
        $params = [];

        // إضافة شرط البحث
        if (!empty($searchTerm)) {
            $query .= " AND (c.container_number LIKE :search_container OR cu.name LIKE :search_customer)";
            $params['search_container'] = "%$searchTerm%";
            $params['search_customer'] = "%$searchTerm%";
        }

        // إضافة شرط الحالة
        if (!empty($statusFilter)) {
            $query .= " AND c.status = :status";
            $params['status'] = $statusFilter;
        }

        // إضافة شرط الشركة
        if ($customerFilter > 0) {
            $query .= " AND c.customer_id = :customer_id";
            $params['customer_id'] = $customerFilter;
        }

        // إضافة شرط اسم التاجر
        if (!empty($traderName)) {
            $query .= " AND c.trader_name LIKE :trader_name";
            $params['trader_name'] = "%$traderName%";
        }

        // إضافة شرط تاريخ الاستلام (من)
        if (!empty($dateFrom)) {
            $query .= " AND c.entry_date >= :date_from";
            $params['date_from'] = $dateFrom;
        }

        // إضافة شرط تاريخ الاستلام (إلى)
        if (!empty($dateTo)) {
            $query .= " AND c.entry_date <= :date_to";
            $params['date_to'] = $dateTo;
        }

        $query .= " ORDER BY c.created_at DESC";

        try {
            // تنفيذ الاستعلام
            $stmt = $db->prepare($query);
            $stmt->execute($params);
            $containers = $stmt->fetchAll();

            // الحصول على قائمة الشركات للفلتر
            $customersStmt = $db->query("SELECT id, name FROM customers ORDER BY name");
            $customers = $customersStmt->fetchAll();
        } catch (PDOException $e) {
            $_SESSION['error'] = 'حدث خطأ أثناء استرجاع بيانات الحاويات: ' . $e->getMessage();
            $containers = [];
            $customers = [];
        }

        // عرض قائمة الحاويات
        ?>
        <!-- Content Header (Page header) -->
        <div class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1 class="m-0">
                            <i class="fas fa-shipping-fast text-primary"></i>
                            إدارة الحاويات
                        </h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                            <li class="breadcrumb-item active">إدارة الحاويات</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <!-- Action Buttons -->
                <div class="row mb-3">
                    <div class="col-12">
                        <a href="index.php?page=containers&action=add" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus-circle"></i> إضافة حاوية جديدة
                        </a>
                        <a href="index.php?page=reports&type=regulatory" class="btn btn-info">
                            <i class="fas fa-chart-line"></i> تقارير الحاويات
                        </a>
                        <button type="button" class="btn btn-secondary" onclick="window.print()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <?php
                    // حساب الإحصائيات
                    $totalContainers = count($containers);
                    $pendingContainers = count(array_filter($containers, function($c) { return $c['status'] == 'pending'; }));
                    $inProgressContainers = count(array_filter($containers, function($c) { return $c['status'] == 'in_progress'; }));
                    $completedContainers = count(array_filter($containers, function($c) { return $c['status'] == 'completed'; }));
                    ?>

                    <div class="col-lg-3 col-6">
                        <div class="small-box bg-info">
                            <div class="inner">
                                <h3><?php echo $totalContainers; ?></h3>
                                <p>إجمالي الحاويات</p>
                            </div>
                            <div class="icon">
                                <i class="fas fa-shipping-fast"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-6">
                        <div class="small-box bg-warning">
                            <div class="inner">
                                <h3><?php echo $pendingContainers; ?></h3>
                                <p>قيد الانتظار</p>
                            </div>
                            <div class="icon">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-6">
                        <div class="small-box bg-primary">
                            <div class="inner">
                                <h3><?php echo $inProgressContainers; ?></h3>
                                <p>قيد التنفيذ</p>
                            </div>
                            <div class="icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-6">
                        <div class="small-box bg-success">
                            <div class="inner">
                                <h3><?php echo $completedContainers; ?></h3>
                                <p>مكتملة</p>
                            </div>
                            <div class="icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        <h5><i class="icon fas fa-check"></i> تم بنجاح!</h5>
                        <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        <h5><i class="icon fas fa-ban"></i> حدث خطأ!</h5>
                        <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                    </div>
                <?php endif; ?>

                <!-- بحث وتصفية -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-search"></i>
                            بحث وتصفية
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <form method="get" action="index.php">
                            <input type="hidden" name="page" value="containers">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="search">
                                            <i class="fas fa-search text-muted"></i>
                                            بحث عام
                                        </label>
                                        <input type="text" class="form-control" id="search" name="search"
                                               value="<?php echo htmlspecialchars($searchTerm); ?>"
                                               placeholder="رقم الحاوية أو اسم الشركة">
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="trader_name">
                                            <i class="fas fa-user-tie text-muted"></i>
                                            اسم التاجر
                                        </label>
                                        <input type="text" class="form-control" id="trader_name" name="trader_name"
                                               value="<?php echo htmlspecialchars($traderName); ?>"
                                               placeholder="ادخل اسم التاجر">
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="status">
                                            <i class="fas fa-flag text-muted"></i>
                                            الحالة
                                        </label>
                                        <div class="searchable-select-wrapper">
                                            <select class="form-control searchable-select" id="status" name="status" data-placeholder="اختر الحالة...">
                                                <option value="">جميع الحالات</option>
                                                <option value="pending" <?php echo $statusFilter == 'pending' ? 'selected' : ''; ?>>قيد الانتظار</option>
                                                <option value="in_progress" <?php echo $statusFilter == 'in_progress' ? 'selected' : ''; ?>>قيد التنفيذ</option>
                                                <option value="completed" <?php echo $statusFilter == 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                                                <option value="cancelled" <?php echo $statusFilter == 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="customer_id">
                                            <i class="fas fa-user text-muted"></i>
                                            الشركة
                                        </label>
                                        <div class="searchable-select-wrapper">
                                            <select class="form-control searchable-select" id="customer_id" name="customer_id" data-placeholder="اختر الشركة أو ابحث...">
                                                <option value="">جميع الشركات</option>
                                                <?php if (!empty($customers)): ?>
                                                    <?php foreach ($customers as $customer): ?>
                                                        <option value="<?php echo $customer['id']; ?>" <?php echo $customerFilter == $customer['id'] ? 'selected' : ''; ?>>
                                                            <?php echo htmlspecialchars($customer['name']); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- صف ثاني للتواريخ والأزرار -->
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="date_from">
                                            <i class="fas fa-calendar-alt text-muted"></i>
                                            تاريخ الاستلام من
                                        </label>
                                        <input type="date" class="form-control" id="date_from" name="date_from"
                                               value="<?php echo htmlspecialchars($dateFrom); ?>">
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="date_to">
                                            <i class="fas fa-calendar-alt text-muted"></i>
                                            تاريخ الاستلام إلى
                                        </label>
                                        <input type="date" class="form-control" id="date_to" name="date_to"
                                               value="<?php echo htmlspecialchars($dateTo); ?>">
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <div>
                                            <button type="submit" class="btn btn-primary btn-block">
                                                <i class="fas fa-search"></i> بحث وتصفية
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <div>
                                            <a href="index.php?page=containers" class="btn btn-secondary btn-block">
                                                <i class="fas fa-undo"></i> إعادة تعيين
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- قائمة الحاويات -->
                <div class="card card-info card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list"></i>
                            قائمة الحاويات
                        </h3>
                        <div class="card-tools">
                            <a href="index.php?page=containers&action=add" class="btn btn-success btn-sm">
                                <i class="fas fa-plus"></i> إضافة حاوية جديدة
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped mb-0">
                                <thead class="bg-light">
                                    <tr>
                                        <th style="width: 50px">#</th>
                                        <th>
                                            <i class="fas fa-barcode text-muted"></i>
                                            رقم الحاوية
                                        </th>
                                        <th>
                                            <i class="fas fa-user text-muted"></i>
                                            الشركة
                                        </th>
                                        <th>
                                            <i class="fas fa-calendar-plus text-muted"></i>
                                            تاريخ الاستلام
                                        </th>
                                        <th>
                                            <i class="fas fa-calendar-minus text-muted"></i>
                                            تاريخ الخروج
                                        </th>
                                        <th>
                                            <i class="fas fa-money-bill-wave text-muted"></i>
                                            سعر الكلفة
                                        </th>
                                        <th>
                                            <i class="fas fa-hand-holding-usd text-muted"></i>
                                            سعر البيع
                                        </th>
                                        <th>
                                            <i class="fas fa-user-tie text-muted"></i>
                                            التاجر
                                        </th>
                                        <th>
                                            <i class="fas fa-flag text-muted"></i>
                                            الحالة
                                        </th>
                                        <th style="width: 120px">
                                            <i class="fas fa-cogs text-muted"></i>
                                            الإجراءات
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($containers)): ?>
                                        <tr>
                                            <td colspan="10" class="text-center py-5">
                                                <div class="text-muted">
                                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                                    <h5>لا توجد حاويات</h5>
                                                    <p>لم يتم العثور على أي حاويات مطابقة للبحث</p>
                                                    <a href="index.php?page=containers&action=add" class="btn btn-primary">
                                                        <i class="fas fa-plus"></i> إضافة حاوية جديدة
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($containers as $index => $container): ?>
                                            <tr>
                                                <td>
                                                    <span class="badge badge-secondary"><?php echo $index + 1; ?></span>
                                                </td>
                                                <td>
                                                    <strong class="text-primary"><?php echo htmlspecialchars($container['container_number']); ?></strong>
                                                </td>
                                                <td>
                                                    <i class="fas fa-user-circle text-muted mr-1"></i>
                                                    <?php echo htmlspecialchars($container['customer_name']); ?>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar mr-1"></i>
                                                        <?php echo date('d/m/Y', strtotime($container['entry_date'])); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar mr-1"></i>
                                                        <?php echo $container['exit_date'] ? date('d/m/Y', strtotime($container['exit_date'])) : '-'; ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <span class="badge badge-success">
                                                        <?php echo isset($container['purchase_price']) && $container['purchase_price'] > 0 ? number_format($container['purchase_price'], 0) . ' د.ع' : '-'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge badge-primary">
                                                        <?php echo isset($container['selling_price']) && $container['selling_price'] > 0 ? number_format($container['selling_price'], 0) . ' د.ع' : '-'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <i class="fas fa-user-tie mr-1"></i>
                                                        <?php echo isset($container['trader_name']) && !empty($container['trader_name']) ? htmlspecialchars($container['trader_name']) : '-'; ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusClass = '';
                                                    $statusText = '';
                                                    $statusIcon = '';

                                                    switch ($container['status']) {
                                                        case 'pending':
                                                            $statusClass = 'warning';
                                                            $statusText = 'قيد الانتظار';
                                                            $statusIcon = 'clock';
                                                            break;
                                                        case 'in_progress':
                                                            $statusClass = 'info';
                                                            $statusText = 'قيد التنفيذ';
                                                            $statusIcon = 'cogs';
                                                            break;
                                                        case 'completed':
                                                            $statusClass = 'success';
                                                            $statusText = 'مكتمل';
                                                            $statusIcon = 'check-circle';
                                                            break;
                                                        case 'cancelled':
                                                            $statusClass = 'danger';
                                                            $statusText = 'ملغي';
                                                            $statusIcon = 'times-circle';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge badge-<?php echo $statusClass; ?>">
                                                        <i class="fas fa-<?php echo $statusIcon; ?>"></i>
                                                        <?php echo $statusText; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="index.php?page=containers&action=view&id=<?php echo $container['id']; ?>"
                                                           class="btn btn-info btn-sm" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="index.php?page=containers&action=edit&id=<?php echo $container['id']; ?>"
                                                           class="btn btn-primary btn-sm" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="index.php?page=containers&action=delete&id=<?php echo $container['id']; ?>"
                                                           class="btn btn-danger btn-sm" title="حذف"
                                                           onclick="return confirmDelete(event, 'هل أنت متأكد من حذف هذه الحاوية؟')">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <?php if (!empty($containers)): ?>
                    <div class="card-footer clearfix">
                        <div class="float-right">
                            <small class="text-muted">
                                إجمالي الحاويات: <strong><?php echo count($containers); ?></strong>
                            </small>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

            </div>
        </section>
        <?php
        break;
}
?>

<script>
// إخفاء الإشعارات تلقائياً بعد 5 ثوان
$(document).ready(function() {
    // إخفاء إشعارات النجاح بعد 5 ثوان
    setTimeout(function() {
        $('.alert-success').fadeOut('slow');
    }, 5000);

    // إخفاء إشعارات الخطأ بعد 8 ثوان
    setTimeout(function() {
        $('.alert-danger').fadeOut('slow');
    }, 8000);
});
</script>