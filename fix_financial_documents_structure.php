<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح هيكل جدول المستندات المالية</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { color: blue; background: #d1ecf1; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .warning { color: orange; background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>إصلاح هيكل جدول المستندات المالية</h1>
    
    <?php
    require_once 'config/config.php';
    
    try {
        $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='step'>";
        echo "<h3>الخطوة 1: فحص القيود الخارجية الحالية</h3>";
        
        // فحص القيود الخارجية
        $stmt = $db->query("
            SELECT 
                CONSTRAINT_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_NAME = 'financial_documents' 
            AND TABLE_SCHEMA = '" . DB_NAME . "'
            AND REFERENCED_TABLE_NAME IS NOT NULL
            AND COLUMN_NAME = 'customer_id'
        ");
        $constraints = $stmt->fetchAll();
        
        $currentReference = null;
        if (count($constraints) > 0) {
            $currentReference = $constraints[0]['REFERENCED_TABLE_NAME'];
            echo "<div class='info'>القيد الخارجي الحالي: customer_id يشير إلى جدول $currentReference</div>";
        } else {
            echo "<div class='warning'>لا يوجد قيد خارجي لعمود customer_id</div>";
        }
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h3>الخطوة 2: فحص الجداول المتاحة</h3>";
        
        // فحص وجود جدول traders
        $stmt = $db->query("SHOW TABLES LIKE 'traders'");
        $tradersExists = $stmt->rowCount() > 0;
        
        // فحص وجود جدول customers
        $stmt = $db->query("SHOW TABLES LIKE 'customers'");
        $customersExists = $stmt->rowCount() > 0;
        
        if ($tradersExists) {
            echo "<div class='success'>✓ جدول traders موجود</div>";
            
            // عدد التجار
            $stmt = $db->query("SELECT COUNT(*) as count FROM traders");
            $tradersCount = $stmt->fetch()['count'];
            echo "<div class='info'>عدد التجار: $tradersCount</div>";
        } else {
            echo "<div class='error'>✗ جدول traders غير موجود</div>";
        }
        
        if ($customersExists) {
            echo "<div class='success'>✓ جدول customers موجود</div>";
            
            // عدد العملاء
            $stmt = $db->query("SELECT COUNT(*) as count FROM customers");
            $customersCount = $stmt->fetch()['count'];
            echo "<div class='info'>عدد العملاء: $customersCount</div>";
        } else {
            echo "<div class='error'>✗ جدول customers غير موجود</div>";
        }
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h3>الخطوة 3: تحديث القيد الخارجي</h3>";
        
        if ($tradersExists && $currentReference !== 'traders') {
            echo "<div class='info'>سيتم تحديث القيد الخارجي ليشير إلى جدول traders</div>";
            
            try {
                // إزالة القيد الخارجي الحالي إذا كان موجوداً
                if (count($constraints) > 0) {
                    $constraintName = $constraints[0]['CONSTRAINT_NAME'];
                    echo "<div class='info'>إزالة القيد الخارجي: $constraintName</div>";
                    $db->exec("ALTER TABLE financial_documents DROP FOREIGN KEY $constraintName");
                    echo "<div class='success'>✓ تم إزالة القيد الخارجي القديم</div>";
                }
                
                // إضافة القيد الخارجي الجديد
                echo "<div class='info'>إضافة قيد خارجي جديد يشير إلى جدول traders</div>";
                $db->exec("
                    ALTER TABLE financial_documents 
                    ADD CONSTRAINT financial_documents_ibfk_1 
                    FOREIGN KEY (customer_id) REFERENCES traders(id) ON DELETE SET NULL
                ");
                echo "<div class='success'>✓ تم إضافة القيد الخارجي الجديد</div>";
                
            } catch (PDOException $e) {
                echo "<div class='error'>خطأ في تحديث القيد الخارجي: " . $e->getMessage() . "</div>";
            }
        } elseif ($currentReference === 'traders') {
            echo "<div class='success'>✓ القيد الخارجي يشير بالفعل إلى جدول traders</div>";
        } else {
            echo "<div class='warning'>لا يمكن تحديث القيد الخارجي - جدول traders غير موجود</div>";
        }
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h3>الخطوة 4: فحص البيانات</h3>";
        
        // فحص المستندات المالية
        $stmt = $db->query("SELECT COUNT(*) as count FROM financial_documents");
        $documentsCount = $stmt->fetch()['count'];
        echo "<div class='info'>عدد المستندات المالية: $documentsCount</div>";
        
        // فحص المستندات المرتبطة بالتجار
        $stmt = $db->query("
            SELECT COUNT(*) as count 
            FROM financial_documents fd
            JOIN traders t ON fd.customer_id = t.id
        ");
        $linkedDocuments = $stmt->fetch()['count'];
        echo "<div class='info'>المستندات المرتبطة بالتجار: $linkedDocuments</div>";
        
        // فحص المستندات غير المرتبطة
        $stmt = $db->query("SELECT COUNT(*) as count FROM financial_documents WHERE customer_id IS NULL");
        $unlinkedDocuments = $stmt->fetch()['count'];
        echo "<div class='info'>المستندات غير المرتبطة: $unlinkedDocuments</div>";
        
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h3>الخطوة 5: اختبار جلب الحاويات</h3>";
        
        if ($tradersExists) {
            // جلب أول تاجر للاختبار
            $stmt = $db->query("SELECT id, name FROM traders ORDER BY id LIMIT 1");
            $testTrader = $stmt->fetch();
            
            if ($testTrader) {
                echo "<div class='info'>اختبار مع التاجر: " . htmlspecialchars($testTrader['name']) . " (ID: " . $testTrader['id'] . ")</div>";
                
                // اختبار جلب الحاويات
                $stmt = $db->prepare("
                    SELECT COUNT(*) as count 
                    FROM containers 
                    WHERE trader_id = ? AND status != 'cancelled'
                ");
                $stmt->execute([$testTrader['id']]);
                $containersCount = $stmt->fetch()['count'];
                
                echo "<div class='info'>عدد الحاويات المتاحة للتاجر: $containersCount</div>";
                
                if ($containersCount > 0) {
                    echo "<div class='success'>✓ يمكن جلب الحاويات للتاجر</div>";
                    
                    // عرض بعض الحاويات
                    $stmt = $db->prepare("
                        SELECT id, container_number, content, status 
                        FROM containers 
                        WHERE trader_id = ? AND status != 'cancelled'
                        ORDER BY entry_date DESC
                        LIMIT 3
                    ");
                    $stmt->execute([$testTrader['id']]);
                    $containers = $stmt->fetchAll();
                    
                    echo "<div class='info'>أمثلة على الحاويات:</div>";
                    echo "<ul>";
                    foreach ($containers as $container) {
                        echo "<li>" . htmlspecialchars($container['container_number']) . " - " . htmlspecialchars($container['content'] ?? 'غير محدد') . " (" . $container['status'] . ")</li>";
                    }
                    echo "</ul>";
                } else {
                    echo "<div class='warning'>لا توجد حاويات للتاجر المحدد</div>";
                }
            } else {
                echo "<div class='warning'>لا يوجد تجار في النظام</div>";
            }
        }
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h3>✅ ملخص الإصلاح</h3>";
        echo "<div class='success'>تم إصلاح هيكل جدول المستندات المالية بنجاح!</div>";
        echo "<div class='info'>";
        echo "<strong>التحديثات المطبقة:</strong><br>";
        echo "• تحديث القيد الخارجي ليشير إلى جدول traders<br>";
        echo "• فحص البيانات والتأكد من سلامتها<br>";
        echo "• اختبار جلب الحاويات<br>";
        echo "</div>";
        
        echo "<div style='margin-top: 20px;'>";
        echo "<a href='test_financial_containers.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار جلب الحاويات</a>";
        echo "<a href='index.php?page=financial&action=add_new&type=receipt' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار إضافة مستند</a>";
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
    }
    ?>
</body>
</html>
