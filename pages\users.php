<?php
// التحقق من الصلاحيات
if (!hasPermission('users')) {
    echo '<script>window.location.href = "index.php?page=unauthorized";</script>';
    return;
}

// تحديد الإجراء
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$userId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// معالجة الإجراءات
switch ($action) {
    case 'add':
    case 'edit':
        include 'pages/users_form.php';
        break;

    case 'view':
        include 'pages/users_view.php';
        break;

    case 'delete':
        // التحقق من وجود المستخدم
        if ($userId > 0) {
            try {
                // التحقق من عدم حذف المستخدم الحالي
                if ($userId == getCurrentUserId()) {
                    $_SESSION['error'] = 'لا يمكن حذف المستخدم الحالي';
                } else {
                    // التحقق من عدم حذف مدير النظام الرئيسي
                    $checkAdminStmt = $db->prepare("SELECT role_id FROM users WHERE id = :id");
                    $checkAdminStmt->execute(['id' => $userId]);
                    $userRole = $checkAdminStmt->fetch()['role_id'];

                    if ($userRole == 1 && $userId == 1) {
                        $_SESSION['error'] = 'لا يمكن حذف مدير النظام الرئيسي';
                    } else {
                        // حذف المستخدم
                        $stmt = $db->prepare("DELETE FROM users WHERE id = :id");
                        $stmt->execute(['id' => $userId]);

                        // تسجيل النشاط
                        $activityStmt = $db->prepare("
                            INSERT INTO activity_log (user_id, activity_type, description, ip_address)
                            VALUES (:user_id, 'delete_user', :description, :ip_address)
                        ");
                        $activityStmt->execute([
                            'user_id' => getCurrentUserId(),
                            'description' => 'تم حذف المستخدم رقم: ' . $userId,
                            'ip_address' => $_SERVER['REMOTE_ADDR']
                        ]);

                        $_SESSION['success'] = 'تم حذف المستخدم بنجاح';
                    }
                }
            } catch (PDOException $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء حذف المستخدم: ' . $e->getMessage();
            }
        }

        // إعادة التوجيه إلى قائمة المستخدمين
        echo '<script>window.location.href = "index.php?page=users";</script>';
        return;
        break;

    case 'list':
    default:
        // استعلام البحث
        $searchTerm = isset($_GET['search']) ? $_GET['search'] : '';
        $roleFilter = isset($_GET['role_id']) ? (int)$_GET['role_id'] : 0;
        $statusFilter = isset($_GET['status']) ? (int)$_GET['status'] : -1;

        // بناء استعلام البحث
        $query = "
            SELECT u.*, r.name as role_name
            FROM users u
            JOIN roles r ON u.role_id = r.id
            WHERE 1=1
        ";
        $params = [];

        if (!empty($searchTerm)) {
            $query .= " AND (u.username LIKE :search OR u.full_name LIKE :search OR u.email LIKE :search)";
            $params['search'] = "%$searchTerm%";
        }

        if ($roleFilter > 0) {
            $query .= " AND u.role_id = :role_id";
            $params['role_id'] = $roleFilter;
        }

        if ($statusFilter !== -1) {
            $query .= " AND u.active = :status";
            $params['status'] = $statusFilter;
        }

        $query .= " ORDER BY u.username";

        try {
            // تنفيذ الاستعلام
            $stmt = $db->prepare($query);
            $stmt->execute($params);
            $users = $stmt->fetchAll();

            // الحصول على قائمة الأدوار للفلتر
            $rolesStmt = $db->query("SELECT id, name FROM roles ORDER BY id");
            $roles = $rolesStmt->fetchAll();
        } catch (PDOException $e) {
            $_SESSION['error'] = 'حدث خطأ أثناء استرجاع بيانات المستخدمين: ' . $e->getMessage();
            $users = [];
            $roles = [];
        }

        // عرض قائمة المستخدمين
        ?>
        <!-- Content Header (Page header) -->
        <div class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1 class="m-0">
                            <i class="fas fa-users text-primary"></i>
                            إدارة المستخدمين
                        </h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                            <li class="breadcrumb-item active">إدارة المستخدمين</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">

                <!-- Action Buttons -->
                <div class="row mb-3">
                    <div class="col-12">
                        <a href="index.php?page=users&action=add" class="btn btn-primary">
                            <i class="fas fa-plus-circle"></i> إضافة مستخدم جديد
                        </a>
                        <button type="button" class="btn btn-secondary" onclick="window.print()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <a href="index.php?page=settings&action=roles" class="btn btn-info">
                            <i class="fas fa-user-cog"></i> إدارة الأدوار
                        </a>
                    </div>
                </div>

        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
            </div>
        <?php endif; ?>

                <!-- Search and Filter Card -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-filter"></i> بحث وتصفية المستخدمين
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <form method="get" action="index.php">
                            <input type="hidden" name="page" value="users">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="search">
                                            <i class="fas fa-search text-primary"></i> البحث العام
                                        </label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                            </div>
                                            <input type="text" class="form-control" id="search" name="search"
                                                   value="<?php echo htmlspecialchars($searchTerm); ?>"
                                                   placeholder="اسم المستخدم، الاسم الكامل، البريد الإلكتروني">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="role_id">
                                            <i class="fas fa-user-tag text-info"></i> الدور الوظيفي
                                        </label>
                                        <div class="searchable-select-wrapper">
                                            <select class="form-control searchable-select" id="role_id" name="role_id" data-placeholder="اختر الدور...">
                                                <option value="">جميع الأدوار</option>
                                                <?php foreach ($roles as $role): ?>
                                                    <option value="<?php echo $role['id']; ?>" <?php echo $roleFilter == $role['id'] ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($role['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="status">
                                            <i class="fas fa-toggle-on text-success"></i> حالة النشاط
                                        </label>
                                        <div class="searchable-select-wrapper">
                                            <select class="form-control searchable-select" id="status" name="status" data-placeholder="اختر الحالة...">
                                                <option value="-1">جميع الحالات</option>
                                                <option value="1" <?php echo $statusFilter === 1 ? 'selected' : ''; ?>>نشط</option>
                                                <option value="0" <?php echo $statusFilter === 0 ? 'selected' : ''; ?>>غير نشط</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <div class="d-flex flex-column">
                                            <button type="submit" class="btn btn-primary mb-2">
                                                <i class="fas fa-search"></i> بحث
                                            </button>
                                            <a href="index.php?page=users" class="btn btn-default">
                                                <i class="fas fa-redo"></i> إعادة تعيين
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list"></i> قائمة المستخدمين
                        </h3>
                        <div class="card-tools">
                            <span class="badge badge-primary">إجمالي: <?php echo count($users); ?> مستخدم</span>
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button type="button" class="btn btn-tool" data-card-widget="maximize">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th style="width: 50px">#</th>
                                    <th>اسم المستخدم</th>
                                    <th>الاسم الكامل</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>رقم الهاتف</th>
                                    <th>الدور الوظيفي</th>
                                    <th>آخر تسجيل دخول</th>
                                    <th>الحالة</th>
                                    <th style="width: 150px">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($users)): ?>
                                    <tr>
                                        <td colspan="9" class="text-center py-4">
                                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                            <br>
                                            <strong>لا يوجد مستخدمين</strong>
                                            <br>
                                            <small class="text-muted">لم يتم العثور على أي مستخدمين مطابقين لمعايير البحث</small>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($users as $index => $user): ?>
                                        <tr>
                                            <td><?php echo $index + 1; ?></td>
                                            <td>
                                                <strong class="text-primary"><?php echo htmlspecialchars($user['username']); ?></strong>
                                                <?php if ($user['id'] == getCurrentUserId()): ?>
                                                    <span class="badge badge-info badge-sm ml-1">أنت</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <i class="fas fa-user-circle text-muted mr-1"></i>
                                                <?php echo htmlspecialchars($user['full_name']); ?>
                                            </td>
                                            <td>
                                                <i class="fas fa-envelope text-muted mr-1"></i>
                                                <a href="mailto:<?php echo htmlspecialchars($user['email']); ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($user['email']); ?>
                                                </a>
                                            </td>
                                            <td>
                                                <i class="fas fa-phone text-muted mr-1"></i>
                                                <a href="tel:<?php echo htmlspecialchars($user['phone']); ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($user['phone']); ?>
                                                </a>
                                            </td>
                                            <td>
                                                <span class="badge badge-info">
                                                    <i class="fas fa-user-tag mr-1"></i>
                                                    <?php echo htmlspecialchars($user['role_name']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($user['last_login']): ?>
                                                    <small class="text-muted">
                                                        <i class="fas fa-clock mr-1"></i>
                                                        <?php echo date('d/m/Y H:i', strtotime($user['last_login'])); ?>
                                                    </small>
                                                <?php else: ?>
                                                    <span class="text-muted">لم يسجل دخول</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($user['active']): ?>
                                                    <span class="badge badge-success">
                                                        <i class="fas fa-check-circle"></i> نشط
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge badge-danger">
                                                        <i class="fas fa-times-circle"></i> غير نشط
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="index.php?page=users&action=view&id=<?php echo $user['id']; ?>"
                                                       class="btn btn-info btn-sm" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="index.php?page=users&action=edit&id=<?php echo $user['id']; ?>"
                                                       class="btn btn-primary btn-sm" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($user['id'] != getCurrentUserId() && !($user['role_id'] == 1 && $user['id'] == 1)): ?>
                                                    <a href="index.php?page=users&action=delete&id=<?php echo $user['id']; ?>"
                                                       class="btn btn-danger btn-sm" title="حذف"
                                                       onclick="return confirmDelete(event, 'هل أنت متأكد من حذف هذا المستخدم؟')">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
            <!-- /.container-fluid -->
        </section>
        <!-- /.content -->
        <?php
        break;
}
?>
