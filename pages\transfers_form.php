<?php
// التحقق من الصلاحيات
if (!hasPermission('transfers')) {
    header('Location: index.php?page=unauthorized');
    exit;
}

// متغيرات النموذج
$transfer = [
    'id' => 0,
    'transfer_number' => '',
    'transfer_date' => date('Y-m-d'),
    'trader_id' => '',
    'driver_id' => '',
    'total_amount' => 0,
    'status' => 'pending',
    'notes' => '',
    'created_by' => getCurrentUserId()
];

$selectedContainers = [];
$errors = [];

// معالجة النموذج عند الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<div class='alert alert-info'>تم استلام النموذج...</div>";
    // استلام البيانات من النموذج
    $transfer['transfer_date'] = $_POST['transfer_date'] ?? date('Y-m-d');
    $transfer['trader_id'] = $_POST['trader_id'] ?? '';
    $transfer['driver_id'] = $_POST['driver_id'] ?? '';
    $transfer['notes'] = $_POST['notes'] ?? '';
    $selectedContainers = $_POST['selected_containers'] ?? [];
    
    // التحقق من البيانات
    if (empty($transfer['trader_id'])) {
        $errors[] = 'يجب اختيار التاجر';
    }
    
    if (empty($transfer['driver_id'])) {
        $errors[] = 'يجب اختيار السائق';
    }
    
    if (empty($selectedContainers)) {
        $errors[] = 'يجب اختيار حاوية واحدة على الأقل';
    }
    
    echo "<div class='alert alert-warning'>البيانات: تاجر=" . $transfer['trader_id'] . ", سائق=" . $transfer['driver_id'] . ", حاويات=" . count($selectedContainers) . "</div>";
    
    // إذا لم توجد أخطاء، حفظ البيانات
    if (empty($errors)) {
        echo "<div class='alert alert-success'>بدء عملية الحفظ...</div>";
        try {
            $db->beginTransaction();
            
            // إنشاء رقم التحويل
            $transfer['transfer_number'] = 'CT' . date('YmdHis');
            
            // حساب المبلغ الإجمالي
            $totalAmount = 0;
            if (!empty($selectedContainers)) {
                $containerIds = implode(',', array_map('intval', $selectedContainers));
                $stmt = $db->prepare("SELECT SUM(selling_price) as total FROM containers WHERE id IN ($containerIds)");
                $stmt->execute();
                $result = $stmt->fetch();
                $totalAmount = $result['total'] ?? 0;
            }
            $transfer['total_amount'] = $totalAmount;
            
            // إدراج التحويل
            $stmt = $db->prepare("
                INSERT INTO container_transfers 
                (transfer_number, transfer_date, trader_id, driver_id, pickup_location, delivery_location, total_amount, status, notes, created_by, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $transfer['transfer_number'],
                $transfer['transfer_date'],
                $transfer['trader_id'],
                $transfer['driver_id'],
                'المستودع الرئيسي', // pickup_location
                'موقع التسليم', // delivery_location
                $transfer['total_amount'],
                $transfer['status'],
                $transfer['notes'],
                $transfer['created_by']
            ]);
            
            $transferId = $db->lastInsertId();
            
            // إدراج تفاصيل الحاويات
            foreach ($selectedContainers as $containerId) {
                $stmt = $db->prepare("
                    INSERT INTO transfer_containers (transfer_id, container_id, transfer_fee) 
                    VALUES (?, ?, ?)
                ");
                
                // الحصول على سعر البيع للحاوية كرسوم النقل
                $containerStmt = $db->prepare("SELECT selling_price FROM containers WHERE id = ?");
                $containerStmt->execute([$containerId]);
                $containerData = $containerStmt->fetch();
                $transferFee = $containerData['selling_price'] ?? 0;
                
                $stmt->execute([$transferId, $containerId, $transferFee]);
                
                // تحديث حالة الحاوية
                $stmt = $db->prepare("UPDATE containers SET status = 'transferred' WHERE id = ?");
                $stmt->execute([$containerId]);
            }
            
            $db->commit();
            echo "<div class='alert alert-success'>تم حفظ التحويل بنجاح! معرف التحويل: $transferId</div>";
            $_SESSION['success'] = 'تم إنشاء تحويل الحاويات بنجاح';
            // header('Location: index.php?page=transfers');
            // exit;
            
        } catch (PDOException $e) {
            $db->rollBack();
            $errors[] = 'حدث خطأ أثناء حفظ البيانات: ' . $e->getMessage();
        }
    }
}

// استرجاع التجار
try {
    $tradersStmt = $db->prepare("SELECT id, name FROM traders WHERE status = 'active' ORDER BY name");
    $tradersStmt->execute();
    $traders = $tradersStmt->fetchAll();
    echo "<div class='alert alert-info'>تم العثور على " . count($traders) . " تاجر</div>";
} catch (PDOException $e) {
    $traders = [];
    echo "<div class='alert alert-danger'>خطأ في استرجاع التجار: " . $e->getMessage() . "</div>";
}

// استرجاع السائقين
try {
    $driversStmt = $db->prepare("SELECT id, driver_name FROM drivers WHERE status = 'active' ORDER BY driver_name");
    $driversStmt->execute();
    $drivers = $driversStmt->fetchAll();
    echo "<div class='alert alert-info'>تم العثور على " . count($drivers) . " سائق</div>";
} catch (PDOException $e) {
    $drivers = [];
    echo "<div class='alert alert-danger'>خطأ في استرجاع السائقين: " . $e->getMessage() . "</div>";
}

// استرجاع الحاويات المتاحة
try {
    $containersStmt = $db->prepare("
        SELECT id, container_number, content, container_type, selling_price 
        FROM containers 
        WHERE status = 'pending' 
        ORDER BY container_number
    ");
    $containersStmt->execute();
    $availableContainers = $containersStmt->fetchAll();
    echo "<div class='alert alert-info'>تم العثور على " . count($availableContainers) . " حاوية متاحة</div>";
} catch (PDOException $e) {
    $availableContainers = [];
    echo "<div class='alert alert-danger'>خطأ في استرجاع الحاويات: " . $e->getMessage() . "</div>";
}
?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>إضافة تحويل حاويات</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php?page=transfers">التحويلات</a></li>
                    <li class="breadcrumb-item active">إضافة تحويل حاويات</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">بيانات التحويل</h3>
            </div>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger m-3">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="transfer_date">تاريخ التحويل</label>
                                <input type="date" class="form-control" id="transfer_date" name="transfer_date" 
                                       value="<?php echo htmlspecialchars($transfer['transfer_date']); ?>" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="trader_id">التاجر</label>
                                <select class="form-control" id="trader_id" name="trader_id" required>
                                    <option value="">اختر التاجر</option>
                                    <?php foreach ($traders as $trader): ?>
                                        <option value="<?php echo $trader['id']; ?>" 
                                                <?php echo ($transfer['trader_id'] == $trader['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($trader['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="driver_id">السائق</label>
                                <select class="form-control" id="driver_id" name="driver_id" required>
                                    <option value="">اختر السائق</option>
                                    <?php foreach ($drivers as $driver): ?>
                                        <option value="<?php echo $driver['id']; ?>" 
                                                <?php echo ($transfer['driver_id'] == $driver['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($driver['driver_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>الحاويات المتاحة</label>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th width="50">اختيار</th>
                                                <th>رقم الحاوية</th>
                                                <th>المحتوى</th>
                                                <th>النوع</th>
                                                <th>سعر البيع</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($availableContainers as $container): ?>
                                                <tr>
                                                    <td>
                                                        <input type="checkbox" name="selected_containers[]" 
                                                               value="<?php echo $container['id']; ?>"
                                                               <?php echo in_array($container['id'], $selectedContainers) ? 'checked' : ''; ?>>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($container['container_number']); ?></td>
                                                    <td><?php echo htmlspecialchars($container['content']); ?></td>
                                                    <td><?php echo htmlspecialchars($container['container_type']); ?></td>
                                                    <td><?php echo number_format($container['selling_price'], 2); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="notes">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo htmlspecialchars($transfer['notes']); ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التحويل
                    </button>
                    <a href="index.php?page=transfers" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</section>