<?php
// التحقق من الصلاحيات
if (!hasPermission('transfers')) {
    header('Location: index.php?page=unauthorized');
    exit;
}

// متغيرات النموذج
$transfer = [
    'id' => 0,
    'transfer_number' => '',
    'transfer_date' => date('Y-m-d'),
    'trader_id' => '',
    'driver_id' => '',
    'total_amount' => 0,
    'status' => 'pending',
    'notes' => '',
    'created_by' => getCurrentUserId()
];

$selectedContainers = [];
$errors = [];
$success = false;

// معالجة النموذج عند الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // استلام البيانات من النموذج
    $transfer['transfer_date'] = $_POST['transfer_date'] ?? date('Y-m-d');
    $transfer['trader_id'] = $_POST['trader_id'] ?? '';
    $transfer['driver_id'] = $_POST['driver_id'] ?? '';
    $transfer['notes'] = $_POST['notes'] ?? '';
    $selectedContainers = $_POST['selected_containers'] ?? [];

    // التحقق من البيانات
    if (empty($transfer['trader_id'])) {
        $errors[] = 'يجب اختيار التاجر';
    }

    if (empty($transfer['driver_id'])) {
        $errors[] = 'يجب اختيار السائق';
    }

    if (empty($selectedContainers)) {
        $errors[] = 'يجب اختيار حاوية واحدة على الأقل';
    }

    // إذا لم توجد أخطاء، حفظ البيانات
    if (empty($errors)) {
        try {
            $db->beginTransaction();

            // إنشاء رقم التحويل
            $transfer['transfer_number'] = 'TR' . date('YmdHis');

            // حساب المبلغ الإجمالي
            $totalAmount = 0;
            $totalContainers = count($selectedContainers);

            if (!empty($selectedContainers)) {
                $placeholders = str_repeat('?,', count($selectedContainers) - 1) . '?';
                $stmt = $db->prepare("SELECT SUM(selling_price) as total FROM containers WHERE id IN ($placeholders)");
                $stmt->execute($selectedContainers);
                $result = $stmt->fetch();
                $totalAmount = $result['total'] ?? 0;
            }

            // إدراج التحويل
            $stmt = $db->prepare("
                INSERT INTO container_transfers
                (transfer_number, transfer_date, trader_id, driver_id, pickup_location, delivery_location,
                 total_containers, total_amount, status, notes, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $transfer['transfer_number'],
                $transfer['transfer_date'],
                $transfer['trader_id'],
                $transfer['driver_id'],
                'المستودع الرئيسي',
                'موقع التسليم',
                $totalContainers,
                $totalAmount,
                $transfer['status'],
                $transfer['notes'],
                $transfer['created_by']
            ]);

            $transferId = $db->lastInsertId();

            // إدراج تفاصيل الحاويات
            foreach ($selectedContainers as $containerId) {
                // الحصول على سعر البيع للحاوية
                $containerStmt = $db->prepare("SELECT selling_price FROM containers WHERE id = ?");
                $containerStmt->execute([$containerId]);
                $containerData = $containerStmt->fetch();
                $transferFee = $containerData['selling_price'] ?? 0;

                $stmt = $db->prepare("
                    INSERT INTO transfer_containers (transfer_id, container_id, transfer_fee)
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$transferId, $containerId, $transferFee]);

                // تحديث حالة الحاوية إلى "في التقدم"
                $stmt = $db->prepare("UPDATE containers SET status = 'in_progress' WHERE id = ?");
                $stmt->execute([$containerId]);
            }

            // إضافة سجل تتبع أولي
            $trackingStmt = $db->prepare("
                INSERT INTO transfer_tracking (transfer_id, status, update_time, notes, updated_by)
                VALUES (?, 'created', NOW(), 'تم إنشاء التحويل', ?)
            ");
            $trackingStmt->execute([$transferId, $transfer['created_by']]);

            $db->commit();
            $success = true;
            $_SESSION['success'] = 'تم إنشاء تحويل الحاويات بنجاح برقم: ' . $transfer['transfer_number'];

            // إعادة توجيه بعد النجاح
            header('Location: index.php?page=transfers');
            exit;

        } catch (PDOException $e) {
            $db->rollBack();
            $errors[] = 'حدث خطأ أثناء حفظ البيانات: ' . $e->getMessage();
        }
    }
}

// استرجاع التجار (استخدام جدول customers إذا لم يكن traders موجود)
$traders = [];
try {
    // محاولة جلب من جدول traders أولاً
    $tradersStmt = $db->prepare("SELECT id, name FROM traders ORDER BY name");
    $tradersStmt->execute();
    $traders = $tradersStmt->fetchAll();
} catch (PDOException $e) {
    // إذا فشل، جرب جدول customers
    try {
        $tradersStmt = $db->prepare("SELECT id, name FROM customers ORDER BY name");
        $tradersStmt->execute();
        $traders = $tradersStmt->fetchAll();
    } catch (PDOException $e2) {
        $errors[] = 'خطأ في استرجاع التجار: ' . $e2->getMessage();
    }
}

// استرجاع السائقين
$drivers = [];
try {
    $driversStmt = $db->prepare("SELECT id, driver_name FROM drivers WHERE status = 'active' ORDER BY driver_name");
    $driversStmt->execute();
    $drivers = $driversStmt->fetchAll();
} catch (PDOException $e) {
    $errors[] = 'خطأ في استرجاع السائقين: ' . $e->getMessage();
}

// استرجاع الحاويات المتاحة (لن تستخدم الآن لأننا نستخدم AJAX)
$availableContainers = [];
// تم تعطيل هذا الاستعلام لأننا نستخدم AJAX لجلب الحاويات حسب التاجر
/*
try {
    $containersStmt = $db->prepare("
        SELECT c.id, c.container_number, c.content, c.container_type, c.selling_price, t.name as trader_name
        FROM containers c
        LEFT JOIN traders t ON c.trader_id = t.id
        WHERE c.status = 'pending'
        ORDER BY c.container_number
    ");
    $containersStmt->execute();
    $availableContainers = $containersStmt->fetchAll();
} catch (PDOException $e) {
    $errors[] = 'خطأ في استرجاع الحاويات: ' . $e->getMessage();
}
*/
?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>إضافة تحويل حاويات</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php?page=transfers">التحويلات</a></li>
                    <li class="breadcrumb-item active">إضافة تحويل حاويات</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">بيانات التحويل</h3>
            </div>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger m-3">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="transfer_date">تاريخ التحويل</label>
                                <input type="date" class="form-control" id="transfer_date" name="transfer_date" 
                                       value="<?php echo htmlspecialchars($transfer['transfer_date']); ?>" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="trader_id">التاجر <span class="text-danger">*</span></label>
                                <div class="searchable-select-wrapper">
                                    <select class="form-control searchable-select" id="trader_id" name="trader_id" required onchange="loadTraderContainers()" data-placeholder="اختر التاجر أو ابحث...">
                                        <option value="">اختر التاجر</option>
                                        <?php foreach ($traders as $trader): ?>
                                            <option value="<?php echo $trader['id']; ?>"
                                                    <?php echo ($transfer['trader_id'] == $trader['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($trader['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="driver_id">السائق</label>
                                <div class="searchable-select-wrapper">
                                    <select class="form-control searchable-select" id="driver_id" name="driver_id" required data-placeholder="اختر السائق أو ابحث...">
                                        <option value="">اختر السائق</option>
                                        <?php foreach ($drivers as $driver): ?>
                                            <option value="<?php echo $driver['id']; ?>"
                                                    <?php echo ($transfer['driver_id'] == $driver['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($driver['driver_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>الحاويات المتاحة للتحويل</label>

                                <!-- فلاتر البحث -->
                                <div class="card card-outline card-info mb-3" id="containerFilters" style="display: none;">
                                    <div class="card-header">
                                        <h3 class="card-title">
                                            <i class="fas fa-filter"></i> فلاتر البحث
                                        </h3>
                                        <div class="card-tools">
                                            <button type="button" class="btn btn-tool" onclick="clearFilters()">
                                                <i class="fas fa-times"></i> مسح الفلاتر
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="searchContainer">البحث في رقم الحاوية أو المحتوى</label>
                                                    <input type="text" class="form-control" id="searchContainer"
                                                           placeholder="ادخل رقم الحاوية أو المحتوى" onkeyup="filterContainers()">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="filterStatus">حالة الحاوية</label>
                                                    <div class="searchable-select-wrapper">
                                                        <select class="form-control searchable-select" id="filterStatus" onchange="filterContainers()" data-placeholder="اختر الحالة...">
                                                            <option value="">جميع الحالات</option>
                                                            <option value="pending">قيد الانتظار</option>
                                                            <option value="in_progress">قيد التنفيذ</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label for="filterDateFrom">من تاريخ</label>
                                                    <input type="date" class="form-control" id="filterDateFrom" onchange="filterContainers()">
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label for="filterDateTo">إلى تاريخ</label>
                                                    <input type="date" class="form-control" id="filterDateTo" onchange="filterContainers()">
                                                </div>
                                            </div>
                                            <div class="col-md-1">
                                                <div class="form-group">
                                                    <label>&nbsp;</label>
                                                    <button type="button" class="btn btn-primary btn-block" onclick="filterContainers()">
                                                        <i class="fas fa-search"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="containerLoadingMessage" style="display: none;">
                                    <div class="alert alert-info">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        جاري تحميل حاويات التاجر...
                                    </div>
                                </div>

                                <div id="noTraderMessage" class="alert alert-warning">
                                    <i class="fas fa-info-circle"></i>
                                    يرجى اختيار التاجر أولاً لعرض حاوياته المتاحة للتحويل
                                </div>

                                <div id="noContainersMessage" style="display: none;">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        لا توجد حاويات متاحة للتحويل لهذا التاجر
                                    </div>
                                </div>

                                <div id="containersTableContainer" style="display: none;">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        يمكنك اختيار حاوية أو أكثر لتحويلها للتاجر المحدد
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-hover" id="containersTable">
                                            <thead class="table-light">
                                                <tr>
                                                    <th width="50">
                                                        <input type="checkbox" id="selectAll" onclick="toggleAllContainers()">
                                                    </th>
                                                    <th>رقم الحاوية</th>
                                                    <th>المحتوى</th>
                                                    <th>النوع</th>
                                                    <th>تاريخ الدخول</th>
                                                    <th>سعر البيع (د.ع)</th>
                                                </tr>
                                            </thead>
                                            <tbody id="containersTableBody">
                                                <!-- سيتم ملء البيانات عبر AJAX -->
                                            </tbody>
                                            <tfoot class="table-light">
                                                <tr>
                                                    <td colspan="5" class="text-end"><strong>المجموع المتوقع:</strong></td>
                                                    <td class="text-end">
                                                        <strong id="totalAmount">0</strong> د.ع
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="notes">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo htmlspecialchars($transfer['notes']); ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-save"></i> حفظ التحويل
                    </button>
                    <a href="index.php?page=transfers" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                    <div class="float-right">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            سيتم تحديث حالة الحاويات المحددة إلى "قيد التنفيذ"
                        </small>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

<script>
let currentContainers = [];

// تحميل حاويات التاجر
function loadTraderContainers() {
    const traderId = document.getElementById('trader_id').value;

    // إخفاء جميع الرسائل والجداول
    document.getElementById('noTraderMessage').style.display = 'none';
    document.getElementById('noContainersMessage').style.display = 'none';
    document.getElementById('containersTableContainer').style.display = 'none';
    document.getElementById('containerFilters').style.display = 'none';

    if (!traderId) {
        document.getElementById('noTraderMessage').style.display = 'block';
        return;
    }

    // إظهار رسالة التحميل
    document.getElementById('containerLoadingMessage').style.display = 'block';

    // طلب AJAX لجلب الحاويات
    fetch(`ajax/get_trader_containers.php?trader_id=${traderId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('containerLoadingMessage').style.display = 'none';

            if (data.success) {
                currentContainers = data.containers;
                if (data.containers.length > 0) {
                    displayContainers(data.containers);
                    document.getElementById('containersTableContainer').style.display = 'block';
                    document.getElementById('containerFilters').style.display = 'block';
                } else {
                    document.getElementById('noContainersMessage').style.display = 'block';
                }
            } else {
                alert('خطأ في تحميل الحاويات: ' + (data.error || 'خطأ غير معروف'));
                document.getElementById('noContainersMessage').style.display = 'block';
            }
        })
        .catch(error => {
            document.getElementById('containerLoadingMessage').style.display = 'none';
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال بالخادم');
            document.getElementById('noContainersMessage').style.display = 'block';
        });
}

// عرض الحاويات في الجدول
function displayContainers(containers) {
    const tbody = document.getElementById('containersTableBody');
    tbody.innerHTML = '';

    containers.forEach(container => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <input type="checkbox" name="selected_containers[]"
                       class="container-checkbox"
                       value="${container.id}"
                       data-price="${container.selling_price}"
                       onchange="updateTotal()">
            </td>
            <td><strong>${container.container_number}</strong></td>
            <td>${container.content || 'غير محدد'}</td>
            <td>${container.container_type || 'غير محدد'}</td>
            <td>${container.entry_date}</td>
            <td class="text-end"><strong>${container.formatted_price}</strong></td>
        `;
        tbody.appendChild(row);
    });

    updateTotal();
}

// فلترة الحاويات
function filterContainers() {
    const traderId = document.getElementById('trader_id').value;
    if (!traderId) return;

    const search = document.getElementById('searchContainer').value;
    const status = document.getElementById('filterStatus').value;
    const dateFrom = document.getElementById('filterDateFrom').value;
    const dateTo = document.getElementById('filterDateTo').value;

    let url = `ajax/get_trader_containers.php?trader_id=${traderId}`;

    if (search) url += `&search=${encodeURIComponent(search)}`;
    if (status) url += `&status=${status}`;
    if (dateFrom) url += `&date_from=${dateFrom}`;
    if (dateTo) url += `&date_to=${dateTo}`;

    document.getElementById('containerLoadingMessage').style.display = 'block';
    document.getElementById('containersTableContainer').style.display = 'none';

    fetch(url)
        .then(response => response.json())
        .then(data => {
            document.getElementById('containerLoadingMessage').style.display = 'none';

            if (data.success) {
                currentContainers = data.containers;
                if (data.containers.length > 0) {
                    displayContainers(data.containers);
                    document.getElementById('containersTableContainer').style.display = 'block';
                    document.getElementById('noContainersMessage').style.display = 'none';
                } else {
                    document.getElementById('noContainersMessage').style.display = 'block';
                    document.getElementById('containersTableContainer').style.display = 'none';
                }
            } else {
                alert('خطأ في فلترة الحاويات: ' + (data.error || 'خطأ غير معروف'));
            }
        })
        .catch(error => {
            document.getElementById('containerLoadingMessage').style.display = 'none';
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال بالخادم');
        });
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchContainer').value = '';
    document.getElementById('filterStatus').value = '';
    document.getElementById('filterDateFrom').value = '';
    document.getElementById('filterDateTo').value = '';
    filterContainers();
}

function toggleAllContainers() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.container-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateTotal();
}

function updateTotal() {
    const checkboxes = document.querySelectorAll('.container-checkbox:checked');
    let total = 0;

    checkboxes.forEach(checkbox => {
        total += parseFloat(checkbox.dataset.price || 0);
    });

    document.getElementById('totalAmount').textContent = total.toLocaleString('ar-IQ');

    // تحديث زر الإرسال
    const submitBtn = document.getElementById('submitBtn');
    if (checkboxes.length > 0) {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ التحويل (' + checkboxes.length + ' حاوية)';
    } else {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ التحويل';
    }
}

// تشغيل التحديث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateTotal();

    // تحميل الحاويات إذا كان التاجر محدد مسبقاً
    const traderId = document.getElementById('trader_id').value;
    if (traderId) {
        loadTraderContainers();
    }

    // إضافة مستمع للبحث السريع
    document.getElementById('searchContainer').addEventListener('keyup', function(e) {
        if (e.key === 'Enter') {
            filterContainers();
        }
    });

    // التحقق من صحة النموذج قبل الإرسال
    document.querySelector('form').addEventListener('submit', function(e) {
        const selectedContainers = document.querySelectorAll('.container-checkbox:checked');
        const traderId = document.getElementById('trader_id').value;
        const driverId = document.getElementById('driver_id').value;

        if (!traderId) {
            alert('يرجى اختيار التاجر');
            e.preventDefault();
            return false;
        }

        if (!driverId) {
            alert('يرجى اختيار السائق');
            e.preventDefault();
            return false;
        }

        if (selectedContainers.length === 0) {
            alert('يرجى اختيار حاوية واحدة على الأقل');
            e.preventDefault();
            return false;
        }

        // تأكيد الإرسال
        const total = Array.from(selectedContainers).reduce((sum, cb) => sum + parseFloat(cb.dataset.price || 0), 0);
        const confirmMsg = `هل أنت متأكد من إنشاء تحويل لـ ${selectedContainers.length} حاوية بقيمة ${total.toLocaleString('ar-IQ')} د.ع؟`;
        if (!confirm(confirmMsg)) {
            e.preventDefault();
            return false;
        }

        // تعطيل الزر لمنع الإرسال المتكرر
        document.getElementById('submitBtn').disabled = true;
        document.getElementById('submitBtn').innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    });
});
</script>

<style>
#containerFilters {
    transition: all 0.3s ease;
}

#containersTableContainer {
    transition: all 0.3s ease;
}

.container-checkbox {
    transform: scale(1.2);
    margin: 0;
}

.table th {
    background-color: #f8f9fa;
    border-top: 2px solid #dee2e6;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: #f5f5f5;
}

.container-checkbox:checked {
    accent-color: #007bff;
}

#totalAmount {
    font-size: 1.1em;
    color: #28a745;
}

.alert {
    border-radius: 8px;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.card-outline.card-info {
    border-top: 3px solid #17a2b8;
}

.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

.badge {
    font-size: 0.9em;
}

/* تحسينات للهواتف المحمولة */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.9em;
    }

    .btn {
        padding: 0.5rem 1rem;
    }

    .form-control {
        padding: 0.5rem 0.75rem;
    }
}
</style>