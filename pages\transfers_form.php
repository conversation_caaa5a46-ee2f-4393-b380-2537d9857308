<?php
// التحقق من الصلاحيات
if (!hasPermission('transfers')) {
    header('Location: index.php?page=unauthorized');
    exit;
}

// متغيرات النموذج
$transfer = [
    'id' => 0,
    'transfer_number' => '',
    'transfer_date' => date('Y-m-d'),
    'trader_id' => '',
    'driver_id' => '',
    'total_amount' => 0,
    'status' => 'pending',
    'notes' => '',
    'created_by' => getCurrentUserId()
];

$selectedContainers = [];
$errors = [];
$success = false;

// معالجة النموذج عند الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // استلام البيانات من النموذج
    $transfer['transfer_date'] = $_POST['transfer_date'] ?? date('Y-m-d');
    $transfer['trader_id'] = $_POST['trader_id'] ?? '';
    $transfer['driver_id'] = $_POST['driver_id'] ?? '';
    $transfer['notes'] = $_POST['notes'] ?? '';
    $selectedContainers = $_POST['selected_containers'] ?? [];

    // التحقق من البيانات
    if (empty($transfer['trader_id'])) {
        $errors[] = 'يجب اختيار التاجر';
    }

    if (empty($transfer['driver_id'])) {
        $errors[] = 'يجب اختيار السائق';
    }

    if (empty($selectedContainers)) {
        $errors[] = 'يجب اختيار حاوية واحدة على الأقل';
    }

    // إذا لم توجد أخطاء، حفظ البيانات
    if (empty($errors)) {
        try {
            $db->beginTransaction();

            // إنشاء رقم التحويل
            $transfer['transfer_number'] = 'TR' . date('YmdHis');

            // حساب المبلغ الإجمالي
            $totalAmount = 0;
            $totalContainers = count($selectedContainers);

            if (!empty($selectedContainers)) {
                $placeholders = str_repeat('?,', count($selectedContainers) - 1) . '?';
                $stmt = $db->prepare("SELECT SUM(selling_price) as total FROM containers WHERE id IN ($placeholders)");
                $stmt->execute($selectedContainers);
                $result = $stmt->fetch();
                $totalAmount = $result['total'] ?? 0;
            }

            // إدراج التحويل
            $stmt = $db->prepare("
                INSERT INTO container_transfers
                (transfer_number, transfer_date, trader_id, driver_id, pickup_location, delivery_location,
                 total_containers, total_amount, status, notes, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $transfer['transfer_number'],
                $transfer['transfer_date'],
                $transfer['trader_id'],
                $transfer['driver_id'],
                'المستودع الرئيسي',
                'موقع التسليم',
                $totalContainers,
                $totalAmount,
                $transfer['status'],
                $transfer['notes'],
                $transfer['created_by']
            ]);

            $transferId = $db->lastInsertId();

            // إدراج تفاصيل الحاويات
            foreach ($selectedContainers as $containerId) {
                // الحصول على سعر البيع للحاوية
                $containerStmt = $db->prepare("SELECT selling_price FROM containers WHERE id = ?");
                $containerStmt->execute([$containerId]);
                $containerData = $containerStmt->fetch();
                $transferFee = $containerData['selling_price'] ?? 0;

                $stmt = $db->prepare("
                    INSERT INTO transfer_containers (transfer_id, container_id, transfer_fee)
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$transferId, $containerId, $transferFee]);

                // تحديث حالة الحاوية إلى "في التقدم"
                $stmt = $db->prepare("UPDATE containers SET status = 'in_progress' WHERE id = ?");
                $stmt->execute([$containerId]);
            }

            // إضافة سجل تتبع أولي
            $trackingStmt = $db->prepare("
                INSERT INTO transfer_tracking (transfer_id, status, update_time, notes, updated_by)
                VALUES (?, 'created', NOW(), 'تم إنشاء التحويل', ?)
            ");
            $trackingStmt->execute([$transferId, $transfer['created_by']]);

            $db->commit();
            $success = true;
            $_SESSION['success'] = 'تم إنشاء تحويل الحاويات بنجاح برقم: ' . $transfer['transfer_number'];

            // إعادة توجيه بعد النجاح
            header('Location: index.php?page=transfers');
            exit;

        } catch (PDOException $e) {
            $db->rollBack();
            $errors[] = 'حدث خطأ أثناء حفظ البيانات: ' . $e->getMessage();
        }
    }
}

// استرجاع التجار (استخدام جدول customers إذا لم يكن traders موجود)
$traders = [];
try {
    // محاولة جلب من جدول traders أولاً
    $tradersStmt = $db->prepare("SELECT id, name FROM traders ORDER BY name");
    $tradersStmt->execute();
    $traders = $tradersStmt->fetchAll();
} catch (PDOException $e) {
    // إذا فشل، جرب جدول customers
    try {
        $tradersStmt = $db->prepare("SELECT id, name FROM customers ORDER BY name");
        $tradersStmt->execute();
        $traders = $tradersStmt->fetchAll();
    } catch (PDOException $e2) {
        $errors[] = 'خطأ في استرجاع التجار: ' . $e2->getMessage();
    }
}

// استرجاع السائقين
$drivers = [];
try {
    $driversStmt = $db->prepare("SELECT id, driver_name FROM drivers WHERE status = 'active' ORDER BY driver_name");
    $driversStmt->execute();
    $drivers = $driversStmt->fetchAll();
} catch (PDOException $e) {
    $errors[] = 'خطأ في استرجاع السائقين: ' . $e->getMessage();
}

// استرجاع الحاويات المتاحة
$availableContainers = [];
try {
    $containersStmt = $db->prepare("
        SELECT c.id, c.container_number, c.content, c.container_type, c.selling_price, cu.name as customer_name
        FROM containers c
        LEFT JOIN customers cu ON c.customer_id = cu.id
        WHERE c.status = 'pending'
        ORDER BY c.container_number
    ");
    $containersStmt->execute();
    $availableContainers = $containersStmt->fetchAll();
} catch (PDOException $e) {
    $errors[] = 'خطأ في استرجاع الحاويات: ' . $e->getMessage();
}
?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>إضافة تحويل حاويات</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php?page=transfers">التحويلات</a></li>
                    <li class="breadcrumb-item active">إضافة تحويل حاويات</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">بيانات التحويل</h3>
            </div>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger m-3">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="transfer_date">تاريخ التحويل</label>
                                <input type="date" class="form-control" id="transfer_date" name="transfer_date" 
                                       value="<?php echo htmlspecialchars($transfer['transfer_date']); ?>" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="trader_id">التاجر</label>
                                <select class="form-control" id="trader_id" name="trader_id" required>
                                    <option value="">اختر التاجر</option>
                                    <?php foreach ($traders as $trader): ?>
                                        <option value="<?php echo $trader['id']; ?>" 
                                                <?php echo ($transfer['trader_id'] == $trader['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($trader['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="driver_id">السائق</label>
                                <select class="form-control" id="driver_id" name="driver_id" required>
                                    <option value="">اختر السائق</option>
                                    <?php foreach ($drivers as $driver): ?>
                                        <option value="<?php echo $driver['id']; ?>" 
                                                <?php echo ($transfer['driver_id'] == $driver['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($driver['driver_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>الحاويات المتاحة للتحويل</label>
                                <?php if (empty($availableContainers)): ?>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        لا توجد حاويات متاحة للتحويل حالياً. يجب أن تكون الحاويات في حالة "قيد الانتظار" لتكون متاحة للتحويل.
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        يمكنك اختيار حاوية أو أكثر لتحويلها للتاجر المحدد
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th width="50">
                                                        <input type="checkbox" id="selectAll" onclick="toggleAllContainers()">
                                                    </th>
                                                    <th>رقم الحاوية</th>
                                                    <th>العميل</th>
                                                    <th>المحتوى</th>
                                                    <th>النوع</th>
                                                    <th>سعر البيع (د.ع)</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($availableContainers as $container): ?>
                                                    <tr>
                                                        <td>
                                                            <input type="checkbox" name="selected_containers[]"
                                                                   class="container-checkbox"
                                                                   value="<?php echo $container['id']; ?>"
                                                                   data-price="<?php echo $container['selling_price']; ?>"
                                                                   onchange="updateTotal()"
                                                                   <?php echo in_array($container['id'], $selectedContainers) ? 'checked' : ''; ?>>
                                                        </td>
                                                        <td>
                                                            <strong><?php echo htmlspecialchars($container['container_number']); ?></strong>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($container['customer_name'] ?? 'غير محدد'); ?></td>
                                                        <td><?php echo htmlspecialchars($container['content'] ?? 'غير محدد'); ?></td>
                                                        <td><?php echo htmlspecialchars($container['container_type'] ?? 'غير محدد'); ?></td>
                                                        <td class="text-end">
                                                            <strong><?php echo number_format($container['selling_price'], 0); ?></strong>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                            <tfoot class="table-light">
                                                <tr>
                                                    <td colspan="5" class="text-end"><strong>المجموع المتوقع:</strong></td>
                                                    <td class="text-end">
                                                        <strong id="totalAmount">0</strong> د.ع
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="notes">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo htmlspecialchars($transfer['notes']); ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-save"></i> حفظ التحويل
                    </button>
                    <a href="index.php?page=transfers" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                    <div class="float-right">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            سيتم تحديث حالة الحاويات المحددة إلى "قيد التنفيذ"
                        </small>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

<script>
function toggleAllContainers() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.container-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateTotal();
}

function updateTotal() {
    const checkboxes = document.querySelectorAll('.container-checkbox:checked');
    let total = 0;

    checkboxes.forEach(checkbox => {
        total += parseFloat(checkbox.dataset.price || 0);
    });

    document.getElementById('totalAmount').textContent = total.toLocaleString('ar-IQ');

    // تحديث زر الإرسال
    const submitBtn = document.getElementById('submitBtn');
    if (checkboxes.length > 0) {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ التحويل (' + checkboxes.length + ' حاوية)';
    } else {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ التحويل';
    }
}

// تشغيل التحديث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateTotal();

    // التحقق من صحة النموذج قبل الإرسال
    document.querySelector('form').addEventListener('submit', function(e) {
        const selectedContainers = document.querySelectorAll('.container-checkbox:checked');
        const traderId = document.getElementById('trader_id').value;
        const driverId = document.getElementById('driver_id').value;

        if (!traderId) {
            alert('يرجى اختيار التاجر');
            e.preventDefault();
            return false;
        }

        if (!driverId) {
            alert('يرجى اختيار السائق');
            e.preventDefault();
            return false;
        }

        if (selectedContainers.length === 0) {
            alert('يرجى اختيار حاوية واحدة على الأقل');
            e.preventDefault();
            return false;
        }

        // تأكيد الإرسال
        const confirmMsg = `هل أنت متأكد من إنشاء تحويل لـ ${selectedContainers.length} حاوية؟`;
        if (!confirm(confirmMsg)) {
            e.preventDefault();
            return false;
        }

        // تعطيل الزر لمنع الإرسال المتكرر
        document.getElementById('submitBtn').disabled = true;
        document.getElementById('submitBtn').innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    });
});
</script>