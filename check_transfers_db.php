<?php
require_once 'config/config.php';

try {
    $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>فحص جداول التحويلات</h2>";
    
    // فحص الجداول المطلوبة
    $tables = [
        'drivers',
        'traders', 
        'container_transfers', 
        'transfer_containers',
        'transfer_expenses',
        'transfer_tracking'
    ];
    
    foreach ($tables as $table) {
        $stmt = $db->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✓ الجدول $table موجود</p>";
        } else {
            echo "<p style='color: red;'>✗ الجدول $table غير موجود</p>";
        }
    }
    
    echo "<h3>فحص بيانات الجداول:</h3>";
    
    // فحص التجار
    try {
        $stmt = $db->query("SELECT COUNT(*) as count FROM traders");
        $count = $stmt->fetch()['count'];
        echo "<p>عدد التجار: $count</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>خطأ في جدول التجار: " . $e->getMessage() . "</p>";
    }
    
    // فحص السائقين
    try {
        $stmt = $db->query("SELECT COUNT(*) as count FROM drivers");
        $count = $stmt->fetch()['count'];
        echo "<p>عدد السائقين: $count</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>خطأ في جدول السائقين: " . $e->getMessage() . "</p>";
    }
    
    // فحص الحاويات المتاحة
    try {
        $stmt = $db->query("SELECT COUNT(*) as count FROM containers WHERE status = 'pending'");
        $count = $stmt->fetch()['count'];
        echo "<p>عدد الحاويات المتاحة: $count</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>خطأ في جدول الحاويات: " . $e->getMessage() . "</p>";
    }
    
    // فحص التحويلات
    try {
        $stmt = $db->query("SELECT COUNT(*) as count FROM container_transfers");
        $count = $stmt->fetch()['count'];
        echo "<p>عدد التحويلات: $count</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>خطأ في جدول التحويلات: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}
?>
