<?php
// التحقق من الصلاحيات
if (!hasPermission('transfers')) {
    header('Location: index.php?page=unauthorized');
    exit;
}

// التحقق من وجود معرف التحويل
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = 'معرف التحويل مطلوب';
    header('Location: index.php?page=transfers');
    exit;
}

$transferId = (int)$_GET['id'];

try {
    // جلب بيانات التحويل مع بيانات التاجر والسائق
    $transferStmt = $db->prepare("
        SELECT 
            ct.*,
            t.name as trader_name,
            t.contact_person as trader_contact,
            t.phone as trader_phone,
            t.address as trader_address,
            d.driver_name,
            d.phone as driver_phone,
            d.license_number,
            d.vehicle_type,
            d.vehicle_number,
            u.full_name as created_by_name
        FROM container_transfers ct
        LEFT JOIN traders t ON ct.trader_id = t.id
        LEFT JOIN drivers d ON ct.driver_id = d.id
        LEFT JOIN users u ON ct.created_by = u.id
        WHERE ct.id = ?
    ");
    $transferStmt->execute([$transferId]);
    $transfer = $transferStmt->fetch();
    
    if (!$transfer) {
        $_SESSION['error'] = 'التحويل غير موجود';
        header('Location: index.php?page=transfers');
        exit;
    }
    
    // جلب تفاصيل الحاويات في التحويل
    $containersStmt = $db->prepare("
        SELECT 
            tc.*,
            c.container_number,
            c.content,
            c.container_type,
            c.entry_date,
            c.selling_price,
            c.purchase_price
        FROM transfer_containers tc
        JOIN containers c ON tc.container_id = c.id
        WHERE tc.transfer_id = ?
        ORDER BY c.container_number
    ");
    $containersStmt->execute([$transferId]);
    $containers = $containersStmt->fetchAll();
    
    // جلب سجل التتبع
    $trackingStmt = $db->prepare("
        SELECT 
            tt.*,
            u.full_name as updated_by_name
        FROM transfer_tracking tt
        LEFT JOIN users u ON tt.updated_by = u.id
        WHERE tt.transfer_id = ?
        ORDER BY tt.update_time DESC
    ");
    $trackingStmt->execute([$transferId]);
    $tracking = $trackingStmt->fetchAll();
    
} catch (PDOException $e) {
    $_SESSION['error'] = 'حدث خطأ أثناء جلب بيانات التحويل: ' . $e->getMessage();
    header('Location: index.php?page=transfers');
    exit;
}

// تحديد حالة التحويل بالعربية
$statusLabels = [
    'pending' => 'قيد الانتظار',
    'in_progress' => 'قيد التنفيذ',
    'completed' => 'مكتمل',
    'cancelled' => 'ملغى'
];

$conditionLabels = [
    'good' => 'جيدة',
    'damaged' => 'تالفة',
    'sealed' => 'مختومة',
    'unsealed' => 'غير مختومة'
];

$trackingStatusLabels = [
    'created' => 'تم الإنشاء',
    'pickup_scheduled' => 'تم جدولة الاستلام',
    'picked_up' => 'تم الاستلام',
    'in_transit' => 'في الطريق',
    'delivered' => 'تم التسليم',
    'delayed' => 'متأخر',
    'cancelled' => 'ملغى'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة تحويل رقم <?php echo htmlspecialchars($transfer['transfer_number']); ?></title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background: #fff;
        }
        
        .print-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .print-btn:hover {
            background: #0056b3;
        }
        
        .print-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
            background: white;
            min-height: 297mm;
        }
        
        .print-header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .print-header h1 {
            font-size: 28px;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .print-header h2 {
            font-size: 20px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .company-info {
            text-align: center;
            color: #666;
            font-size: 12px;
        }
        
        .transfer-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .info-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .info-section h3 {
            color: #007bff;
            font-size: 16px;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        
        .info-value {
            color: #212529;
        }
        
        .containers-section {
            margin-bottom: 30px;
        }
        
        .containers-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background: white;
        }
        
        .containers-table th,
        .containers-table td {
            border: 1px solid #dee2e6;
            padding: 12px 8px;
            text-align: right;
        }
        
        .containers-table th {
            background: #007bff;
            color: white;
            font-weight: bold;
            font-size: 13px;
        }
        
        .containers-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .containers-table tbody tr:hover {
            background: #e3f2fd;
        }
        
        .total-row {
            background: #e8f4fd !important;
            font-weight: bold;
            color: #007bff;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-in_progress { background: #d1ecf1; color: #0c5460; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        .tracking-section {
            margin-top: 30px;
        }
        
        .tracking-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        
        .tracking-time {
            font-weight: bold;
            color: #007bff;
            margin-left: 15px;
            min-width: 120px;
        }
        
        .tracking-status {
            font-weight: bold;
            margin-left: 15px;
            min-width: 100px;
        }
        
        .signature-section {
            margin-top: 50px;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
        }
        
        .signature-box {
            text-align: center;
            padding: 20px;
            border: 2px dashed #ccc;
            border-radius: 8px;
        }
        
        .signature-label {
            font-weight: bold;
            color: #666;
            margin-bottom: 40px;
        }
        
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 40px;
            padding-top: 10px;
            font-size: 12px;
            color: #666;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
        }
        
        /* أنماط الطباعة */
        @media print {
            .print-btn {
                display: none !important;
            }
            
            body {
                font-size: 12px;
            }
            
            .print-container {
                max-width: none;
                margin: 0;
                padding: 15mm;
                box-shadow: none;
            }
            
            .info-section {
                background: #f8f9fa !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .containers-table th {
                background: #007bff !important;
                color: white !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            @page {
                size: A4;
                margin: 15mm;
            }
            
            .print-header,
            .transfer-info,
            .containers-section,
            .signature-section {
                page-break-inside: avoid;
            }
            
            .containers-table {
                page-break-inside: auto;
            }
            
            .containers-table thead {
                display: table-header-group;
            }
        }
        
        /* تحسينات للهواتف المحمولة */
        @media (max-width: 768px) {
            .print-container {
                padding: 10px;
            }
            
            .transfer-info {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .signature-section {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .containers-table {
                font-size: 12px;
            }
            
            .containers-table th,
            .containers-table td {
                padding: 8px 4px;
            }
        }
    </style>
</head>

<body>
    <button class="print-btn" onclick="window.print()">
        <i class="fas fa-print"></i> طباعة
    </button>

    <div class="print-container">
        <!-- رأس المستند -->
        <div class="print-header">
            <h1><?php echo COMPANY_NAME; ?></h1>
            <h2>سند تحويل حاويات</h2>
            <div class="company-info">
                <p><?php echo COMPANY_ADDRESS; ?> | هاتف: <?php echo COMPANY_PHONE; ?></p>
                <p>تاريخ الطباعة: <?php echo date('Y-m-d H:i'); ?></p>
            </div>
        </div>

        <!-- معلومات التحويل -->
        <div class="transfer-info">
            <div class="info-section">
                <h3>معلومات التحويل</h3>
                <div class="info-row">
                    <span class="info-label">رقم التحويل:</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['transfer_number']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">تاريخ التحويل:</span>
                    <span class="info-value"><?php echo date('Y-m-d', strtotime($transfer['transfer_date'])); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">الحالة:</span>
                    <span class="info-value">
                        <span class="status-badge status-<?php echo $transfer['status']; ?>">
                            <?php echo $statusLabels[$transfer['status']] ?? $transfer['status']; ?>
                        </span>
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">عدد الحاويات:</span>
                    <span class="info-value"><?php echo $transfer['total_containers']; ?> حاوية</span>
                </div>
                <div class="info-row">
                    <span class="info-label">المبلغ الإجمالي:</span>
                    <span class="info-value"><?php echo number_format($transfer['total_amount'], 0); ?> <?php echo CURRENCY_SYMBOL; ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">أنشأ بواسطة:</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['created_by_name'] ?? 'غير محدد'); ?></span>
                </div>
            </div>

            <div class="info-section">
                <h3>معلومات التاجر</h3>
                <div class="info-row">
                    <span class="info-label">اسم التاجر:</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['trader_name'] ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">الشخص المسؤول:</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['trader_contact'] ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم الهاتف:</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['trader_phone'] ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['trader_address'] ?? 'غير محدد'); ?></span>
                </div>
            </div>
        </div>

        <div class="transfer-info">
            <div class="info-section">
                <h3>معلومات السائق</h3>
                <div class="info-row">
                    <span class="info-label">اسم السائق:</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['driver_name'] ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم الهاتف:</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['driver_phone'] ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم الرخصة:</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['license_number'] ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">نوع المركبة:</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['vehicle_type'] ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم المركبة:</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['vehicle_number'] ?? 'غير محدد'); ?></span>
                </div>
            </div>

            <div class="info-section">
                <h3>معلومات النقل</h3>
                <div class="info-row">
                    <span class="info-label">موقع الاستلام:</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['pickup_location']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">موقع التسليم:</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['delivery_location']); ?></span>
                </div>
                <?php if ($transfer['pickup_time']): ?>
                <div class="info-row">
                    <span class="info-label">وقت الاستلام:</span>
                    <span class="info-value"><?php echo date('Y-m-d H:i', strtotime($transfer['pickup_time'])); ?></span>
                </div>
                <?php endif; ?>
                <?php if ($transfer['delivery_time']): ?>
                <div class="info-row">
                    <span class="info-label">وقت التسليم:</span>
                    <span class="info-value"><?php echo date('Y-m-d H:i', strtotime($transfer['delivery_time'])); ?></span>
                </div>
                <?php endif; ?>
                <?php if ($transfer['notes']): ?>
                <div class="info-row">
                    <span class="info-label">ملاحظات:</span>
                    <span class="info-value"><?php echo htmlspecialchars($transfer['notes']); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- جدول الحاويات -->
        <div class="containers-section">
            <h3 style="color: #007bff; margin-bottom: 15px; font-size: 18px;">تفاصيل الحاويات</h3>
            
            <?php if (!empty($containers)): ?>
                <table class="containers-table">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="15%">رقم الحاوية</th>
                            <th width="20%">المحتوى</th>
                            <th width="10%">النوع</th>
                            <th width="12%">تاريخ الدخول</th>
                            <th width="12%">رسوم النقل</th>
                            <th width="10%">حالة الحاوية</th>
                            <th width="16%">ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $totalFees = 0;
                        foreach ($containers as $index => $container): 
                            $totalFees += $container['transfer_fee'];
                        ?>
                            <tr>
                                <td><?php echo $index + 1; ?></td>
                                <td><strong><?php echo htmlspecialchars($container['container_number']); ?></strong></td>
                                <td><?php echo htmlspecialchars($container['content'] ?? 'غير محدد'); ?></td>
                                <td><?php echo htmlspecialchars($container['container_type'] ?? 'غير محدد'); ?></td>
                                <td><?php echo date('Y-m-d', strtotime($container['entry_date'])); ?></td>
                                <td style="text-align: left;"><?php echo number_format($container['transfer_fee'], 0); ?></td>
                                <td>
                                    <span class="status-badge status-<?php echo $container['container_condition']; ?>">
                                        <?php echo $conditionLabels[$container['container_condition']] ?? $container['container_condition']; ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($container['notes'] ?? ''); ?></td>
                            </tr>
                        <?php endforeach; ?>
                        
                        <tr class="total-row">
                            <td colspan="5"><strong>الإجمالي</strong></td>
                            <td style="text-align: left;"><strong><?php echo number_format($totalFees, 0); ?> <?php echo CURRENCY_SYMBOL; ?></strong></td>
                            <td colspan="2"></td>
                        </tr>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    لا توجد حاويات مرتبطة بهذا التحويل
                </div>
            <?php endif; ?>
        </div>

        <!-- سجل التتبع -->
        <?php if (!empty($tracking)): ?>
        <div class="tracking-section">
            <h3 style="color: #007bff; margin-bottom: 15px; font-size: 18px;">سجل تتبع التحويل</h3>
            
            <?php foreach ($tracking as $track): ?>
                <div class="tracking-item">
                    <div class="tracking-time">
                        <?php echo date('Y-m-d H:i', strtotime($track['update_time'])); ?>
                    </div>
                    <div class="tracking-status">
                        <?php echo $trackingStatusLabels[$track['status']] ?? $track['status']; ?>
                    </div>
                    <div class="tracking-details">
                        <?php if ($track['location']): ?>
                            <strong>الموقع:</strong> <?php echo htmlspecialchars($track['location']); ?>
                        <?php endif; ?>
                        <?php if ($track['notes']): ?>
                            <br><strong>ملاحظات:</strong> <?php echo htmlspecialchars($track['notes']); ?>
                        <?php endif; ?>
                        <?php if ($track['updated_by_name']): ?>
                            <br><small><strong>بواسطة:</strong> <?php echo htmlspecialchars($track['updated_by_name']); ?></small>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- قسم التوقيعات -->
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-label">توقيع التاجر</div>
                <div class="signature-line">
                    <?php echo htmlspecialchars($transfer['trader_name'] ?? ''); ?>
                </div>
            </div>
            
            <div class="signature-box">
                <div class="signature-label">توقيع السائق</div>
                <div class="signature-line">
                    <?php echo htmlspecialchars($transfer['driver_name'] ?? ''); ?>
                </div>
            </div>
            
            <div class="signature-box">
                <div class="signature-label">توقيع المسؤول</div>
                <div class="signature-line">
                    <?php echo htmlspecialchars($transfer['created_by_name'] ?? ''); ?>
                </div>
            </div>
        </div>

        <!-- تذييل المستند -->
        <div class="footer">
            <p>تم إنشاء هذا المستند بواسطة نظام <?php echo APP_NAME; ?> - <?php echo date('Y-m-d H:i:s'); ?></p>
            <p>هذا المستند صالح قانونياً ولا يحتاج إلى توقيع إضافي</p>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند فتح الصفحة (اختياري)
        // window.onload = function() { window.print(); }
        
        // إضافة اختصار لوحة المفاتيح للطباعة
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>
</body>
</html>
