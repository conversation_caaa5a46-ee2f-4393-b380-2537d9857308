<?php
// التحقق من الصلاحيات
if (!hasPermission('reports')) {
    header('Location: index.php?page=unauthorized');
    exit;
}

// استعلام التقارير الرقابية
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$status = isset($_GET['status']) ? $_GET['status'] : '';

// بناء شرط الاستعلام
$conditions = [];
$params = [];

// إضافة شرط التاريخ
$conditions[] = "c.entry_date BETWEEN :start_date AND :end_date";
$params['start_date'] = $startDate;
$params['end_date'] = $endDate;

// إضافة شرط الحالة إذا تم تحديدها
if (!empty($status)) {
    $conditions[] = "c.status = :status";
    $params['status'] = $status;
}

// بناء جملة الاستعلام
$whereClause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";

// استعلام إحصائيات الحاويات
$statsQuery = "
    SELECT
        COUNT(*) as total_containers,
        SUM(CASE WHEN c.status = 'pending' THEN 1 ELSE 0 END) as pending_containers,
        SUM(CASE WHEN c.status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_containers,
        SUM(CASE WHEN c.status = 'completed' THEN 1 ELSE 0 END) as completed_containers,
        SUM(CASE WHEN c.status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_containers
    FROM
        containers c
    $whereClause
";

// استعلام تفاصيل الحاويات
$containersQuery = "
    SELECT
        c.id,
        c.container_number,
        c.entry_date,
        c.status,
        c.container_type,
        c.purchase_price,
        c.selling_price,
        c.content,
        c.notes,
        cu.name as customer_name,
        (SELECT COUNT(*) FROM financial_documents fd WHERE fd.container_id = c.id) as documents_count
    FROM
        containers c
    LEFT JOIN
        customers cu ON c.customer_id = cu.id
    $whereClause
    ORDER BY
        c.entry_date DESC, c.id DESC
";

try {
    // تنفيذ استعلام إحصائيات الحاويات
    $statsStmt = $db->prepare($statsQuery);
    $statsStmt->execute($params);
    $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);

    // تنفيذ استعلام تفاصيل الحاويات
    $containersStmt = $db->prepare($containersQuery);
    $containersStmt->execute($params);
    $containers = $containersStmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // تسجيل الخطأ
    error_log("خطأ في استعلام التقارير الرقابية: " . $e->getMessage());
    $error = "حدث خطأ أثناء استرجاع البيانات. يرجى المحاولة مرة أخرى.";
}

// تحديد حالات الحاويات للفلتر
$containerStatuses = [
    'pending' => 'قيد الانتظار',
    'in_progress' => 'قيد التنفيذ',
    'completed' => 'مكتملة',
    'cancelled' => 'ملغاة'
];
?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">التقارير الرقابية</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php?page=reports">التقارير</a></li>
                    <li class="breadcrumb-item active">التقارير الرقابية</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- Navigation Tabs -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="btn-group" role="group">
                    <a href="index.php?page=reports&type=financial" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line"></i> التقارير المالية
                    </a>
                    <a href="index.php?page=reports&type=cash" class="btn btn-outline-primary">
                        <i class="fas fa-money-bill-wave"></i> التقارير النقدية
                    </a>
                    <a href="index.php?page=reports&type=regulatory" class="btn btn-primary">
                        <i class="fas fa-clipboard-check"></i> التقارير الرقابية
                    </a>
                    <a href="index.php?page=reports&type=penalties" class="btn btn-outline-primary">
                        <i class="fas fa-exclamation-triangle"></i> تقارير الغرامات
                    </a>
                </div>
                <button type="button" class="btn btn-success float-right" onclick="window.print()">
                    <i class="fas fa-print"></i> طباعة التقرير
                </button>
            </div>
        </div>

        <!-- Search and Filter Card -->
        <div class="card card-primary card-outline no-print">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter"></i> معايير التقرير
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form method="get" action="index.php">
                    <input type="hidden" name="page" value="reports">
                    <input type="hidden" name="type" value="regulatory">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="start_date">
                                    <i class="fas fa-calendar-alt"></i> من تاريخ
                                </label>
                                <input type="date" class="form-control" id="start_date" name="start_date"
                                       value="<?php echo $startDate; ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="end_date">
                                    <i class="fas fa-calendar-alt"></i> إلى تاريخ
                                </label>
                                <input type="date" class="form-control" id="end_date" name="end_date"
                                       value="<?php echo $endDate; ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="status">
                                    <i class="fas fa-tasks"></i> حالة الحاوية
                                </label>
                                <div class="searchable-select-wrapper">
                                    <select class="form-control searchable-select" id="status" name="status" data-placeholder="اختر حالة الحاوية...">
                                        <option value="">الكل</option>
                                        <?php foreach ($containerStatuses as $value => $label): ?>
                                            <option value="<?php echo $value; ?>" <?php echo $status === $value ? 'selected' : ''; ?>>
                                                <?php echo $label; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="d-block">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    <a href="index.php?page=reports&type=regulatory" class="btn btn-default">
                                        <i class="fas fa-redo"></i> إعادة تعيين
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php else: ?>
        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-lg-3 col-6">
                <!-- small box -->
                <div class="small-box bg-primary">
                    <div class="inner">
                        <h3><?php echo $stats['total_containers']; ?></h3>
                        <p>إجمالي الحاويات</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <a href="#containers-section" class="small-box-footer">
                        المزيد من التفاصيل <i class="fas fa-arrow-circle-right"></i>
                    </a>
                </div>
            </div>
            <!-- ./col -->
            <div class="col-lg-3 col-6">
                <!-- small box -->
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3><?php echo $stats['pending_containers']; ?></h3>
                        <p>قيد الانتظار</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <a href="#containers-section" class="small-box-footer">
                        المزيد من التفاصيل <i class="fas fa-arrow-circle-right"></i>
                    </a>
                </div>
            </div>
            <!-- ./col -->
            <div class="col-lg-3 col-6">
                <!-- small box -->
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3><?php echo $stats['in_progress_containers']; ?></h3>
                        <p>قيد التنفيذ</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <a href="#containers-section" class="small-box-footer">
                        المزيد من التفاصيل <i class="fas fa-arrow-circle-right"></i>
                    </a>
                </div>
            </div>
            <!-- ./col -->
            <div class="col-lg-3 col-6">
                <!-- small box -->
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3><?php echo $stats['completed_containers']; ?></h3>
                        <p>مكتملة</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <a href="#containers-section" class="small-box-footer">
                        المزيد من التفاصيل <i class="fas fa-arrow-circle-right"></i>
                    </a>
                </div>
            </div>
            <!-- ./col -->
        </div>
        <!-- /.row -->

        <!-- تفاصيل الحاويات -->
        <div class="card" id="containers-section">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-list"></i> تفاصيل الحاويات
                </h3>
                <div class="card-tools">
                    <span class="badge badge-info">الفترة: <?php echo $startDate; ?> - <?php echo $endDate; ?></span>
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-tool" data-card-widget="maximize">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>
            <div class="card-body table-responsive p-0">
                <table class="table table-hover text-nowrap">
                    <thead>
                        <tr>
                            <th style="width: 50px">#</th>
                            <th>رقم الحاوية</th>
                            <th>الشركة</th>
                            <th>تاريخ الاستلام</th>
                            <th>النوع</th>
                            <th>سعر الكلفة</th>
                            <th>سعر البيع</th>
                            <th>المحتوى</th>
                            <th>الحالة</th>
                            <th>المستندات</th>
                            <th style="width: 100px" class="no-print">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($containers)): ?>
                            <tr>
                                <td colspan="11" class="text-center py-4">لا توجد بيانات متاحة</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($containers as $index => $container): ?>
                                <tr>
                                    <td><?php echo $index + 1; ?></td>
                                    <td><strong><?php echo htmlspecialchars($container['container_number']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($container['customer_name'] ?? '-'); ?></td>
                                    <td><?php echo date('d/m/Y', strtotime($container['entry_date'])); ?></td>
                                    <td>
                                        <span class="badge badge-secondary"><?php echo htmlspecialchars($container['container_type'] ?? '-'); ?></span>
                                    </td>
                                    <td class="text-right">
                                        <strong><?php echo number_format($container['purchase_price'], 2); ?></strong>
                                        <small class="text-muted">د.ع</small>
                                    </td>
                                    <td class="text-right">
                                        <strong><?php echo number_format($container['selling_price'], 2); ?></strong>
                                        <small class="text-muted">د.ع</small>
                                    </td>
                                    <td><?php echo htmlspecialchars(substr($container['content'], 0, 30)) . (strlen($container['content']) > 30 ? '...' : ''); ?></td>
                                    <td>
                                        <?php
                                            $statusLabels = [
                                                'pending' => '<span class="badge badge-warning">قيد الانتظار</span>',
                                                'in_progress' => '<span class="badge badge-info">قيد التنفيذ</span>',
                                                'completed' => '<span class="badge badge-success">مكتملة</span>',
                                                'cancelled' => '<span class="badge badge-danger">ملغاة</span>'
                                            ];
                                            echo $statusLabels[$container['status']] ?? $container['status'];
                                        ?>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge badge-primary"><?php echo $container['documents_count']; ?></span>
                                    </td>
                                    <td class="no-print">
                                        <div class="btn-group">
                                            <a href="index.php?page=containers&action=view&id=<?php echo $container['id']; ?>"
                                               class="btn btn-info btn-sm" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="index.php?page=containers&action=edit&id=<?php echo $container['id']; ?>"
                                               class="btn btn-primary btn-sm" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->

<!-- أنماط الطباعة -->
<style media="print">
    @page {
        size: A4 landscape;
        margin: 1cm;
    }
    body {
        font-size: 12pt;
    }
    .no-print {
        display: none !important;
    }
    .card {
        border: 1px solid #ddd;
        margin-bottom: 20px;
        break-inside: avoid;
    }
    .table {
        width: 100%;
        border-collapse: collapse;
    }
    .table th, .table td {
        border: 1px solid #ddd;
        padding: 8px;
    }
    .page-header h2 {
        font-size: 18pt;
        margin-bottom: 10px;
    }
    .breadcrumb {
        display: none;
    }
</style>
