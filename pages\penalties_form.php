<?php
// التحقق من الصلاحيات
if (!hasPermission('financial')) {
    header('Location: index.php?page=unauthorized');
    exit;
}

// تحديد ما إذا كان الإجراء إضافة أو تعديل
$isEdit = ($action == 'edit' && isset($_GET['id']) && $_GET['id'] > 0);
$penaltyId = $isEdit ? (int)$_GET['id'] : 0;

// متغيرات النموذج الافتراضية
$penalty = [
    'id' => 0,
    'container_id' => '',
    'trader_id' => '',
    'penalty_type' => '',
    'amount' => '',
    'penalty_date' => date('Y-m-d'),
    'due_date' => date('Y-m-d', strtotime('+30 days')),
    'description' => '',
    'is_paid' => 0,
    'payment_date' => '',
    'payment_reference' => '',
    'notes' => ''
];

// تهيئة مصفوفة الأخطاء
$errors = [];

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // جمع البيانات من النموذج
    $penaltyData = [
        'container_id' => !empty($_POST['container_id']) ? (int)$_POST['container_id'] : null,
        'trader_id' => (int)$_POST['trader_id'],
        'penalty_type' => trim($_POST['penalty_type']),
        'amount' => (float)str_replace(',', '', $_POST['amount_numeric']),
        'penalty_date' => $_POST['penalty_date'],
        'due_date' => $_POST['due_date'],
        'description' => trim($_POST['description']),
        'is_paid' => isset($_POST['is_paid']) ? 1 : 0,
        'payment_date' => !empty($_POST['payment_date']) ? $_POST['payment_date'] : null,
        'payment_reference' => trim($_POST['payment_reference'] ?? ''),
        'notes' => trim($_POST['notes'] ?? '')
    ];

    // التحقق من الصحة
    if (empty($penaltyData['trader_id'])) {
        $errors['trader_id'] = 'يجب اختيار التاجر';
    }
    
    if (empty($penaltyData['penalty_type'])) {
        $errors['penalty_type'] = 'يجب اختيار نوع الغرامة';
    }
    
    if ($penaltyData['amount'] <= 0) {
        $errors['amount'] = 'يجب إدخال مبلغ صحيح';
    }
    
    if (empty($penaltyData['penalty_date'])) {
        $errors['penalty_date'] = 'يجب إدخال تاريخ الغرامة';
    } elseif (!validateDate($penaltyData['penalty_date'])) {
        $errors['penalty_date'] = 'تاريخ غير صحيح';
    }
    
    if (empty($penaltyData['due_date'])) {
        $errors['due_date'] = 'يجب إدخال تاريخ الاستحقاق';
    } elseif (!validateDate($penaltyData['due_date'])) {
        $errors['due_date'] = 'تاريخ غير صحيح';
    }
    
    if (empty($penaltyData['description'])) {
        $errors['description'] = 'يجب إدخال وصف الغرامة';
    } elseif (strlen($penaltyData['description']) < 10) {
        $errors['description'] = 'الوصف يجب أن يكون 10 أحرف على الأقل';
    }
    
    if ($penaltyData['is_paid'] == 1) {
        if (empty($penaltyData['payment_date'])) {
            $errors['payment_date'] = 'يجب إدخال تاريخ الدفع';
        } elseif (!validateDate($penaltyData['payment_date'])) {
            $errors['payment_date'] = 'تاريخ غير صحيح';
        }
        
        if (empty($penaltyData['payment_reference'])) {
            $errors['payment_reference'] = 'يجب إدخال مرجع الدفع';
        }
    }

    // إذا لم تكن هناك أخطاء، حفظ البيانات
    if (empty($errors)) {
        try {
            if ($isEdit) {
                // عملية التعديل
                $stmt = $db->prepare("UPDATE penalties SET
                    container_id = :container_id,
                    trader_id = :trader_id,
                    penalty_type = :penalty_type,
                    amount = :amount,
                    penalty_date = :penalty_date,
                    due_date = :due_date,
                    description = :description,
                    is_paid = :is_paid,
                    payment_date = :payment_date,
                    payment_reference = :payment_reference,
                    notes = :notes,
                    updated_at = NOW()
                    WHERE id = :id");
                
                $penaltyData['id'] = $penaltyId;
                $stmt->execute($penaltyData);
                
                $_SESSION['success'] = 'تم تحديث بيانات الغرامة بنجاح';
            } else {
                // عملية الإضافة
                $stmt = $db->prepare("INSERT INTO penalties (
                    container_id, trader_id, penalty_type, amount,
                    penalty_date, due_date, description, is_paid,
                    payment_date, payment_reference, notes, created_at, updated_at
                ) VALUES (
                    :container_id, :trader_id, :penalty_type, :amount,
                    :penalty_date, :due_date, :description, :is_paid, 
                    :payment_date, :payment_reference, :notes, NOW(), NOW()
                )");
                
                $stmt->execute($penaltyData);
                $penaltyId = $db->lastInsertId();
                
                $_SESSION['success'] = 'تم إضافة الغرامة بنجاح';
            }

           // إعداد رسالة النجاح وإعادة التوجيه
            $redirectUrl = $isEdit ? 'index.php?page=penalties&action=edit&id='.$penaltyId : 'index.php?page=penalties';
            
            echo "<script>
                window.location.href = '$redirectUrl';
            </script>";
            
            // إغلاق اتصال قاعدة البيانات إذا كان موجوداً
            if(isset($db)) {
                $db = null;
            }
            exit();
        } catch (PDOException $e) {
            $errors['general'] = 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage();
            $penalty = array_merge($penalty, $penaltyData);
        }
    } else {
        $penalty = array_merge($penalty, $penaltyData);
    }
}

// إذا كان الإجراء تعديل، استرجاع بيانات الغرامة (فقط إذا لم يكن هناك إرسال للنموذج)
if ($isEdit && $_SERVER['REQUEST_METHOD'] !== 'POST') {
    try {
        $stmt = $db->prepare("SELECT * FROM penalties WHERE id = :id");
        $stmt->execute(['id' => $penaltyId]);
        $penaltyData = $stmt->fetch();

        if (!$penaltyData) {
            $_SESSION['error'] = 'الغرامة غير موجودة';
            header('Location: index.php?page=penalties');
            exit;
        }

        $penalty = array_merge($penalty, $penaltyData);
    } catch (PDOException $e) {
        $_SESSION['error'] = 'حدث خطأ أثناء استرجاع بيانات الغرامة: ' . $e->getMessage();
        header('Location: index.php?page=penalties');
        exit;
    }
}

// الحصول على قائمة الحاويات مع معرف التاجر
try {
    $containersStmt = $db->query("
        SELECT c.id, c.container_number, c.trader_id, c.customer_id, cu.name as customer_name, t.name as trader_name
        FROM containers c
        LEFT JOIN customers cu ON c.customer_id = cu.id
        LEFT JOIN traders t ON c.trader_id = t.id
        ORDER BY t.name, c.container_number
    ");
    $containers = $containersStmt->fetchAll();
} catch (PDOException $e) {
    $errors['general'] = 'حدث خطأ أثناء استرجاع قائمة الحاويات: ' . $e->getMessage();
    $containers = [];
}

// الحصول على قائمة التجار
try {
    $tradersStmt = $db->query("SELECT id, name FROM traders WHERE active = 1 ORDER BY name");
    $traders = $tradersStmt->fetchAll();
} catch (PDOException $e) {
    $errors['general'] = 'حدث خطأ أثناء استرجاع قائمة التجار: ' . $e->getMessage();
    $traders = [];
}

// أنواع الغرامات
$penaltyTypes = [
    'delay' => 'غرامة تأخير',
    'damage' => 'غرامة أضرار',

    'other' => 'غرامات أخرى'
];

// دالة التحقق من صحة التاريخ
function validateDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}
?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">
                    <i class="fas fa-exclamation-triangle text-warning"></i>
                    <?php echo $isEdit ? 'تعديل بيانات الغرامة' : 'إضافة غرامة جديدة'; ?>
                </h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php?page=penalties">إدارة الغرامات</a></li>
                    <li class="breadcrumb-item active"><?php echo $isEdit ? 'تعديل' : 'إضافة جديدة'; ?></li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <?php if (isset($_SESSION['success'])): ?>
        <div class="row">
            <div class="col-12">
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                    <h5><i class="icon fas fa-check"></i> نجاح!</h5>
                    <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Action Buttons -->
        <div class="row mb-3">
            <div class="col-12">
                <a href="index.php?page=penalties" class="btn btn-default">
                    <i class="fas fa-arrow-right"></i> العودة إلى القائمة
                </a>
                <?php if ($isEdit): ?>
                <a href="index.php?page=penalties&action=add" class="btn btn-success">
                    <i class="fas fa-plus-circle"></i> إضافة غرامة جديدة
                </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Error Messages -->
        <?php if (isset($errors['general'])): ?>
        <div class="row">
            <div class="col-12">
                <div class="alert alert-danger alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                    <h5><i class="icon fas fa-ban"></i> خطأ!</h5>
                    <?php echo $errors['general']; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Main Form -->
        <div class="row">
            <div class="col-12">
                <div class="card card-warning">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-<?php echo $isEdit ? 'edit' : 'plus-circle'; ?>"></i>
                            <?php echo $isEdit ? 'تعديل بيانات الغرامة' : 'بيانات الغرامة الجديدة'; ?>
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <form id="penalty-form" method="post" action="index.php?page=penalties&action=<?php echo $action; ?><?php echo $isEdit ? '&id=' . $penaltyId : ''; ?>">

                            <!-- Basic Information Section -->
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-info-circle"></i> المعلومات الأساسية
                                    </h5>
                                </div>
                            </div>

                            <div class="row">
                                <!-- Trader Selection - First -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="trader_id">
                                            <i class="fas fa-user-tie text-success"></i> التاجر
                                            <span class="text-danger">*</span>
                                        </label>
                                        <div class="searchable-select-wrapper">
                                            <select class="form-control searchable-select <?php echo isset($errors['trader_id']) ? 'is-invalid' : ''; ?>"
                                                    id="trader_id" name="trader_id" onchange="updateContainersList()" required data-placeholder="اختر التاجر أو ابحث...">
                                                <option value="">اختر التاجر أولاً</option>
                                                <?php foreach ($traders as $trader): ?>
                                                    <option value="<?php echo $trader['id']; ?>" <?php echo $penalty['trader_id'] == $trader['id'] ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($trader['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <?php if (isset($errors['trader_id'])): ?>
                                            <span class="error invalid-feedback"><?php echo $errors['trader_id']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Container Selection - Second -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="container_id">
                                            <i class="fas fa-shipping-fast text-info"></i> الحاوية
                                        </label>
                                        <div class="searchable-select-wrapper">
                                            <select class="form-control searchable-select" id="container_id" name="container_id" data-placeholder="اختر الحاوية أو ابحث...">
                                                <option value="">اختر التاجر أولاً لعرض حاوياته</option>
                                                <?php foreach ($containers as $container): ?>
                                                    <option value="<?php echo $container['id']; ?>"
                                                            data-trader-id="<?php echo $container['trader_id']; ?>"
                                                            <?php echo $penalty['container_id'] == $container['id'] ? 'selected' : ''; ?>
                                                            style="display: none;">
                                                        <?php echo htmlspecialchars($container['container_number']); ?>
                                                        <?php if ($container['customer_name']): ?>
                                                            - (<?php echo htmlspecialchars($container['customer_name']); ?>)
                                                        <?php endif; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <small class="form-text text-muted">
                                            <i class="fas fa-info-circle"></i>
                                            يجب اختيار التاجر أولاً لعرض حاوياته
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <!-- Penalty Type -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="penalty_type">
                                            <i class="fas fa-exclamation-triangle text-warning"></i> نوع الغرامة
                                            <span class="text-danger">*</span>
                                        </label>
                                        <div class="searchable-select-wrapper">
                                            <select class="form-control searchable-select <?php echo isset($errors['penalty_type']) ? 'is-invalid' : ''; ?>" id="penalty_type" name="penalty_type" required data-placeholder="اختر نوع الغرامة...">
                                                <option value="">اختر نوع الغرامة</option>
                                                <?php foreach ($penaltyTypes as $key => $value): ?>
                                                    <option value="<?php echo $key; ?>" <?php echo $penalty['penalty_type'] == $key ? 'selected' : ''; ?>>
                                                        <?php echo $value; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <?php if (isset($errors['penalty_type'])): ?>
                                            <span class="error invalid-feedback"><?php echo $errors['penalty_type']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Amount -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="amount">
                                            <i class="fas fa-money-bill-wave text-success"></i> المبلغ
                                            <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group">
                                            <input type="text" 
                                                   class="form-control <?php echo isset($errors['amount']) ? 'is-invalid' : ''; ?>" 
                                                   id="amount" 
                                                   name="amount" 
                                                   value="<?php echo !empty($penalty['amount']) ? number_format($penalty['amount'], 2) : ''; ?>" 
                                                   placeholder="أدخل المبلغ"
                                                   oninput="formatAmount(this); convertToText(this.value)" 
                                                   required>
                                            <div class="input-group-append">
                                                <span class="input-group-text"><?php echo CURRENCY_SYMBOL; ?></span>
                                            </div>
                                        </div>
                                        
                                        <!-- عرض المبلغ كتابة -->
                                        <div class="amount-written" id="amount-text" style="background-color: #e8f4fd; border: 2px solid #bee5eb; border-radius: 6px; padding: 12px; margin-top: 10px; font-size: 14px; color: #0c5460; min-height: 24px; font-weight: bold;">
                                            <?php if (!empty($penalty['amount'])): ?>
                                                <script>
                                                    document.addEventListener('DOMContentLoaded', function() {
                                                        convertToText('<?php echo $penalty['amount']; ?>');
                                                    });
                                                </script>
                                            <?php else: ?>
                                                اكتب المبلغ أعلاه ليظهر هنا كتابة...
                                            <?php endif; ?>
                                        </div>
                                        
                                        <!-- حقل مخفي للقيمة الرقمية الفعلية -->
                                        <input type="hidden" id="amount-numeric" name="amount_numeric" value="<?php echo $penalty['amount']; ?>">
                                        
                                        <?php if (isset($errors['amount'])): ?>
                                            <span class="error invalid-feedback"><?php echo $errors['amount']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Dates Section -->
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3 mt-4">
                                        <i class="fas fa-calendar-alt"></i> التواريخ
                                    </h5>
                                </div>
                            </div>

                            <div class="row">
                                <!-- Penalty Date -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="penalty_date">
                                            <i class="fas fa-calendar text-danger"></i> تاريخ الغرامة
                                            <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                            </div>
                                            <input type="date" class="form-control <?php echo isset($errors['penalty_date']) ? 'is-invalid' : ''; ?>" id="penalty_date" name="penalty_date" value="<?php echo htmlspecialchars($penalty['penalty_date']); ?>" required>
                                        </div>
                                        <?php if (isset($errors['penalty_date'])): ?>
                                            <span class="error invalid-feedback"><?php echo $errors['penalty_date']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Due Date -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="due_date">
                                            <i class="fas fa-clock text-warning"></i> تاريخ الاستحقاق
                                            <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            </div>
                                            <input type="date" class="form-control <?php echo isset($errors['due_date']) ? 'is-invalid' : ''; ?>" id="due_date" name="due_date" value="<?php echo htmlspecialchars($penalty['due_date']); ?>" required>
                                        </div>
                                        <?php if (isset($errors['due_date'])): ?>
                                            <span class="error invalid-feedback"><?php echo $errors['due_date']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Description Section -->
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3 mt-4">
                                        <i class="fas fa-file-alt"></i> التفاصيل
                                    </h5>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="description">
                                            <i class="fas fa-edit text-info"></i> وصف الغرامة
                                            <span class="text-danger">*</span>
                                        </label>
                                        <textarea class="form-control <?php echo isset($errors['description']) ? 'is-invalid' : ''; ?>" id="description" name="description" rows="4" placeholder="أدخل وصف مفصل للغرامة..." required><?php echo htmlspecialchars($penalty['description']); ?></textarea>
                                        <?php if (isset($errors['description'])): ?>
                                            <span class="error invalid-feedback"><?php echo $errors['description']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Status Section -->
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3 mt-4">
                                        <i class="fas fa-credit-card"></i> حالة الدفع
                                    </h5>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch custom-switch-off-danger custom-switch-on-success">
                                            <input type="checkbox" class="custom-control-input" id="is_paid" name="is_paid" <?php echo $penalty['is_paid'] ? 'checked' : ''; ?> onchange="togglePaymentFields()">
                                            <label class="custom-control-label" for="is_paid">
                                                <i class="fas fa-check-circle"></i> تم دفع الغرامة
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Details (Hidden by default) -->
                            <div class="payment-fields" style="display: <?php echo $penalty['is_paid'] ? 'block' : 'none'; ?>;">
                                <div class="row">
                                    <!-- Payment Date -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="payment_date">
                                                <i class="fas fa-calendar-check text-success"></i> تاريخ الدفع
                                                <?php if ($penalty['is_paid']): ?><span class="text-danger">*</span><?php endif; ?>
                                            </label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fas fa-calendar-check"></i></span>
                                                </div>
                                                <input type="date" class="form-control <?php echo isset($errors['payment_date']) ? 'is-invalid' : ''; ?>" id="payment_date" name="payment_date" value="<?php echo htmlspecialchars($penalty['payment_date'] ?: date('Y-m-d')); ?>">
                                            </div>
                                            <?php if (isset($errors['payment_date'])): ?>
                                                <span class="error invalid-feedback"><?php echo $errors['payment_date']; ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <!-- Payment Reference -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="payment_reference">
                                                <i class="fas fa-receipt text-primary"></i> مرجع الدفع
                                                <?php if ($penalty['is_paid']): ?><span class="text-danger">*</span><?php endif; ?>
                                            </label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fas fa-receipt"></i></span>
                                                </div>
                                                <input type="text" class="form-control <?php echo isset($errors['payment_reference']) ? 'is-invalid' : ''; ?>" id="payment_reference" name="payment_reference" value="<?php echo htmlspecialchars($penalty['payment_reference']); ?>" placeholder="رقم الإيصال أو المرجع">
                                            </div>
                                            <?php if (isset($errors['payment_reference'])): ?>
                                                <span class="error invalid-feedback"><?php echo $errors['payment_reference']; ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Notes Section -->
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="text-primary mb-3 mt-4">
                                        <i class="fas fa-sticky-note"></i> ملاحظات إضافية
                                    </h5>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="notes">
                                            <i class="fas fa-comment text-secondary"></i> ملاحظات
                                        </label>
                                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="أي ملاحظات إضافية..."><?php echo htmlspecialchars($penalty['notes']); ?></textarea>
                                    </div>
                                </div>
                            </div>

                        </form>
                    </div>

                    <!-- Card Footer with Action Buttons -->
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" form="penalty-form" class="btn btn-warning btn-lg">
                                    <i class="fas fa-save"></i> <?php echo $isEdit ? 'حفظ التغييرات' : 'إضافة الغرامة'; ?>
                                </button>
                                <a href="index.php?page=penalties" class="btn btn-default btn-lg">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <?php if ($isEdit): ?>
                                <a href="index.php?page=penalties&action=add" class="btn btn-success btn-lg">
                                    <i class="fas fa-plus-circle"></i> إضافة غرامة جديدة
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</section>

<script>
// دالة تنسيق المبلغ بالفواصل
function formatAmount(input) {
    // إزالة جميع الفواصل والمسافات
    let value = input.value.replace(/[,\s]/g, '');
    
    // التأكد من أن القيمة رقمية فقط
    if (!/^\d*\.?\d*$/.test(value)) {
        // إزالة الأحرف غير الرقمية
        value = value.replace(/[^\d.]/g, '');
    }
    
    // منع وجود أكثر من نقطة عشرية واحدة
    const parts = value.split('.');
    if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // تنسيق الرقم بالفواصل
    if (value) {
        const numericValue = parseFloat(value);
        if (!isNaN(numericValue)) {
            // حفظ القيمة الرقمية في الحقل المخفي
            document.getElementById('amount-numeric').value = numericValue;
            
            // تنسيق العرض بالفواصل
            const formatted = numericValue.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
            input.value = formatted;
        }
    } else {
        document.getElementById('amount-numeric').value = '';
        input.value = '';
    }
}

// دالة تحويل الرقم إلى كتابة عربية
function convertToText(value) {
    // إزالة الفواصل للمعالجة
    const numericValue = parseFloat(value.toString().replace(/,/g, ''));
    
    if (isNaN(numericValue) || numericValue === 0) {
        document.getElementById('amount-text').innerHTML = 'اكتب المبلغ أعلاه ليظهر هنا كتابة...';
        return;
    }
    
    const arabicText = numberToArabicWords(numericValue);
    document.getElementById('amount-text').innerHTML = `<i class="fas fa-coins text-success"></i> ${arabicText} دينار`;
}

// دالة تحويل الأرقام إلى كلمات عربية
function numberToArabicWords(num) {
    if (num === 0) return 'صفر';
    
    const ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
    const teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];
    const tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
    const hundreds = ['', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'];
    
    const scales = [
        { value: 1000000000, name: 'مليار', names: ['مليار', 'ملياران', 'مليارات'] },
        { value: 1000000, name: 'مليون', names: ['مليون', 'مليونان', 'ملايين'] },
        { value: 1000, name: 'ألف', names: ['ألف', 'ألفان', 'آلاف'] }
    ];
    
    function convertChunk(n) {
        let result = '';
        
        const h = Math.floor(n / 100);
        const remainder = n % 100;
        
        if (h > 0) {
            result += hundreds[h];
            if (remainder > 0) result += ' و ';
        }
        
        if (remainder >= 10 && remainder < 20) {
            result += teens[remainder - 10];
        } else {
            const t = Math.floor(remainder / 10);
            const o = remainder % 10;
            
            if (t > 0) {
                result += tens[t];
                if (o > 0) result += ' و ';
            }
            
            if (o > 0) {
                result += ones[o];
            }
        }
        
        return result;
    }
    
    function getScaleName(scale, count) {
        if (count === 1) {
            return scale.names[0];
        } else if (count === 2) {
            return scale.names[1];
        } else if (count >= 3 && count <= 10) {
            return scale.names[2];
        } else {
            return scale.names[0];
        }
    }
    
    let result = '';
    let remaining = Math.floor(num);
    
    for (let scale of scales) {
        if (remaining >= scale.value) {
            const count = Math.floor(remaining / scale.value);
            const chunkText = convertChunk(count);
            const scaleName = getScaleName(scale, count);
            
            result += chunkText + ' ' + scaleName;
            remaining = remaining % scale.value;
            
            if (remaining > 0) {
                result += ' و ';
            }
        }
    }
    
    if (remaining > 0) {
        result += convertChunk(remaining);
    }
    
    // التعامل مع الكسور العشرية
    const decimalPart = num - Math.floor(num);
    if (decimalPart > 0) {
        const cents = Math.round(decimalPart * 100);
        if (cents > 0) {
            result += ' و ' + convertChunk(cents) + ' فلس';
        }
    }
    
    return result;
}

$(document).ready(function() {
    // Initialize Select2 for dropdowns
    $('.select2').select2({
        theme: 'bootstrap4',
        placeholder: 'اختر من القائمة...',
        allowClear: true
    });

    // Initialize form validation
    $('#penalty-form').validate({
        rules: {
            customer_id: {
                required: true
            },
            penalty_type: {
                required: true
            },
            amount: {
                required: true,
                number: true,
                min: 0.01
            },
            penalty_date: {
                required: true,
                date: true
            },
            due_date: {
                required: true,
                date: true
            },
            description: {
                required: true,
                minlength: 10
            },
            payment_date: {
                required: function() {
                    return $('#is_paid').is(':checked');
                },
                date: true
            },
            payment_reference: {
                required: function() {
                    return $('#is_paid').is(':checked');
                }
            }
        },
        messages: {
            customer_id: "يرجى اختيار الشركة",
            penalty_type: "يرجى اختيار نوع الغرامة",
            amount: {
                required: "يرجى إدخال المبلغ",
                number: "يرجى إدخال رقم صحيح",
                min: "المبلغ يجب أن يكون أكبر من 0"
            },
            penalty_date: {
                required: "يرجى إدخال تاريخ الغرامة",
                date: "تاريخ غير صحيح"
            },
            due_date: {
                required: "يرجى إدخال تاريخ الاستحقاق",
                date: "تاريخ غير صحيح"
            },
            description: {
                required: "يرجى إدخال وصف الغرامة",
                minlength: "الوصف يجب أن يكون 10 أحرف على الأقل"
            },
            payment_date: {
                required: "يرجى إدخال تاريخ الدفع",
                date: "تاريخ غير صحيح"
            },
            payment_reference: "يرجى إدخال مرجع الدفع"
        },
        errorElement: 'span',
        errorPlacement: function (error, element) {
            error.addClass('invalid-feedback');
            element.closest('.form-group').append(error);
        },
        highlight: function (element, errorClass, validClass) {
            $(element).addClass('is-invalid');
        },
        unhighlight: function (element, errorClass, validClass) {
            $(element).removeClass('is-invalid');
        }
    });

    // Initialize containers list on page load
    updateContainersList();
});

// تحديث قائمة الحاويات عند تغيير التاجر
function updateContainersList() {
    const traderId = document.getElementById('trader_id').value;
    const containerSelect = document.getElementById('container_id');
    const containerOptions = containerSelect.querySelectorAll('option');

    // إعادة تعيين قائمة الحاويات
    containerSelect.value = '';

    // تحديث Searchable Select إذا كان مفعل
    if (window.SearchableSelectManager) {
        // إعادة تهيئة المكون
        const instance = window.SearchableSelectManager.instances.get(containerSelect);
        if (instance) {
            instance.close();
        }
    }

    // إخفاء جميع الخيارات ما عدا الخيار الأول
    let visibleCount = 0;
    for (let i = 1; i < containerOptions.length; i++) {
        const option = containerOptions[i];
        const optionTraderId = option.getAttribute('data-trader-id');

        if (!traderId) {
            // إذا لم يتم اختيار تاجر، إخفاء جميع الحاويات
            option.style.display = 'none';
        } else if (optionTraderId == traderId) {
            // إظهار حاويات التاجر المحدد فقط
            option.style.display = '';
            visibleCount++;
        } else {
            // إخفاء حاويات التجار الآخرين
            option.style.display = 'none';
        }
    }

    // تحديث النص الافتراضي
    const defaultOption = containerOptions[0];
    if (!traderId) {
        defaultOption.textContent = 'اختر التاجر أولاً لعرض حاوياته';
    } else if (visibleCount === 0) {
        defaultOption.textContent = 'لا توجد حاويات لهذا التاجر';
    } else {
        defaultOption.textContent = 'اختر الحاوية';
    }

    // تحديث Searchable Select إذا كان مفعل
    if (window.SearchableSelectManager) {
        const instance = window.SearchableSelectManager.instances.get(containerSelect);
        if (instance) {
            instance.populateOptions();
        }
    }
}

function togglePaymentFields() {
    const isPaid = document.getElementById('is_paid').checked;
    const paymentFields = document.querySelector('.payment-fields');

    if (paymentFields) {
        paymentFields.style.display = isPaid ? 'block' : 'none';

        // Add/remove required attribute for payment fields
        const paymentDateField = document.getElementById('payment_date');
        const paymentRefField = document.getElementById('payment_reference');

        if (isPaid) {
            paymentDateField.setAttribute('required', 'required');
            paymentRefField.setAttribute('required', 'required');
        } else {
            paymentDateField.removeAttribute('required');
            paymentRefField.removeAttribute('required');
            paymentDateField.value = '';
            paymentRefField.value = '';
        }
        
        // إعادة تحميل التحقق من الصحة
        $('#penalty-form').validate().form();
    }
}

// Auto-calculate due date (30 days after penalty date)
document.getElementById('penalty_date').addEventListener('change', function() {
    const penaltyDate = new Date(this.value);
    if (penaltyDate && !isNaN(penaltyDate.getTime())) {
        const dueDate = new Date(penaltyDate);
        dueDate.setDate(dueDate.getDate() + 30);

        const dueDateField = document.getElementById('due_date');
        if (!dueDateField.value) {
            dueDateField.value = dueDate.toISOString().split('T')[0];
        }
    }
});

// Auto-set payment date to today when payment is marked as paid
document.getElementById('is_paid').addEventListener('change', function() {
    if (this.checked) {
        const paymentDateField = document.getElementById('payment_date');
        if (!paymentDateField.value) {
            paymentDateField.value = new Date().toISOString().split('T')[0];
        }
    }
});
</script>
