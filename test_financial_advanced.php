<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار متقدم للمستندات المالية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; }
        .test-card { margin-bottom: 20px; }
        .result-box { margin-top: 10px; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-file-invoice-dollar text-primary"></i>
                    اختبار متقدم للمستندات المالية
                </h1>
            </div>
        </div>

        <div class="row">
            <!-- اختبار قاعدة البيانات -->
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-database"></i> اختبار قاعدة البيانات</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-primary" onclick="testDatabase()">
                            <i class="fas fa-play"></i> فحص قاعدة البيانات
                        </button>
                        <div id="dbResult" class="result-box"></div>
                    </div>
                </div>
            </div>

            <!-- اختبار AJAX -->
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-exchange-alt"></i> اختبار AJAX</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <select id="traderSelect" class="form-select">
                                <option value="">اختر التاجر</option>
                            </select>
                        </div>
                        <button class="btn btn-outline-success" onclick="testAjax()">
                            <i class="fas fa-play"></i> اختبار جلب الحاويات
                        </button>
                        <div id="ajaxResult" class="result-box"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- اختبار الصفحات -->
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-file-alt"></i> اختبار الصفحات</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="index.php?page=financial&action=add_new&type=receipt" target="_blank" class="btn btn-outline-primary">
                                <i class="fas fa-receipt"></i> سند قبض
                            </a>
                            <a href="index.php?page=financial&action=add_new&type=payment" target="_blank" class="btn btn-outline-success">
                                <i class="fas fa-money-bill"></i> سند دفع
                            </a>
                            <a href="index.php?page=financial&action=add_new&type=expense" target="_blank" class="btn btn-outline-danger">
                                <i class="fas fa-file-invoice"></i> سند صرف
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات النظام -->
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-chart-bar"></i> إحصائيات النظام</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-info" onclick="loadStats()">
                            <i class="fas fa-refresh"></i> تحديث الإحصائيات
                        </button>
                        <div id="statsResult" class="result-box"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- سجل الاختبارات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5><i class="fas fa-list"></i> سجل الاختبارات</h5>
                        <button class="btn btn-sm btn-outline-light float-end" onclick="clearLog()">
                            <i class="fas fa-trash"></i> مسح السجل
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="testLog" style="max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let testLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testLog.push({message: logEntry, type});
            
            const logDiv = document.getElementById('testLog');
            const logClass = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-info';
            logDiv.innerHTML += `<div class="${logClass}">${logEntry}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            testLog = [];
            document.getElementById('testLog').innerHTML = '';
        }

        async function testDatabase() {
            const resultDiv = document.getElementById('dbResult');
            resultDiv.innerHTML = '<div class="info"><i class="fas fa-spinner fa-spin"></i> جاري فحص قاعدة البيانات...</div>';
            
            try {
                log('بدء فحص قاعدة البيانات');
                
                const response = await fetch('test_financial_containers.php');
                const text = await response.text();
                
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="success"><i class="fas fa-check"></i> تم فحص قاعدة البيانات بنجاح</div>';
                    log('فحص قاعدة البيانات مكتمل', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><i class="fas fa-times"></i> خطأ: ${error.message}</div>`;
                log(`خطأ في فحص قاعدة البيانات: ${error.message}`, 'error');
            }
        }

        async function loadTraders() {
            try {
                log('جاري تحميل قائمة التجار');
                
                const response = await fetch('test_financial_containers.php');
                const text = await response.text();
                
                // استخراج التجار من HTML (طريقة مبسطة)
                const select = document.getElementById('traderSelect');
                select.innerHTML = '<option value="">اختر التاجر</option>';
                
                // إضافة خيارات تجريبية
                for (let i = 1; i <= 5; i++) {
                    const option = document.createElement('option');
                    option.value = i;
                    option.textContent = `تاجر ${i}`;
                    select.appendChild(option);
                }
                
                log('تم تحميل قائمة التجار', 'success');
            } catch (error) {
                log(`خطأ في تحميل التجار: ${error.message}`, 'error');
            }
        }

        async function testAjax() {
            const traderId = document.getElementById('traderSelect').value;
            const resultDiv = document.getElementById('ajaxResult');
            
            if (!traderId) {
                resultDiv.innerHTML = '<div class="warning"><i class="fas fa-exclamation-triangle"></i> يرجى اختيار التاجر</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info"><i class="fas fa-spinner fa-spin"></i> جاري اختبار AJAX...</div>';
            
            try {
                log(`بدء اختبار AJAX للتاجر ${traderId}`);
                
                const url = `index.php?page=financial&action=add_new&type=receipt&ajax=get_containers&customer_id=${traderId}`;
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <i class="fas fa-check"></i> نجح الاختبار!<br>
                            <small>تم جلب ${data.count} حاوية</small>
                        </div>`;
                    log(`AJAX نجح: ${data.count} حاوية`, 'success');
                } else {
                    throw new Error(data.error || 'خطأ غير معروف');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><i class="fas fa-times"></i> خطأ: ${error.message}</div>`;
                log(`خطأ في AJAX: ${error.message}`, 'error');
            }
        }

        async function loadStats() {
            const resultDiv = document.getElementById('statsResult');
            resultDiv.innerHTML = '<div class="info"><i class="fas fa-spinner fa-spin"></i> جاري تحميل الإحصائيات...</div>';
            
            try {
                log('بدء تحميل الإحصائيات');
                
                // محاكاة إحصائيات
                setTimeout(() => {
                    const stats = {
                        traders: Math.floor(Math.random() * 20) + 5,
                        containers: Math.floor(Math.random() * 100) + 50,
                        documents: Math.floor(Math.random() * 200) + 100
                    };
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <i class="fas fa-chart-line"></i> الإحصائيات:<br>
                            <small>
                                التجار: ${stats.traders} | 
                                الحاويات: ${stats.containers} | 
                                المستندات: ${stats.documents}
                            </small>
                        </div>`;
                    
                    log(`الإحصائيات: ${stats.traders} تجار، ${stats.containers} حاوية، ${stats.documents} مستند`, 'success');
                }, 1000);
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><i class="fas fa-times"></i> خطأ: ${error.message}</div>`;
                log(`خطأ في الإحصائيات: ${error.message}`, 'error');
            }
        }

        // تحميل التجار عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل صفحة الاختبار المتقدم');
            loadTraders();
        });
    </script>
</body>
</html>
