<?php
require_once 'includes/session.php';
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';
require_once 'includes/db.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn() && basename($_SERVER['PHP_SELF']) != 'login.php') {
    header('Location: login.php');
    exit;
}

// كشف الهاتف المحمول المحسن
function isMobileDevice() {
    // $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    // // قائمة محسنة لكشف الأجهزة المحمولة
    // $mobileKeywords = [
    //     'Mobile', 'Android', 'iPhone', 'iPad', 'iPod', 'BlackBerry',
    //     'IEMobile', 'Opera Mini', 'webOS', 'Windows Phone', 'Kindle',
    //     'Silk', 'Mobile Safari', 'Opera Mobi', 'Opera Mobile'
    // ];

    // foreach ($mobileKeywords as $keyword) {
    //     if (stripos($userAgent, $keyword) !== false) {
    //         return true;
    //     }
    // }

    // // فحص إضافي للشاشات الصغيرة
    // if (isset($_COOKIE['screen_width']) && $_COOKIE['screen_width'] <= 768) {
    //     return true;
    // }

    return false;
}

// فحص إذا كان المستخدم يستخدم جهاز محمول
$isMobile = isMobileDevice();

// إذا كان جهاز محمول وليس في مجلد mobile-template، قم بإعادة التوجيه
// if ($isMobile && !isset($_GET['desktop']) && strpos($_SERVER['REQUEST_URI'], 'mobile-template') === false) {
//     // بناء URL الجديد مع الحفاظ على المعاملات
//     $currentParams = $_GET;
//     $queryString = http_build_query($currentParams);
//     $redirectUrl = 'mobile-template/';

//     if (!empty($queryString)) {
//         $redirectUrl .= '?' . $queryString;
//     }

//     // إعادة التوجيه إلى النسخة المحمولة
//     header('Location: ' . $redirectUrl);
//     exit;
// }

// إزالة كود معالجة نموذج التجار (POST) من index.php وإرجاعه إلى traders.php

// إذا لم يكن جهاز محمول أو طلب المستخدم النسخة العادية
$page = isset($_GET['page']) ? $_GET['page'] : 'dashboard';
$action = isset($_GET['action']) ? $_GET['action'] : '';
$allowed_pages = ['dashboard', 'containers', 'customers', 'traders', 'traders', 'trader_statement', 'trader_statement_select', 'financial', 'penalties','wages', 'reports', 'users', 'settings', 'profile', 'notifications', 'unauthorized', 'drivers', 'transfers', 'transfer_print', 'transfer_print_simple', 'transfers_report_print'];

if (!in_array($page, $allowed_pages)) {
    $page = 'dashboard';
}

// التحقق من الصلاحيات (تجاهل فحص الصلاحيات لصفحات الطباعة)
$printPages = ['transfer_print', 'transfer_print_simple', 'transfers_report_print'];
if (!in_array($page, $printPages) && !hasPermission($page)) {
    $page = 'unauthorized';
}

// استخدام القالب العادي (AdminLTE)
include 'includes/header.php';

// معالجة خاصة لصفحات السائقين والتحويلات
if ($page === 'drivers' && in_array($action, ['add', 'edit', 'view'])) {
    include 'pages/drivers_form.php';
} elseif ($page === 'transfers' && in_array($action, ['add', 'edit', 'view'])) {
    include 'pages/transfers_form.php';
} else {
    include 'pages/' . $page . '.php';
}

include 'includes/footer.php';
?>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- App JS -->
<script src="assets/js/app.js"></script>


