<?php
/**
 * تطبيق البحث في القوائم المنسدلة على جميع صفحات النظام
 * 
 * هذا الملف يحتوي على دالة لتحويل أي قائمة منسدلة عادية إلى قائمة قابلة للبحث
 * يمكن استخدامه في أي صفحة في النظام
 */

/**
 * تحويل جميع القوائم المنسدلة في الصفحة إلى قوائم قابلة للبحث
 * 
 * @param array $excludeIds معرفات العناصر المراد استثناؤها
 * @param array $includeOnly معرفات العناصر المراد تضمينها فقط
 */
function applySearchableSelect($excludeIds = [], $includeOnly = []) {
    ?>
    <!-- Searchable Select CSS -->
    <link rel="stylesheet" href="assets/css/searchable-select.css">
    
    <!-- Searchable Select JavaScript -->
    <script src="assets/js/searchable-select.js"></script>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // قائمة العناصر المستثناة
        const excludeIds = <?php echo json_encode($excludeIds); ?>;
        const includeOnly = <?php echo json_encode($includeOnly); ?>;
        
        // العثور على جميع عناصر select
        let selects = document.querySelectorAll('select');
        
        // تطبيق الفلترة
        if (includeOnly.length > 0) {
            selects = Array.from(selects).filter(select => 
                includeOnly.includes(select.id) || includeOnly.includes(select.name)
            );
        } else if (excludeIds.length > 0) {
            selects = Array.from(selects).filter(select => 
                !excludeIds.includes(select.id) && !excludeIds.includes(select.name)
            );
        }
        
        // تطبيق البحث على كل عنصر
        selects.forEach(select => {
            // تخطي العناصر التي تحتوي على عدد قليل من الخيارات
            if (select.options.length <= 3) return;
            
            // تخطي العناصر المخفية
            if (select.style.display === 'none' || select.hidden) return;
            
            // إضافة الكلاسات المطلوبة
            select.classList.add('searchable-select');
            
            // إنشاء wrapper إذا لم يكن موجوداً
            if (!select.closest('.searchable-select-wrapper')) {
                const wrapper = document.createElement('div');
                wrapper.className = 'searchable-select-wrapper';
                select.parentNode.insertBefore(wrapper, select);
                wrapper.appendChild(select);
            }
            
            // إضافة placeholder إذا لم يكن موجوداً
            if (!select.dataset.placeholder) {
                const firstOption = select.querySelector('option[value=""]');
                if (firstOption) {
                    select.dataset.placeholder = firstOption.textContent + ' أو ابحث...';
                } else {
                    select.dataset.placeholder = 'ابحث...';
                }
            }
        });
        
        // تهيئة المكون
        if (window.SearchableSelectManager) {
            window.SearchableSelectManager.initializeAll();
        }
    });
    </script>
    <?php
}

/**
 * تطبيق البحث على عنصر محدد
 * 
 * @param string $selector CSS selector للعنصر
 * @param string $placeholder النص التوضيحي
 */
function applySearchableSelectToElement($selector, $placeholder = 'ابحث...') {
    ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const element = document.querySelector('<?php echo addslashes($selector); ?>');
        if (element) {
            element.classList.add('searchable-select');
            element.dataset.placeholder = '<?php echo addslashes($placeholder); ?>';
            
            // إنشاء wrapper إذا لم يكن موجوداً
            if (!element.closest('.searchable-select-wrapper')) {
                const wrapper = document.createElement('div');
                wrapper.className = 'searchable-select-wrapper';
                element.parentNode.insertBefore(wrapper, element);
                wrapper.appendChild(element);
            }
            
            // تهيئة المكون
            if (window.SearchableSelectManager) {
                window.SearchableSelectManager.initialize(element);
            }
        }
    });
    </script>
    <?php
}

/**
 * إنشاء قائمة منسدلة قابلة للبحث من البداية
 * 
 * @param string $name اسم الحقل
 * @param array $options الخيارات المتاحة
 * @param string $selected القيمة المحددة
 * @param array $attributes خصائص إضافية
 * @return string HTML للقائمة المنسدلة
 */
function createSearchableSelect($name, $options = [], $selected = '', $attributes = []) {
    $id = $attributes['id'] ?? $name;
    $class = $attributes['class'] ?? 'form-control';
    $placeholder = $attributes['placeholder'] ?? 'اختر خياراً أو ابحث...';
    $required = isset($attributes['required']) ? 'required' : '';
    
    $html = '<div class="searchable-select-wrapper">';
    $html .= '<select name="' . htmlspecialchars($name) . '" ';
    $html .= 'id="' . htmlspecialchars($id) . '" ';
    $html .= 'class="searchable-select ' . htmlspecialchars($class) . '" ';
    $html .= 'data-placeholder="' . htmlspecialchars($placeholder) . '" ';
    $html .= $required . '>';
    
    // إضافة خيار فارغ إذا لم يكن الحقل مطلوباً
    if (!isset($attributes['required'])) {
        $html .= '<option value="">اختر خياراً</option>';
    }
    
    // إضافة الخيارات
    foreach ($options as $value => $text) {
        $isSelected = ($value == $selected) ? 'selected' : '';
        $html .= '<option value="' . htmlspecialchars($value) . '" ' . $isSelected . '>';
        $html .= htmlspecialchars($text);
        $html .= '</option>';
    }
    
    $html .= '</select>';
    $html .= '</div>';
    
    return $html;
}

/**
 * تحميل البيانات من قاعدة البيانات لإنشاء قائمة منسدلة
 * 
 * @param PDO $db اتصال قاعدة البيانات
 * @param string $table اسم الجدول
 * @param string $valueField حقل القيمة
 * @param string $textField حقل النص
 * @param string $whereClause شرط WHERE (اختياري)
 * @param array $params معاملات الاستعلام
 * @return array مصفوفة الخيارات
 */
function loadSelectOptions($db, $table, $valueField, $textField, $whereClause = '', $params = []) {
    try {
        $sql = "SELECT {$valueField}, {$textField} FROM {$table}";
        if ($whereClause) {
            $sql .= " WHERE {$whereClause}";
        }
        $sql .= " ORDER BY {$textField}";
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $options = [];
        foreach ($results as $row) {
            $options[$row[$valueField]] = $row[$textField];
        }
        
        return $options;
    } catch (PDOException $e) {
        error_log("Error loading select options: " . $e->getMessage());
        return [];
    }
}

/**
 * إنشاء قائمة منسدلة للتجار
 * 
 * @param PDO $db اتصال قاعدة البيانات
 * @param string $name اسم الحقل
 * @param string $selected القيمة المحددة
 * @param array $attributes خصائص إضافية
 * @return string HTML للقائمة المنسدلة
 */
function createTradersSelect($db, $name = 'trader_id', $selected = '', $attributes = []) {
    $options = loadSelectOptions($db, 'traders', 'id', 'name', 'active = 1');
    $attributes['placeholder'] = $attributes['placeholder'] ?? 'اختر التاجر أو ابحث...';
    return createSearchableSelect($name, $options, $selected, $attributes);
}

/**
 * إنشاء قائمة منسدلة للشركات
 * 
 * @param PDO $db اتصال قاعدة البيانات
 * @param string $name اسم الحقل
 * @param string $selected القيمة المحددة
 * @param array $attributes خصائص إضافية
 * @return string HTML للقائمة المنسدلة
 */
function createCustomersSelect($db, $name = 'customer_id', $selected = '', $attributes = []) {
    $options = loadSelectOptions($db, 'customers', 'id', 'name', 'active = 1');
    $attributes['placeholder'] = $attributes['placeholder'] ?? 'اختر الشركة أو ابحث...';
    return createSearchableSelect($name, $options, $selected, $attributes);
}

/**
 * إنشاء قائمة منسدلة للسائقين
 * 
 * @param PDO $db اتصال قاعدة البيانات
 * @param string $name اسم الحقل
 * @param string $selected القيمة المحددة
 * @param array $attributes خصائص إضافية
 * @return string HTML للقائمة المنسدلة
 */
function createDriversSelect($db, $name = 'driver_id', $selected = '', $attributes = []) {
    $options = loadSelectOptions($db, 'drivers', 'id', 'driver_name', 'status = "active"');
    $attributes['placeholder'] = $attributes['placeholder'] ?? 'اختر السائق أو ابحث...';
    return createSearchableSelect($name, $options, $selected, $attributes);
}

/**
 * إنشاء قائمة منسدلة للمستخدمين
 * 
 * @param PDO $db اتصال قاعدة البيانات
 * @param string $name اسم الحقل
 * @param string $selected القيمة المحددة
 * @param array $attributes خصائص إضافية
 * @return string HTML للقائمة المنسدلة
 */
function createUsersSelect($db, $name = 'user_id', $selected = '', $attributes = []) {
    $options = loadSelectOptions($db, 'users', 'id', 'full_name', 'active = 1');
    $attributes['placeholder'] = $attributes['placeholder'] ?? 'اختر المستخدم أو ابحث...';
    return createSearchableSelect($name, $options, $selected, $attributes);
}

/**
 * تطبيق البحث على صفحة معينة بناءً على اسم الصفحة
 * 
 * @param string $pageName اسم الصفحة
 */
function applySearchableSelectByPage($pageName) {
    $pageConfigs = [
        'containers_form' => [
            'include' => ['customer_id', 'trader_id', 'container_type', 'status'],
            'exclude' => []
        ],
        'financial_add_new' => [
            'include' => ['customer_id', 'document_type', 'payment_method'],
            'exclude' => []
        ],
        'transfers_form' => [
            'include' => ['trader_id', 'driver_id', 'status'],
            'exclude' => []
        ],
        'penalties_form' => [
            'include' => ['customer_id', 'penalty_type'],
            'exclude' => []
        ]
    ];
    
    $config = $pageConfigs[$pageName] ?? ['include' => [], 'exclude' => []];
    applySearchableSelect($config['exclude'], $config['include']);
}

// تطبيق تلقائي إذا تم استدعاء الملف مباشرة
if (basename($_SERVER['PHP_SELF']) !== 'apply_searchable_select.php') {
    // تحديد اسم الصفحة الحالية
    $currentPage = basename($_SERVER['PHP_SELF'], '.php');
    
    // تطبيق البحث بناءً على الصفحة
    applySearchableSelectByPage($currentPage);
}
?>
