      </div><!-- /.container-fluid -->
    </div>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->

  <!-- Main Footer -->
  <footer class="main-footer">
    <!-- To the right -->
    <div class="float-left d-none d-sm-inline">
      الإصدار <?php echo defined('APP_VERSION') ? APP_VERSION : '1.0'; ?>
    </div>
    <!-- Default to the left -->
    <strong>&copy; <?php echo date('Y'); ?> <a href="index.php"><?php echo APP_NAME; ?></a>.</strong>
    جميع الحقوق محفوظة.
  </footer>

</div>
<!-- ./wrapper -->

<!-- REQUIRED SCRIPTS -->

<!-- jQuery -->
<script src="assets/adminlte/plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="assets/adminlte/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE App -->
<script src="assets/adminlte/dist/js/adminlte.min.js"></script>

<!-- Custom JS -->
<script src="assets/js/app.js"></script>
<!-- Sidebar Toggle JS -->
<script src="assets/js/sidebar-toggle.js"></script>
<!-- Mobile PWA JS -->
<script src="assets/js/mobile.js"></script>
<!-- Searchable Select JS -->
<script src="assets/js/searchable-select.js"></script>

    <?php if ($page == 'containers' && in_array($action, ['add', 'edit'])): ?>
    <!-- Containers Form JS -->
    <script src="assets/js/containers.js"></script>
    <?php endif; ?>

    <?php if ($page == 'financial' && in_array($action, ['add', 'edit'])): ?>
    <!-- Financial Form JS -->
    <script src="assets/js/financial.js"></script>
    <?php endif; ?>

    <!-- PWA Service Worker -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful with scope: ', registration.scope);
                    })
                    .catch(function(error) {
                        console.log('ServiceWorker registration failed: ', error);
                    });
            });
        }
    </script>
</body>
</html>
