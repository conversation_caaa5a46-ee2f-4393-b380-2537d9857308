<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار جلب حاويات التاجر في المستندات المالية</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { color: blue; background: #d1ecf1; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
        .btn { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
    </style>
</head>
<body>
    <h1>اختبار جلب حاويات التاجر في المستندات المالية</h1>
    
    <?php
    require_once 'config/config.php';
    
    try {
        $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='test-section'>";
        echo "<h3>فحص هيكل جدول financial_documents</h3>";
        
        // فحص هيكل الجدول
        $stmt = $db->query("DESCRIBE financial_documents");
        $columns = $stmt->fetchAll();
        
        echo "<table>";
        echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>فحص القيود الخارجية</h3>";
        
        // فحص القيود الخارجية
        $stmt = $db->query("
            SELECT 
                CONSTRAINT_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_NAME = 'financial_documents' 
            AND TABLE_SCHEMA = '" . DB_NAME . "'
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        $constraints = $stmt->fetchAll();
        
        if (count($constraints) > 0) {
            echo "<table>";
            echo "<tr><th>اسم القيد</th><th>العمود</th><th>الجدول المرجعي</th><th>العمود المرجعي</th></tr>";
            foreach ($constraints as $constraint) {
                echo "<tr>";
                echo "<td>" . $constraint['CONSTRAINT_NAME'] . "</td>";
                echo "<td>" . $constraint['COLUMN_NAME'] . "</td>";
                echo "<td>" . $constraint['REFERENCED_TABLE_NAME'] . "</td>";
                echo "<td>" . $constraint['REFERENCED_COLUMN_NAME'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='info'>لا توجد قيود خارجية</div>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>فحص التجار والحاويات</h3>";
        
        // جلب التجار
        $stmt = $db->query("SELECT id, name FROM traders ORDER BY name LIMIT 5");
        $traders = $stmt->fetchAll();
        
        if (count($traders) > 0) {
            echo "<div class='success'>✓ تم العثور على " . count($traders) . " تجار</div>";
            echo "<table>";
            echo "<tr><th>معرف التاجر</th><th>اسم التاجر</th><th>عدد الحاويات</th></tr>";
            
            foreach ($traders as $trader) {
                // عدد حاويات كل تاجر
                $containerStmt = $db->prepare("SELECT COUNT(*) as count FROM containers WHERE trader_id = ?");
                $containerStmt->execute([$trader['id']]);
                $containerCount = $containerStmt->fetch()['count'];
                
                echo "<tr>";
                echo "<td>" . $trader['id'] . "</td>";
                echo "<td>" . htmlspecialchars($trader['name']) . "</td>";
                echo "<td>" . $containerCount . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='error'>لا توجد تجار في النظام</div>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار AJAX endpoint</h3>";
        
        if (count($traders) > 0) {
            $testTrader = $traders[0];
            $testTraderId = $testTrader['id'];
            
            echo "<div class='info'>اختبار مع التاجر: " . htmlspecialchars($testTrader['name']) . " (ID: $testTraderId)</div>";
            
            // اختبار مباشر للـ endpoint
            $testUrl = "index.php?page=financial&action=add_new&type=receipt&ajax=get_containers&customer_id=" . $testTraderId;
            
            echo "<div class='info'>رابط الاختبار: $testUrl</div>";
            
            echo "<button onclick='testAjaxEndpoint($testTraderId)' class='btn'>اختبار AJAX</button>";
            echo "<div id='ajaxResult'></div>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار استعلام الحاويات مباشرة</h3>";
        
        if (count($traders) > 0) {
            $testTraderId = $traders[0]['id'];
            
            // اختبار الاستعلام مباشرة
            $stmt = $db->prepare("
                SELECT id, container_number, entry_date, exit_date, purchase_price, selling_price, 
                       content, status, notes, created_at
                FROM containers 
                WHERE trader_id = ? AND status != 'cancelled'
                ORDER BY entry_date DESC
                LIMIT 5
            ");
            $stmt->execute([$testTraderId]);
            $containers = $stmt->fetchAll();
            
            if (count($containers) > 0) {
                echo "<div class='success'>✓ تم العثور على " . count($containers) . " حاوية للتاجر</div>";
                echo "<table>";
                echo "<tr><th>معرف الحاوية</th><th>رقم الحاوية</th><th>المحتوى</th><th>الحالة</th><th>تاريخ الدخول</th></tr>";
                
                foreach ($containers as $container) {
                    echo "<tr>";
                    echo "<td>" . $container['id'] . "</td>";
                    echo "<td>" . htmlspecialchars($container['container_number']) . "</td>";
                    echo "<td>" . htmlspecialchars($container['content'] ?? 'غير محدد') . "</td>";
                    echo "<td>" . $container['status'] . "</td>";
                    echo "<td>" . $container['entry_date'] . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<div class='error'>لا توجد حاويات للتاجر المحدد</div>";
            }
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار صفحة إضافة المستندات المالية</h3>";
        
        echo "<div class='info'>روابط الاختبار:</div>";
        echo "<p><a href='index.php?page=financial&action=add_new&type=receipt' target='_blank' class='btn'>إضافة سند قبض</a></p>";
        echo "<p><a href='index.php?page=financial&action=add_new&type=payment' target='_blank' class='btn'>إضافة سند دفع</a></p>";
        echo "<p><a href='index.php?page=financial&action=add_new&type=expense' target='_blank' class='btn'>إضافة سند صرف</a></p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
    }
    ?>
    
    <script>
    function testAjaxEndpoint(traderId) {
        const resultDiv = document.getElementById('ajaxResult');
        resultDiv.innerHTML = '<div style="color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px;">جاري اختبار AJAX...</div>';
        
        const url = `index.php?page=financial&action=add_new&type=receipt&ajax=get_containers&customer_id=${traderId}`;
        
        fetch(url)
            .then(response => response.json())
            .then(data => {
                console.log('AJAX Response:', data);
                
                if (data.success) {
                    let html = `<div style="color: green; background: #d4edda; padding: 10px; border-radius: 5px;">✓ نجح الاختبار! تم جلب ${data.count} حاوية</div>`;
                    
                    if (data.containers && data.containers.length > 0) {
                        html += '<table style="margin-top: 10px; width: 100%; border-collapse: collapse;">';
                        html += '<tr style="background: #f2f2f2;"><th style="border: 1px solid #ddd; padding: 8px;">رقم الحاوية</th><th style="border: 1px solid #ddd; padding: 8px;">المحتوى</th><th style="border: 1px solid #ddd; padding: 8px;">الحالة</th></tr>';
                        
                        data.containers.slice(0, 3).forEach(container => {
                            html += `<tr>
                                <td style="border: 1px solid #ddd; padding: 8px;">${container.container_number}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${container.content || 'غير محدد'}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${container.status}</td>
                            </tr>`;
                        });
                        
                        html += '</table>';
                        
                        if (data.containers.length > 3) {
                            html += `<div style="color: #666; margin-top: 5px;">... و ${data.containers.length - 3} حاوية أخرى</div>`;
                        }
                    }
                    
                    // إضافة معلومات إضافية للتشخيص
                    html += `<div style="margin-top: 10px; font-size: 12px; color: #666;">
                        <strong>معلومات التشخيص:</strong><br>
                        - معرف التاجر: ${data.trader_id}<br>
                        - عدد الحاويات: ${data.count}<br>
                        - الاستعلام: ${data.sql}<br>
                        - المعاملات: ${JSON.stringify(data.query_params)}
                    </div>`;
                    
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div style="color: red; background: #f8d7da; padding: 10px; border-radius: 5px;">❌ خطأ: ${data.error}</div>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = `<div style="color: red; background: #f8d7da; padding: 10px; border-radius: 5px;">❌ خطأ في الاتصال: ${error.message}</div>`;
            });
    }
    </script>
</body>
</html>
