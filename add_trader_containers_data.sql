-- إضافة عمود trader_id إلى جدول containers إذا لم يكن موجوداً
ALTER TABLE containers ADD COLUMN IF NOT EXISTS trader_id INT(11) DEFAULT NULL AFTER customer_id;

-- إضافة فهرس لعمود trader_id
ALTER TABLE containers ADD INDEX IF NOT EXISTS idx_trader_id (trader_id);

-- نسخ البيانات من customer_id إلى trader_id إذا كان trader_id فارغ
UPDATE containers 
SET trader_id = customer_id 
WHERE trader_id IS NULL AND customer_id IS NOT NULL;

-- إضافة بيانات تجريبية للحاويات مع trader_id
INSERT IGNORE INTO containers 
(container_number, trader_id, customer_id, entry_date, status, container_type, content, selling_price, purchase_price) 
VALUES
-- حاويات للتاجر الأول
('CONT-TR1-001', 1, 1, '2024-01-15', 'pending', '20 قدم', 'أجهزة إلكترونية', 1200000, 1000000),
('CONT-TR1-002', 1, 1, '2024-01-16', 'pending', '40 قدم', 'قطع غيار سيارات', 1800000, 1500000),
('CONT-TR1-003', 1, 1, '2024-01-17', 'pending', '20 قدم', 'مواد كيميائية', 900000, 750000),
('CONT-TR1-004', 1, 1, '2024-01-18', 'pending', '40 قدم', 'منسوجات', 1500000, 1200000),
('CONT-TR1-005', 1, 1, '2024-01-19', 'pending', '20 قدم', 'مواد غذائية', 800000, 650000),

-- حاويات للتاجر الثاني
('CONT-TR2-001', 2, 2, '2024-01-20', 'pending', '40 قدم', 'أثاث منزلي', 2000000, 1600000),
('CONT-TR2-002', 2, 2, '2024-01-21', 'pending', '20 قدم', 'أدوات كهربائية', 1100000, 900000),
('CONT-TR2-003', 2, 2, '2024-01-22', 'pending', '40 قدم', 'مواد بناء', 1700000, 1400000),
('CONT-TR2-004', 2, 2, '2024-01-23', 'pending', '20 قدم', 'ألعاب أطفال', 600000, 500000),
('CONT-TR2-005', 2, 2, '2024-01-24', 'pending', '40 قدم', 'أجهزة طبية', 2500000, 2000000),

-- حاويات للتاجر الثالث
('CONT-TR3-001', 3, 3, '2024-01-25', 'pending', '20 قدم', 'كتب ومجلات', 400000, 350000),
('CONT-TR3-002', 3, 3, '2024-01-26', 'pending', '40 قدم', 'أدوات رياضية', 1300000, 1100000),
('CONT-TR3-003', 3, 3, '2024-01-27', 'pending', '20 قدم', 'مستحضرات تجميل', 700000, 600000),
('CONT-TR3-004', 3, 3, '2024-01-28', 'pending', '40 قدم', 'أجهزة كمبيوتر', 2200000, 1800000),
('CONT-TR3-005', 3, 3, '2024-01-29', 'pending', '20 قدم', 'ملابس', 500000, 400000),

-- حاويات إضافية للتاجر الأول (لاختبار الفلترة)
('CONT-TR1-006', 1, 1, '2024-02-01', 'pending', '40 قدم', 'معدات صناعية', 3000000, 2500000),
('CONT-TR1-007', 1, 1, '2024-02-02', 'pending', '20 قدم', 'أدوات مكتبية', 300000, 250000),
('CONT-TR1-008', 1, 1, '2024-02-03', 'pending', '40 قدم', 'مواد خام', 1600000, 1300000),

-- حاويات إضافية للتاجر الثاني
('CONT-TR2-006', 2, 2, '2024-02-04', 'pending', '20 قدم', 'عطور', 800000, 650000),
('CONT-TR2-007', 2, 2, '2024-02-05', 'pending', '40 قدم', 'سيارات', 5000000, 4000000),
('CONT-TR2-008', 2, 2, '2024-02-06', 'pending', '20 قدم', 'إكسسوارات', 450000, 380000),

-- حاويات بحالات مختلفة للاختبار
('CONT-TR1-009', 1, 1, '2024-02-07', 'in_progress', '20 قدم', 'قيد التنفيذ', 700000, 600000),
('CONT-TR2-009', 2, 2, '2024-02-08', 'completed', '40 قدم', 'مكتمل', 1400000, 1200000),
('CONT-TR3-006', 3, 3, '2024-02-09', 'pending', '20 قدم', 'جديد للاختبار', 550000, 450000);

-- تحديث إحصائيات
UPDATE traders t 
SET notes = CONCAT(
    COALESCE(notes, ''), 
    ' - عدد الحاويات: ', 
    (SELECT COUNT(*) FROM containers c WHERE c.trader_id = t.id)
) 
WHERE id IN (1, 2, 3);

-- إنشاء view مفيد لعرض الحاويات مع بيانات التاجر
CREATE OR REPLACE VIEW containers_with_trader AS
SELECT 
    c.id,
    c.container_number,
    c.entry_date,
    c.exit_date,
    c.status,
    c.container_type,
    c.content,
    c.selling_price,
    c.purchase_price,
    c.notes as container_notes,
    t.name as trader_name,
    t.contact_person as trader_contact,
    t.phone as trader_phone,
    cu.name as customer_name
FROM containers c
LEFT JOIN traders t ON c.trader_id = t.id
LEFT JOIN customers cu ON c.customer_id = cu.id
ORDER BY c.entry_date DESC, c.container_number;

-- إحصائيات سريعة
SELECT 
    'إحصائيات الحاويات حسب التاجر' as report_title,
    '' as separator;

SELECT 
    t.name as 'اسم التاجر',
    COUNT(c.id) as 'إجمالي الحاويات',
    SUM(CASE WHEN c.status = 'pending' THEN 1 ELSE 0 END) as 'قيد الانتظار',
    SUM(CASE WHEN c.status = 'in_progress' THEN 1 ELSE 0 END) as 'قيد التنفيذ',
    SUM(CASE WHEN c.status = 'completed' THEN 1 ELSE 0 END) as 'مكتمل',
    SUM(c.selling_price) as 'إجمالي القيمة'
FROM traders t
LEFT JOIN containers c ON t.id = c.trader_id
GROUP BY t.id, t.name
ORDER BY COUNT(c.id) DESC;

-- فحص البيانات
SELECT 
    'فحص البيانات' as check_title,
    '' as separator;

SELECT 
    COUNT(*) as 'إجمالي الحاويات',
    SUM(CASE WHEN trader_id IS NOT NULL THEN 1 ELSE 0 END) as 'مرتبطة بتاجر',
    SUM(CASE WHEN trader_id IS NULL THEN 1 ELSE 0 END) as 'غير مرتبطة بتاجر'
FROM containers;
