# إصلاح مشكلة التنقل لكشف حساب التاجر

## المشكلة الأصلية
عند الضغط على "كشف حساب تاجر" في الـ sidebar، كان يحول المستخدم إلى:
```
http://localhost/ccis_appis/index.php?page=trader_statement
```
ولكن يعرض لوحة التحكم بدلاً من صفحة كشف الحساب.

## سبب المشكلة
صفحة `trader_statement.php` تتطلب معرف التاجر (`trader_id`) كمعامل إجباري. عندما يتم الوصول إليها بدون هذا المعامل، تقوم بإعادة توجيه المستخدم إلى الصفحة الرئيسية.

## الحل المطبق

### 1. إنشاء صفحة اختيار التاجر (`pages/trader_statement_select.php`)
صفحة وسيطة تسمح للمستخدم باختيار التاجر قبل عرض كشف الحساب.

#### الميزات:
- **اختيار سريع**: قائمة منسدلة لجميع التجار النشطين
- **فلترة التاريخ**: إمكانية تحديد الفترة الزمنية
- **عرض شبكي**: بطاقات تفاعلية لكل تاجر مع إحصائياته
- **روابط مباشرة**: أزرار لعرض كشف الحساب والطباعة

#### المحتوى:
```php
<?php
// التحقق من الصلاحيات
if (!hasPermission('financial')) {
    $_SESSION['error'] = 'ليس لديك صلاحية الوصول إلى هذه الصفحة';
    echo '<script>window.location.href = "index.php?page=unauthorized";</script>';
    return;
}

// الحصول على جميع التجار النشطين مع إحصائياتهم
$stmt = $db->query("
    SELECT t.id, t.name, t.contact_person, t.phone, t.price,
           (SELECT COUNT(*) FROM containers WHERE trader_id = t.id) as total_containers,
           (SELECT COUNT(*) FROM financial_documents WHERE customer_id = t.id) as total_documents,
           (SELECT COUNT(*) FROM container_transfers WHERE trader_id = t.id) as total_transfers
    FROM traders t 
    WHERE t.active = 1 
    ORDER BY t.name ASC
");
$traders = $stmt->fetchAll();
?>
```

### 2. تحديث التنقل في الـ Sidebar
```php
<!-- قبل الإصلاح -->
<li class="nav-item">
  <a href="index.php?page=trader_statement" class="nav-link">
    <i class="nav-icon fas fa-file-invoice-dollar"></i>
    <p>كشف حساب تاجر</p>
  </a>
</li>

<!-- بعد الإصلاح -->
<li class="nav-item">
  <a href="index.php?page=trader_statement_select" class="nav-link <?php echo $page == 'trader_statement_select' || $page == 'trader_statement' ? 'active' : ''; ?>">
    <i class="nav-icon fas fa-file-invoice-dollar"></i>
    <p>كشف حساب تاجر</p>
  </a>
</li>
```

### 3. إضافة عنوان الصفحة الجديدة
```php
switch($page) {
  case 'traders_list': $pageTitle = 'التجار'; break;
  case 'trader_statement_select': $pageTitle = 'كشف حساب تاجر'; break;  // جديد
  case 'trader_statement': $pageTitle = 'كشف حساب تاجر'; break;
  // ...
}
```

## مسار التنقل الجديد

### 1. من الـ Sidebar:
```
التقارير → كشف حساب تاجر
↓
صفحة اختيار التاجر (trader_statement_select)
↓
اختيار التاجر والفترة الزمنية
↓
كشف الحساب الفعلي (trader_statement)
```

### 2. من قائمة التجار:
```
التجار → قائمة التجار (traders_list)
↓
زر "كشف الحساب" لتاجر محدد
↓
كشف الحساب مباشرة (trader_statement?trader_id=X)
```

## الميزات الجديدة في صفحة الاختيار

### 1. نموذج الاختيار السريع
```html
<form method="GET">
  <input type="hidden" name="page" value="trader_statement">
  
  <select name="trader_id" required>
    <option value="">-- اختر التاجر --</option>
    <?php foreach ($traders as $trader): ?>
    <option value="<?php echo $trader['id']; ?>">
      <?php echo $trader['name']; ?>
      <?php if ($trader['price'] > 0): ?>
      (مستحق: <?php echo number_format($trader['price']); ?> د.ع)
      <?php endif; ?>
    </option>
    <?php endforeach; ?>
  </select>
  
  <input type="date" name="date_from" value="<?php echo date('Y-m-01'); ?>">
  <input type="date" name="date_to" value="<?php echo date('Y-m-d'); ?>">
  
  <button type="submit">عرض كشف الحساب</button>
</form>
```

### 2. بطاقات التجار التفاعلية
```html
<?php foreach ($traders as $trader): ?>
<div class="card trader-card">
  <div class="card-body">
    <h6><?php echo $trader['name']; ?></h6>
    
    <!-- الإحصائيات -->
    <div class="row text-center">
      <div class="col-4">
        <div class="fw-bold text-primary"><?php echo $trader['total_containers']; ?></div>
        <small>حاويات</small>
      </div>
      <div class="col-4">
        <div class="fw-bold text-info"><?php echo $trader['total_documents']; ?></div>
        <small>مستندات</small>
      </div>
      <div class="col-4">
        <div class="fw-bold text-success"><?php echo $trader['total_transfers']; ?></div>
        <small>تحويلات</small>
      </div>
    </div>
    
    <!-- أزرار العمل -->
    <div class="d-flex gap-1">
      <a href="index.php?page=trader_statement&trader_id=<?php echo $trader['id']; ?>" 
         class="btn btn-primary btn-sm flex-fill">
        <i class="fas fa-file-invoice-dollar me-1"></i>
        كشف الحساب
      </a>
      <a href="index.php?page=trader_statement&trader_id=<?php echo $trader['id']; ?>&print=1" 
         target="_blank" class="btn btn-success btn-sm">
        <i class="fas fa-print"></i>
      </a>
    </div>
  </div>
</div>
<?php endforeach; ?>
```

### 3. JavaScript للطباعة السريعة
```javascript
function openPrintVersion() {
    const traderId = document.querySelector('select[name="trader_id"]').value;
    const dateFrom = document.querySelector('input[name="date_from"]').value;
    const dateTo = document.querySelector('input[name="date_to"]').value;
    
    if (!traderId) {
        alert('يرجى اختيار التاجر أولاً');
        return;
    }
    
    let url = `index.php?page=trader_statement&trader_id=${traderId}&print=1`;
    if (dateFrom) url += `&date_from=${dateFrom}`;
    if (dateTo) url += `&date_to=${dateTo}`;
    
    window.open(url, '_blank');
}
```

## الروابط المحدثة

### 1. صفحة اختيار التاجر:
```
http://localhost/ccis_appis/index.php?page=trader_statement_select
```

### 2. كشف حساب تاجر محدد:
```
http://localhost/ccis_appis/index.php?page=trader_statement&trader_id=1
```

### 3. كشف حساب مع فترة زمنية:
```
http://localhost/ccis_appis/index.php?page=trader_statement&trader_id=1&date_from=2024-01-01&date_to=2024-12-31
```

### 4. طباعة كشف الحساب:
```
http://localhost/ccis_appis/index.php?page=trader_statement&trader_id=1&print=1
```

## التحسينات المطبقة

### 1. تجربة المستخدم
- ✅ **واجهة واضحة** لاختيار التاجر
- ✅ **معاينة الإحصائيات** قبل عرض كشف الحساب
- ✅ **اختيار الفترة الزمنية** مباشرة
- ✅ **روابط سريعة** للطباعة

### 2. التصميم
- ✅ **بطاقات تفاعلية** مع تأثيرات hover
- ✅ **ألوان متناسقة** مع النظام
- ✅ **أيقونات واضحة** ومعبرة
- ✅ **تصميم متجاوب** للأجهزة المختلفة

### 3. الوظائف
- ✅ **فلترة التجار النشطين** فقط
- ✅ **عرض المبالغ المستحقة** في القائمة
- ✅ **روابط مباشرة** لكشف الحساب
- ✅ **إمكانية الطباعة** المباشرة

## اختبار الحل

### 1. اختبار التنقل من الـ Sidebar:
1. اذهب إلى التقارير → كشف حساب تاجر
2. يجب أن تظهر صفحة اختيار التاجر
3. اختر تاجر واضغط "عرض كشف الحساب"
4. يجب أن يظهر كشف الحساب الفعلي

### 2. اختبار الاختيار السريع:
1. استخدم القائمة المنسدلة لاختيار التاجر
2. حدد الفترة الزمنية
3. اضغط "عرض كشف الحساب"
4. تحقق من صحة البيانات المعروضة

### 3. اختبار البطاقات التفاعلية:
1. تحقق من عرض إحصائيات كل تاجر
2. اختبر أزرار "كشف الحساب" و "طباعة"
3. تأكد من تأثيرات hover

### 4. اختبار الطباعة:
1. استخدم زر "عرض للطباعة" في النموذج
2. أو استخدم زر الطباعة في بطاقة التاجر
3. تأكد من فتح نافذة جديدة للطباعة

## الخلاصة

تم حل المشكلة بنجاح من خلال:

- ✅ **إنشاء صفحة وسيطة** لاختيار التاجر
- ✅ **تحديث روابط التنقل** في الـ sidebar
- ✅ **تحسين تجربة المستخدم** مع واجهة واضحة
- ✅ **إضافة ميزات إضافية** مثل الطباعة السريعة
- ✅ **الحفاظ على التناسق** مع تصميم النظام

الآن عند الضغط على "كشف حساب تاجر" في الـ sidebar، سيتم توجيه المستخدم إلى صفحة اختيار التاجر بدلاً من الخطأ السابق! 🎉
