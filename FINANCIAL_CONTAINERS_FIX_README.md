# إصلاح مشكلة جلب حاويات التاجر في المستندات المالية

## المشكلة
صفحة إضافة المستندات المالية `financial_add_new.php` لا تجلب حاويات التاجر المحدد بشكل صحيح.

## السبب
كان هناك تضارب في أسماء المتغيرات:
- الكود يستقبل `customer_id` من JavaScript
- لكن يستخدم `$customerId` في PHP بدلاً من `$traderId`
- الاستعلام يبحث في `trader_id` في جدول containers

## الإصلاحات المطبقة

### 1. تصحيح المتغيرات في AJAX Handler
```php
// قبل الإصلاح
$customerId = (int)$_GET['customer_id'];
$params = [$customerId];
'trader_id' => $customerId,

// بعد الإصلاح
$traderId = (int)$_GET['customer_id']; // customer_id هنا يشير إلى trader_id
$params = [$traderId];
'trader_id' => $traderId,
```

### 2. تحسين التشخيص في JavaScript
```javascript
// إضافة المزيد من معلومات التشخيص
console.log('Response URL:', response.url);
console.log('Trader ID used:', data.trader_id);
console.log('SQL query:', data.sql);
console.log('Query params:', data.query_params);
```

### 3. إصلاح هيكل قاعدة البيانات
تأكد من أن القيد الخارجي في جدول `financial_documents` يشير إلى جدول `traders`:
```sql
ALTER TABLE financial_documents 
DROP FOREIGN KEY financial_documents_ibfk_1;

ALTER TABLE financial_documents 
ADD CONSTRAINT financial_documents_ibfk_1 
FOREIGN KEY (customer_id) REFERENCES traders(id) ON DELETE SET NULL;
```

## الملفات المنشأة للاختبار والإصلاح

### 1. ملفات الاختبار
- `test_financial_containers.php` - اختبار شامل لجلب الحاويات
- `fix_financial_documents_structure.php` - إصلاح هيكل قاعدة البيانات

### 2. الملفات المحدثة
- `pages/financial_add_new.php` - إصلاح المتغيرات وتحسين التشخيص

## خطوات الاختبار

### الخطوة 1: فحص هيكل قاعدة البيانات
```
http://localhost/ccis_appis/fix_financial_documents_structure.php
```

### الخطوة 2: اختبار جلب الحاويات
```
http://localhost/ccis_appis/test_financial_containers.php
```

### الخطوة 3: اختبار الصفحة الفعلية
```
http://localhost/ccis_appis/index.php?page=financial&action=add_new&type=receipt
```

## التشخيص

### فحص Console في المتصفح
1. افتح Developer Tools (F12)
2. اذهب إلى Console
3. اختر تاجر من القائمة
4. راقب الرسائل في Console:

```javascript
// رسائل التشخيص المتوقعة
Loading containers from: index.php?page=financial&action=add_new&type=receipt&ajax=get_containers&customer_id=1
Response status: 200
Response URL: http://localhost/ccis_appis/index.php?page=financial&action=add_new&type=receipt&ajax=get_containers&customer_id=1
Parsed data: {success: true, trader_id: 1, containers: [...], count: 5}
Success is true, checking containers...
*** CALLING displayContainers with data: [...]
```

### فحص AJAX Response
يمكن اختبار AJAX endpoint مباشرة:
```
http://localhost/ccis_appis/index.php?page=financial&action=add_new&type=receipt&ajax=get_containers&customer_id=1
```

الاستجابة المتوقعة:
```json
{
  "success": true,
  "trader_id": 1,
  "containers": [
    {
      "id": 1,
      "container_number": "CONT-001",
      "content": "أجهزة إلكترونية",
      "status": "pending",
      ...
    }
  ],
  "count": 1,
  "query_params": [1],
  "sql": "SELECT id, container_number, ... FROM containers WHERE trader_id = ? AND status != 'cancelled' ORDER BY entry_date DESC"
}
```

## المشاكل الشائعة والحلول

### 1. لا توجد حاويات تظهر
**السبب المحتمل**: لا توجد حاويات للتاجر المحدد
**الحل**: 
- تحقق من وجود حاويات في جدول containers
- تأكد من أن trader_id صحيح
- تحقق من حالة الحاويات (يجب ألا تكون cancelled)

### 2. خطأ في AJAX
**السبب المحتمل**: مشكلة في هيكل قاعدة البيانات
**الحل**: 
- شغل `fix_financial_documents_structure.php`
- تحقق من القيود الخارجية

### 3. JavaScript Error
**السبب المحتمل**: مشكلة في parsing JSON
**الحل**: 
- تحقق من Console للأخطاء
- تأكد من أن AJAX endpoint يعيد JSON صحيح

## البيانات التجريبية

إذا لم تكن هناك بيانات كافية للاختبار، يمكن إضافة بيانات تجريبية:

```sql
-- إضافة تجار تجريبيين
INSERT INTO traders (name, contact_person, phone, address) VALUES
('شركة التجارة الأولى', 'أحمد محمد', '07801234567', 'بغداد'),
('شركة الاستيراد المتقدمة', 'فاطمة علي', '07809876543', 'البصرة');

-- إضافة حاويات تجريبية
INSERT INTO containers (container_number, trader_id, entry_date, status, container_type, content, selling_price, purchase_price) VALUES
('CONT-TEST-001', 1, CURDATE(), 'pending', '20 قدم', 'أجهزة إلكترونية', 1200000, 1000000),
('CONT-TEST-002', 1, CURDATE(), 'pending', '40 قدم', 'قطع غيار سيارات', 1800000, 1500000),
('CONT-TEST-003', 2, CURDATE(), 'pending', '20 قدم', 'مواد غذائية', 800000, 650000);
```

## الخلاصة

تم إصلاح المشكلة من خلال:
1. تصحيح أسماء المتغيرات في PHP
2. تحسين التشخيص في JavaScript
3. التأكد من صحة هيكل قاعدة البيانات
4. إضافة ملفات اختبار شاملة

الآن يجب أن تعمل صفحة إضافة المستندات المالية بشكل صحيح وتجلب حاويات التاجر المحدد.
