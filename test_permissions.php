<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الصلاحيات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { color: blue; background: #d1ecf1; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .warning { color: orange; background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>اختبار الصلاحيات والتنقل</h1>
    
    <?php
    session_start();
    require_once 'config/config.php';
    require_once 'includes/auth.php';
    
    echo "<div class='info'><h3>معلومات الجلسة الحالية:</h3>";
    echo "<p><strong>مسجل دخول:</strong> " . (isLoggedIn() ? 'نعم' : 'لا') . "</p>";
    
    if (isLoggedIn()) {
        echo "<p><strong>معرف المستخدم:</strong> " . ($_SESSION['user_id'] ?? 'غير محدد') . "</p>";
        echo "<p><strong>اسم المستخدم:</strong> " . ($_SESSION['username'] ?? 'غير محدد') . "</p>";
        echo "<p><strong>معرف الدور:</strong> " . ($_SESSION['user_role_id'] ?? 'غير محدد') . "</p>";
        echo "<p><strong>اسم الدور:</strong> " . ($_SESSION['user_role'] ?? 'غير محدد') . "</p>";
        
        echo "<p><strong>الصلاحيات المتاحة:</strong></p>";
        if (isset($_SESSION['permissions']) && is_array($_SESSION['permissions'])) {
            echo "<ul>";
            foreach ($_SESSION['permissions'] as $permission) {
                echo "<li>" . htmlspecialchars($permission) . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>لا توجد صلاحيات محددة في الجلسة</p>";
        }
    }
    echo "</div>";
    
    echo "<div class='info'><h3>اختبار الصلاحيات:</h3>";
    
    $permissions_to_test = [
        'dashboard' => 'لوحة التحكم',
        'containers' => 'إدارة الحاويات', 
        'customers' => 'إدارة الشركات',
        'financial' => 'الإدارة المالية',
        'reports' => 'التقارير',
        'users' => 'إدارة المستخدمين',
        'settings' => 'الإعدادات'
    ];
    
    foreach ($permissions_to_test as $permission => $name) {
        $hasPermission = hasPermission($permission);
        $class = $hasPermission ? 'success' : 'error';
        $icon = $hasPermission ? '✅' : '❌';
        echo "<div class='$class'>$icon <strong>$name ($permission):</strong> " . ($hasPermission ? 'متاح' : 'غير متاح') . "</div>";
    }
    echo "</div>";
    
    echo "<div class='info'><h3>اختبار الصفحات:</h3>";
    
    $pages_to_test = [
        'dashboard' => 'لوحة التحكم',
        'traders_list' => 'قائمة التجار',
        'trader_statement_select' => 'اختيار التاجر لكشف الحساب',
        'trader_statement' => 'كشف حساب التاجر',
        'financial' => 'المستندات المالية'
    ];
    
    foreach ($pages_to_test as $page => $name) {
        $canAccess = hasPermission($page) || hasPermission('financial') || hasPermission('customers');
        $class = $canAccess ? 'success' : 'warning';
        $icon = $canAccess ? '✅' : '⚠️';
        
        $url = "index.php?page=$page";
        echo "<div class='$class'>$icon <strong>$name:</strong> ";
        echo "<a href='$url' target='_blank' style='color: inherit;'>$url</a>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='info'><h3>اختبار قاعدة البيانات:</h3>";
    
    try {
        // اختبار الاتصال بقاعدة البيانات
        $stmt = $db->query("SELECT COUNT(*) as count FROM traders WHERE active = 1");
        $tradersCount = $stmt->fetchColumn();
        echo "<div class='success'>✅ الاتصال بقاعدة البيانات يعمل</div>";
        echo "<div class='info'>عدد التجار النشطين: $tradersCount</div>";
        
        // اختبار جدول الصلاحيات
        $stmt = $db->query("SHOW TABLES LIKE 'permissions'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='success'>✅ جدول الصلاحيات موجود</div>";
            
            $stmt = $db->query("SELECT COUNT(*) FROM permissions");
            $permissionsCount = $stmt->fetchColumn();
            echo "<div class='info'>عدد الصلاحيات في قاعدة البيانات: $permissionsCount</div>";
        } else {
            echo "<div class='warning'>⚠️ جدول الصلاحيات غير موجود</div>";
        }
        
        // اختبار جدول role_permissions
        $stmt = $db->query("SHOW TABLES LIKE 'role_permissions'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='success'>✅ جدول صلاحيات الأدوار موجود</div>";
            
            if (isset($_SESSION['user_role_id'])) {
                $stmt = $db->prepare("
                    SELECT p.permission_key, p.name 
                    FROM role_permissions rp 
                    JOIN permissions p ON rp.permission_id = p.id 
                    WHERE rp.role_id = ?
                ");
                $stmt->execute([$_SESSION['user_role_id']]);
                $dbPermissions = $stmt->fetchAll();
                
                echo "<div class='info'>صلاحيات الدور من قاعدة البيانات:</div>";
                echo "<ul>";
                foreach ($dbPermissions as $perm) {
                    echo "<li>" . htmlspecialchars($perm['name']) . " (" . htmlspecialchars($perm['permission_key']) . ")</li>";
                }
                echo "</ul>";
            }
        } else {
            echo "<div class='warning'>⚠️ جدول صلاحيات الأدوار غير موجود</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='info'><h3>روابط الاختبار المباشر:</h3>";
    echo "<p><a href='index.php?page=traders_list' target='_blank'>قائمة التجار</a></p>";
    echo "<p><a href='index.php?page=trader_statement_select' target='_blank'>اختيار التاجر لكشف الحساب</a></p>";
    echo "<p><a href='index.php?page=financial' target='_blank'>المستندات المالية</a></p>";
    echo "</div>";
    ?>
    
    <div class="info">
        <h3>تشخيص المشكلة:</h3>
        <p>إذا كانت صفحة "اختيار التاجر لكشف الحساب" لا تعمل، تحقق من:</p>
        <ul>
            <li>وجود صلاحية "financial" للمستخدم الحالي</li>
            <li>وجود ملف pages/trader_statement_select.php</li>
            <li>إضافة الصفحة في قائمة allowed_pages في index.php</li>
            <li>صحة رابط الـ sidebar في includes/header.php</li>
        </ul>
    </div>
</body>
</html>
