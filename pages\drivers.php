<?php
// التحقق من الصلاحيات (مؤقت - معطل)
/*
if (!hasPermission('drivers')) {
    $_SESSION['error'] = 'ليس لديك صلاحية الوصول إلى هذه الصفحة';
    echo '<script>window.location.href = "index.php?page=unauthorized";</script>';
    return;
}
*/

// معالجة حذف السائق
if (isset($_POST['delete_driver']) && isset($_POST['driver_id'])) {
    try {
        $driverId = (int)$_POST['driver_id'];
        
        // التحقق من عدم وجود تحويلات مرتبطة بالسائق
        $checkStmt = $db->prepare("SELECT COUNT(*) FROM container_transfers WHERE driver_id = ?");
        $checkStmt->execute([$driverId]);
        $transferCount = $checkStmt->fetchColumn();
        
        if ($transferCount > 0) {
            $_SESSION['error'] = 'لا يمكن حذف السائق لوجود تحويلات مرتبطة به. يمكنك إلغاء تفعيله بدلاً من ذلك.';
        } else {
            $stmt = $db->prepare("DELETE FROM drivers WHERE id = ?");
            $stmt->execute([$driverId]);
            $_SESSION['success'] = 'تم حذف السائق بنجاح';
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = 'حدث خطأ أثناء حذف السائق: ' . $e->getMessage();
    }
    
    echo '<script>window.location.href = "index.php?page=drivers";</script>';
    return;
}

// معالجة تغيير حالة السائق
if (isset($_POST['toggle_status']) && isset($_POST['driver_id'])) {
    try {
        $driverId = (int)$_POST['driver_id'];
        $newStatus = $_POST['new_status'];
        
        $stmt = $db->prepare("UPDATE drivers SET status = ? WHERE id = ?");
        $stmt->execute([$newStatus, $driverId]);
        $_SESSION['success'] = 'تم تحديث حالة السائق بنجاح';
    } catch (PDOException $e) {
        $_SESSION['error'] = 'حدث خطأ أثناء تحديث حالة السائق: ' . $e->getMessage();
    }
    
    echo '<script>window.location.href = "index.php?page=drivers";</script>';
    return;
}

// معاملات البحث والفلترة
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'created_at';
$order = isset($_GET['order']) && $_GET['order'] === 'asc' ? 'ASC' : 'DESC';

// بناء الاستعلام
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(d.driver_name LIKE ? OR d.license_number LIKE ? OR d.phone LIKE ? OR d.vehicle_number LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($status_filter)) {
    $where_conditions[] = "d.status = ?";
    $params[] = $status_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// الحصول على إجمالي عدد السجلات
$count_sql = "SELECT COUNT(*) FROM drivers d $where_clause";
$count_stmt = $db->prepare($count_sql);
$count_stmt->execute($params);
$total_records = $count_stmt->fetchColumn();

// إعداد الترقيم
$records_per_page = 15;
$total_pages = ceil($total_records / $records_per_page);
$current_page = isset($_GET['page_num']) ? max(1, (int)$_GET['page_num']) : 1;
$offset = ($current_page - 1) * $records_per_page;

// الاستعلام الرئيسي مع الترقيم
$allowed_sorts = ['driver_name', 'license_number', 'phone', 'status', 'created_at', 'license_expiry'];
$sort = in_array($sort, $allowed_sorts) ? $sort : 'created_at';

// إضافة alias الجدول للترتيب
if (in_array($sort, ['driver_name', 'license_number', 'phone', 'status', 'created_at', 'license_expiry'])) {
    $sort = 'd.' . $sort;
}

$sql = "
    SELECT d.*, 
           u.full_name as created_by_name,
           (SELECT COUNT(*) FROM container_transfers WHERE driver_id = d.id) as total_transfers,
           (SELECT COALESCE(SUM(total_amount), 0) FROM container_transfers WHERE driver_id = d.id) as total_revenue
    FROM drivers d
    LEFT JOIN users u ON d.created_by = u.id
    $where_clause
    ORDER BY $sort $order
    LIMIT $records_per_page OFFSET $offset
";

$stmt = $db->prepare($sql);
$stmt->execute($params);
$drivers = $stmt->fetchAll();

// إحصائيات سريعة
$stats_sql = "
    SELECT 
        COUNT(*) as total_drivers,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_drivers,
        SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_drivers,
        SUM(CASE WHEN status = 'suspended' THEN 1 ELSE 0 END) as suspended_drivers,
        SUM(CASE WHEN license_expiry < CURDATE() THEN 1 ELSE 0 END) as expired_licenses,
        SUM(CASE WHEN license_expiry BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as expiring_soon
    FROM drivers
";
$stats_stmt = $db->query($stats_sql);
$stats = $stats_stmt->fetch();
?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">إدارة السائقين</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                    <li class="breadcrumb-item">النقل والتحويلات</li>
                    <li class="breadcrumb-item active">إدارة السائقين</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
    
        <!-- Action Buttons -->
        <div class="row mb-3">
            <div class="col-12">
                <a href="index.php?page=drivers&action=add" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة سائق جديد
                </a>
                <a href="index.php?page=transfers" class="btn btn-info">
                    <i class="fas fa-shipping-container"></i> تحويلات الحاويات
                </a>
            </div>
        </div>

        <!-- Alerts -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <h5><i class="icon fas fa-check"></i> نجح!</h5>
                <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <h5><i class="icon fas fa-ban"></i> خطأ!</h5>
                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>

        <!-- Small boxes (Stat box) -->
        <div class="row">
            <div class="col-lg-2 col-6">
                <div class="small-box bg-primary">
                    <div class="inner">
                        <h3><?php echo number_format($stats['total_drivers']); ?></h3>
                        <p>إجمالي السائقين</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <a href="#" class="small-box-footer">المزيد من المعلومات <i class="fas fa-arrow-circle-right"></i></a>
                </div>
            </div>
            
            <div class="col-lg-2 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3><?php echo number_format($stats['active_drivers']); ?></h3>
                        <p>النشطين</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <a href="#" class="small-box-footer">المزيد من المعلومات <i class="fas fa-arrow-circle-right"></i></a>
                </div>
            </div>
            
            <div class="col-lg-2 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3><?php echo number_format($stats['suspended_drivers']); ?></h3>
                        <p>موقوفين</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-ban"></i>
                    </div>
                    <a href="#" class="small-box-footer">المزيد من المعلومات <i class="fas fa-arrow-circle-right"></i></a>
                </div>
            </div>
            
            <div class="col-lg-2 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3><?php echo number_format($stats['expired_licenses']); ?></h3>
                        <p>رخص منتهية</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <a href="#" class="small-box-footer">المزيد من المعلومات <i class="fas fa-arrow-circle-right"></i></a>
                </div>
            </div>
            
            <div class="col-lg-2 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3><?php echo number_format($stats['expiring_soon']); ?></h3>
                        <p>تنتهي قريباً</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <a href="#" class="small-box-footer">المزيد من المعلومات <i class="fas fa-arrow-circle-right"></i></a>
                </div>
            </div>
            
            <div class="col-lg-2 col-6">
                <div class="small-box bg-secondary">
                    <div class="inner">
                        <h3><?php echo number_format($stats['inactive_drivers']); ?></h3>
                        <p>غير نشط</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-pause-circle"></i>
                    </div>
                    <a href="#" class="small-box-footer">المزيد من المعلومات <i class="fas fa-arrow-circle-right"></i></a>
                </div>
            </div>
        </div>

        <!-- أدوات البحث والفلترة -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-search me-2"></i>البحث والفلترة
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <input type="hidden" name="page" value="drivers">
                    
                    <div class="col-lg-4 col-md-6">
                        <label class="form-label">
                            <i class="fas fa-search me-1"></i>البحث العام
                        </label>
                        <div class="input-group">
                            <input type="text" class="form-control" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>"
                                   placeholder="اسم السائق، رقم الرخصة، الهاتف...">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                        </div>
                    </div>
                    
                    <!-- <div class="col-lg-2 col-md-3">
                        <label class="form-label">
                            <i class="fas fa-filter me-1"></i>الحالة
                        </label>
                        <select class="form-select" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>نشط</option>
                            <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                            <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>موقوف</option>
                        </select>
                    </div> -->
                    
                    <!-- <div class="col-lg-2 col-md-3">
                        <label class="form-label">
                            <i class="fas fa-sort me-1"></i>ترتيب حسب
                        </label>
                        <select class="form-select" name="sort">
                            <option value="created_at" <?php echo $sort === 'created_at' ? 'selected' : ''; ?>>تاريخ الإضافة</option>
                            <option value="driver_name" <?php echo $sort === 'driver_name' ? 'selected' : ''; ?>>اسم السائق</option>
                            <option value="license_expiry" <?php echo $sort === 'license_expiry' ? 'selected' : ''; ?>>انتهاء الرخصة</option>
                            <option value="status" <?php echo $sort === 'status' ? 'selected' : ''; ?>>الحالة</option>
                        </select>
                    </div> -->
                    
                    <!-- <div class="col-lg-2 col-md-3">
                        <label class="form-label">
                            <i class="fas fa-arrows-alt-v me-1"></i>نوع الترتيب
                        </label>
                        <select class="form-select" name="order">
                            <option value="desc" <?php echo $order === 'DESC' ? 'selected' : ''; ?>>تنازلي ↓</option>
                            <option value="asc" <?php echo $order === 'ASC' ? 'selected' : ''; ?>>تصاعدي ↑</option>
                        </select>
                    </div> -->
                    
                    <div class="col-lg-2 col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>بحث
                            </button>
                          
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-1">
                           
                            <a href="index.php?page=drivers" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-redo me-1"></i>إعادة تعيين
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- جدول السائقين -->
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>قائمة السائقين
                            <span class="badge bg-primary ms-2"><?php echo number_format($total_records); ?></span>
                        </h5>
                    </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <?php if ($total_pages > 1): ?>
                        <div class="d-flex justify-content-end align-items-center">
                            <span class="text-muted me-3">
                                <i class="fas fa-file-alt me-1"></i>
                                صفحة <?php echo $current_page; ?> من <?php echo $total_pages; ?>
                            </span>
                            <span class="badge bg-info">
                                <?php echo (($current_page - 1) * $records_per_page + 1); ?> - 
                                <?php echo min($current_page * $records_per_page, $total_records); ?>
                                من <?php echo $total_records; ?>
                            </span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        
        <div class="card-body p-0">
            <?php if (empty($drivers)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-user-slash text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">لا توجد سائقين</h4>
                    <p class="text-muted">لم يتم العثور على سائقين بالمعايير المحددة</p>
                    <a href="index.php?page=drivers&action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة أول سائق
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>السائق</th>
                                <th>رقم الرخصة</th>
                                <th>الهاتف</th>
                                <th>المركبة</th>
                                <th>الحالة</th>
                                <th>انتهاء الرخصة</th>
                                <th>الإحصائيات</th>
                                <th>تاريخ الإضافة</th>
                                <th width="120">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($drivers as $driver): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
        
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($driver['driver_name']); ?></h6>
                                            <?php if ($driver['national_id']): ?>
                                            <small class="text-muted">هوية: <?php echo htmlspecialchars($driver['national_id']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                
                                <td>
                                    <span class="font-monospace"><?php echo htmlspecialchars($driver['license_number']); ?></span>
                                </td>
                                
                                <td>
                                    <span class="font-monospace" dir="ltr"><?php echo htmlspecialchars($driver['phone']); ?></span>
                                </td>
                                
                                <td>
                                    <?php if ($driver['vehicle_type'] || $driver['vehicle_number']): ?>
                                    <div>
                                        <?php if ($driver['vehicle_type']): ?>
                                        <div><small class="text-muted"><?php echo htmlspecialchars($driver['vehicle_type']); ?></small></div>
                                        <?php endif; ?>
                                        <?php if ($driver['vehicle_number']): ?>
                                        <div class="font-monospace"><?php echo htmlspecialchars($driver['vehicle_number']); ?></div>
                                        <?php endif; ?>
                                    </div>
                                    <?php else: ?>
                                    <span class="text-muted">غير محدد</span>
                                    <?php endif; ?>
                                </td>
                                
                                <td>
                                    <?php
                                    $status_classes = [
                                        'active' => 'bg-success',
                                        'inactive' => 'bg-secondary', 
                                        'suspended' => 'bg-warning'
                                    ];
                                    $status_texts = [
                                        'active' => 'نشط',
                                        'inactive' => 'غير نشط',
                                        'suspended' => 'موقوف'
                                    ];
                                    ?>
                                    <span class="badge <?php echo $status_classes[$driver['status']] ?? 'bg-secondary'; ?>">
                                        <?php echo $status_texts[$driver['status']] ?? $driver['status']; ?>
                                    </span>
                                </td>
                                
                                <td>
                                    <?php if ($driver['license_expiry']): ?>
                                        <?php
                                        $expiry_date = new DateTime($driver['license_expiry']);
                                        $today = new DateTime();
                                        $days_diff = $today->diff($expiry_date)->days;
                                        $is_expired = $expiry_date < $today;
                                        $is_expiring_soon = !$is_expired && $days_diff <= 30;
                                        ?>
                                        <div>
                                            <div class="<?php echo $is_expired ? 'text-danger' : ($is_expiring_soon ? 'text-warning' : 'text-success'); ?>">
                                                <?php echo convertDateFromMysql($driver['license_expiry']); ?>
                                            </div>
                                            <?php if ($is_expired): ?>
                                            <small class="text-danger">منتهية منذ <?php echo $days_diff; ?> يوم</small>
                                            <?php elseif ($is_expiring_soon): ?>
                                            <small class="text-warning">تنتهي خلال <?php echo $days_diff; ?> يوم</small>
                                            <?php endif; ?>
                                        </div>
                                    <?php else: ?>
                                    <span class="text-muted">غير محدد</span>
                                    <?php endif; ?>
                                </td>
                                
                                <td>
                                    <div class="text-center">
                                        <div class="fw-bold text-primary"><?php echo number_format($driver['total_transfers']); ?></div>
                                        <small class="text-muted">تحويل</small>
                                        <?php if ($driver['total_revenue'] > 0): ?>
                                        <div class="small text-success">
                                            <?php echo number_format($driver['total_revenue'], 0); ?> د.ع
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                
                                <td>
                                    <div>
                                        <?php echo convertDateFromMysql($driver['created_at'], true); ?>
                                    </div>
                                    <?php if ($driver['created_by_name']): ?>
                                    <small class="text-muted">بواسطة: <?php echo htmlspecialchars($driver['created_by_name']); ?></small>
                                    <?php endif; ?>
                                </td>
                                
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="index.php?page=drivers&action=view&id=<?php echo $driver['id']; ?>" 
                                           class="btn btn-outline-info" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="index.php?page=drivers&action=edit&id=<?php echo $driver['id']; ?>" 
                                           class="btn btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        
                                        <!-- تغيير الحالة -->
                                        <div class="btn-group">
                                            <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" title="تغيير الحالة">
                                                <i class="fas fa-toggle-on"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <?php if ($driver['status'] != 'active'): ?>
                                                <li>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="driver_id" value="<?php echo $driver['id']; ?>">
                                                        <input type="hidden" name="new_status" value="active">
                                                        <button type="submit" name="toggle_status" class="dropdown-item text-success">
                                                            <i class="fas fa-check-circle me-2"></i>تفعيل
                                                        </button>
                                                    </form>
                                                </li>
                                                <?php endif; ?>
                                                
                                                <?php if ($driver['status'] != 'inactive'): ?>
                                                <li>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="driver_id" value="<?php echo $driver['id']; ?>">
                                                        <input type="hidden" name="new_status" value="inactive">
                                                        <button type="submit" name="toggle_status" class="dropdown-item text-secondary">
                                                            <i class="fas fa-pause-circle me-2"></i>إلغاء تفعيل
                                                        </button>
                                                    </form>
                                                </li>
                                                <?php endif; ?>
                                                
                                                <?php if ($driver['status'] != 'suspended'): ?>
                                                <li>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="driver_id" value="<?php echo $driver['id']; ?>">
                                                        <input type="hidden" name="new_status" value="suspended">
                                                        <button type="submit" name="toggle_status" class="dropdown-item text-warning">
                                                            <i class="fas fa-ban me-2"></i>إيقاف
                                                        </button>
                                                    </form>
                                                </li>
                                                <?php endif; ?>
                                                
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <button class="dropdown-item text-danger" onclick="confirmDelete(<?php echo $driver['id']; ?>, '<?php echo htmlspecialchars($driver['driver_name'], ENT_QUOTES); ?>')">
                                                        <i class="fas fa-trash me-2"></i>حذف
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- الترقيم -->
        <?php if ($total_pages > 1): ?>
        <div class="card-footer">
            <nav>
                <ul class="pagination justify-content-center mb-0">
                    <?php
                    $base_url = "index.php?page=drivers";
                    if (!empty($search)) $base_url .= "&search=" . urlencode($search);
                    if (!empty($status_filter)) $base_url .= "&status=" . urlencode($status_filter);
                    $base_url .= "&sort=$sort&order=" . strtolower($order);
                    ?>
                    
                    <!-- الصفحة الأولى -->
                    <li class="page-item <?php echo $current_page == 1 ? 'disabled' : ''; ?>">
                        <a class="page-link" href="<?php echo $base_url; ?>&page_num=1">الأولى</a>
                    </li>
                    
                    <!-- الصفحة السابقة -->
                    <li class="page-item <?php echo $current_page == 1 ? 'disabled' : ''; ?>">
                        <a class="page-link" href="<?php echo $base_url; ?>&page_num=<?php echo $current_page - 1; ?>">السابق</a>
                    </li>
                    
                    <!-- أرقام الصفحات -->
                    <?php
                    $start_page = max(1, $current_page - 2);
                    $end_page = min($total_pages, $current_page + 2);
                    
                    for ($i = $start_page; $i <= $end_page; $i++):
                    ?>
                    <li class="page-item <?php echo $i == $current_page ? 'active' : ''; ?>">
                        <a class="page-link" href="<?php echo $base_url; ?>&page_num=<?php echo $i; ?>"><?php echo $i; ?></a>
                    </li>
                    <?php endfor; ?>
                    
                    <!-- الصفحة التالية -->
                    <li class="page-item <?php echo $current_page == $total_pages ? 'disabled' : ''; ?>">
                        <a class="page-link" href="<?php echo $base_url; ?>&page_num=<?php echo $current_page + 1; ?>">التالي</a>
                    </li>
                    
                    <!-- الصفحة الأخيرة -->
                    <li class="page-item <?php echo $current_page == $total_pages ? 'disabled' : ''; ?>">
                        <a class="page-link" href="<?php echo $base_url; ?>&page_num=<?php echo $total_pages; ?>">الأخيرة</a>
                    </li>
                </ul>
            </nav>
        </div>
        <?php endif; ?>
        </div>
    </div>

    </div>
    <!-- /.container-fluid -->
</div>
<!-- /.content -->

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف السائق <strong id="driverName"></strong>؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه.
                </div>
            </div>
            <div class="modal-footer">
                <form method="POST" id="deleteForm">
                    <input type="hidden" name="driver_id" id="deleteDriverId">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="delete_driver" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(driverId, driverName) {
    document.getElementById('deleteDriverId').value = driverId;
    document.getElementById('driverName').textContent = driverName;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// تحديث الصفحة تلقائياً كل 5 دقائق لمراقبة انتهاء الرخص
setTimeout(() => {
    window.location.reload();
}, 300000);
</script>

<style>
/* تحسينات بسيطة للحفاظ على التناسق مع النظام */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.avatar-sm {
    width: 40px;
    height: 40px;
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.font-monospace {
    font-family: 'Courier New', monospace;
    direction: ltr;
    text-align: left;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.btn-group-sm > .btn, .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.badge {
    font-size: 0.75em;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group-sm > .btn {
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
    }
    
    .avatar-sm {
        width: 32px;
        height: 32px;
    }
}
</style>