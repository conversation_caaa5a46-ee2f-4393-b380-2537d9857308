<?php
// التحقق من الصلاحيات
if (!hasPermission('financial')) {
    $_SESSION['error'] = 'ليس لديك صلاحية الوصول إلى هذه الصفحة';
    echo '<script>window.location.href = "index.php?page=unauthorized";</script>';
    return;
}

// معاملات البحث والفلترة
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'name';
$order = isset($_GET['order']) && $_GET['order'] === 'desc' ? 'DESC' : 'ASC';

// بناء الاستعلام
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(t.name LIKE ? OR t.contact_person LIKE ? OR t.phone LIKE ? OR t.email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status_filter !== '') {
    $where_conditions[] = "t.active = ?";
    $params[] = $status_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// الحصول على إجمالي عدد السجلات
$count_sql = "SELECT COUNT(*) FROM traders t $where_clause";
$count_stmt = $db->prepare($count_sql);
$count_stmt->execute($params);
$total_records = $count_stmt->fetchColumn();

// إعداد الترقيم
$records_per_page = 15;
$total_pages = ceil($total_records / $records_per_page);
$current_page = isset($_GET['page_num']) ? max(1, (int)$_GET['page_num']) : 1;
$offset = ($current_page - 1) * $records_per_page;

// الاستعلام الرئيسي مع الترقيم
$allowed_sorts = ['name', 'contact_person', 'phone', 'created_at', 'price'];
$sort = in_array($sort, $allowed_sorts) ? $sort : 'name';

// إضافة alias الجدول للترتيب
if (in_array($sort, ['name', 'contact_person', 'phone', 'created_at', 'price'])) {
    $sort = 't.' . $sort;
}

$sql = "
    SELECT t.*, 
           u.full_name as created_by_name,
           (SELECT COUNT(*) FROM containers WHERE trader_id = t.id) as total_containers,
           (SELECT COUNT(*) FROM financial_documents WHERE customer_id = t.id) as total_documents,
           (SELECT COUNT(*) FROM container_transfers WHERE trader_id = t.id) as total_transfers
    FROM traders t
    LEFT JOIN users u ON t.created_by = u.id
    $where_clause
    ORDER BY $sort $order
    LIMIT $records_per_page OFFSET $offset
";

$stmt = $db->prepare($sql);
$stmt->execute($params);
$traders = $stmt->fetchAll();

// إحصائيات سريعة
$stats_sql = "
    SELECT 
        COUNT(*) as total_traders,
        SUM(CASE WHEN active = 1 THEN 1 ELSE 0 END) as active_traders,
        SUM(CASE WHEN active = 0 THEN 1 ELSE 0 END) as inactive_traders,
        SUM(price) as total_debt
    FROM traders
";
$stats_stmt = $db->query($stats_sql);
$stats = $stats_stmt->fetch();
?>

<!-- Content Header -->
<div class="content-header bg-gradient-primary">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-8">
                <div class="d-flex align-items-center">
                    <div class="page-icon bg-white bg-opacity-25 rounded-circle p-3 me-3">
                        <i class="fas fa-users text-white fa-2x"></i>
                    </div>
                    <div>
                        <h1 class="m-0 text-white">إدارة التجار</h1>
                        <p class="text-white-50 mb-0">عرض وإدارة قائمة التجار وكشوف حساباتهم</p>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <ol class="breadcrumb float-sm-right bg-transparent">
                    <li class="breadcrumb-item"><a href="index.php" class="text-white-50">الرئيسية</a></li>
                    <li class="breadcrumb-item active text-white">التجار</li>
                </ol>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center flex-wrap gap-2">
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="index.php?page=customers" class="btn btn-light btn-sm shadow-sm">
                            <i class="fas fa-building me-2"></i>إدارة الشركات
                        </a>
                        <a href="index.php?page=financial" class="btn btn-light btn-sm shadow-sm">
                            <i class="fas fa-file-invoice-dollar me-2"></i>المستندات المالية
                        </a>
                    </div>
                    <a href="index.php?page=customers&action=add" class="btn btn-warning btn-lg shadow-sm pulse-animation">
                        <i class="fas fa-plus me-2"></i>إضافة تاجر جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="content">
    <div class="container-fluid">

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100 card-hover">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="text-primary mb-1">
                                    <i class="fas fa-users fa-lg"></i>
                                </div>
                                <h3 class="mb-1 text-primary fw-bold"><?php echo number_format($stats['total_traders']); ?></h3>
                                <p class="mb-0 text-muted small">إجمالي التجار</p>
                            </div>
                            <div class="stat-icon bg-primary bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-users text-primary fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100 card-hover">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="text-success mb-1">
                                    <i class="fas fa-check-circle fa-lg"></i>
                                </div>
                                <h3 class="mb-1 text-success fw-bold"><?php echo number_format($stats['active_traders']); ?></h3>
                                <p class="mb-0 text-muted small">تجار نشطين</p>
                            </div>
                            <div class="stat-icon bg-success bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-check-circle text-success fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100 card-hover">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="text-warning mb-1">
                                    <i class="fas fa-pause-circle fa-lg"></i>
                                </div>
                                <h3 class="mb-1 text-warning fw-bold"><?php echo number_format($stats['inactive_traders']); ?></h3>
                                <p class="mb-0 text-muted small">تجار غير نشطين</p>
                            </div>
                            <div class="stat-icon bg-warning bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-pause-circle text-warning fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100 card-hover">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="text-danger mb-1">
                                    <i class="fas fa-dollar-sign fa-lg"></i>
                                </div>
                                <h3 class="mb-1 text-danger fw-bold"><?php echo number_format($stats['total_debt']); ?></h3>
                                <p class="mb-0 text-muted small">إجمالي الديون (د.ع)</p>
                            </div>
                            <div class="stat-icon bg-danger bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-dollar-sign text-danger fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-light border-0">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-filter me-2"></i>البحث والفلترة
                    </h5>
                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="collapse" data-bs-target="#filtersCollapse">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>
            <div class="collapse show" id="filtersCollapse">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <input type="hidden" name="page" value="traders_list">
                        
                        <div class="col-lg-4 col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-search text-primary me-1"></i>البحث العام
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control border-2" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="اسم التاجر، الهاتف، البريد الإلكتروني...">
                                <span class="input-group-text bg-primary text-white">
                                    <i class="fas fa-search"></i>
                                </span>
                            </div>
                        </div>
                        
                        <div class="col-lg-2 col-md-3">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-toggle-on text-success me-1"></i>الحالة
                            </label>
                            <select class="form-select border-2" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="1" <?php echo $status_filter === '1' ? 'selected' : ''; ?>>نشط</option>
                                <option value="0" <?php echo $status_filter === '0' ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>
                        
                        <div class="col-lg-2 col-md-3">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-sort text-info me-1"></i>ترتيب حسب
                            </label>
                            <select class="form-select border-2" name="sort">
                                <option value="name" <?php echo $sort === 't.name' ? 'selected' : ''; ?>>الاسم</option>
                                <option value="created_at" <?php echo $sort === 't.created_at' ? 'selected' : ''; ?>>تاريخ الإنشاء</option>
                                <option value="price" <?php echo $sort === 't.price' ? 'selected' : ''; ?>>المبلغ المستحق</option>
                            </select>
                        </div>
                        
                        <div class="col-lg-2 col-md-3">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-sort-amount-down text-secondary me-1"></i>الاتجاه
                            </label>
                            <select class="form-select border-2" name="order">
                                <option value="asc" <?php echo $order === 'ASC' ? 'selected' : ''; ?>>تصاعدي</option>
                                <option value="desc" <?php echo $order === 'DESC' ? 'selected' : ''; ?>>تنازلي</option>
                            </select>
                        </div>
                        
                        <div class="col-lg-2 col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary shadow-sm">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                                <a href="index.php?page=traders_list" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-redo me-1"></i>إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Traders Table -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-light border-0">
                <div class="d-flex justify-content-between align-items-center flex-wrap gap-2">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-2">
                                <i class="fas fa-list text-primary"></i>
                            </div>
                        </div>
                        <div>
                            <h5 class="mb-0 text-primary fw-bold">قائمة التجار</h5>
                            <small class="text-muted">إجمالي <?php echo number_format($total_records); ?> تاجر</small>
                        </div>
                    </div>

                    <div class="d-flex align-items-center gap-2 flex-wrap">
                        <?php if ($total_pages > 1): ?>
                        <div class="pagination-info bg-light rounded px-3 py-1">
                            <small class="text-muted">
                                <i class="fas fa-file-alt me-1"></i>
                                صفحة <?php echo $current_page; ?> من <?php echo $total_pages; ?>
                            </small>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="card-body p-0">
                <?php if (empty($traders)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-users text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">لا يوجد تجار</h4>
                    <p class="text-muted">لم يتم العثور على تجار بالمعايير المحددة</p>
                    <a href="index.php?page=customers&action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة أول تاجر
                    </a>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>التاجر</th>
                                <th>معلومات الاتصال</th>
                                <th>الإحصائيات</th>
                                <th>المبلغ المستحق</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th width="200">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($traders as $trader): ?>
                            <tr class="transaction-row">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary bg-soft rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <i class="fas fa-user text-primary"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($trader['name']); ?></h6>
                                            <?php if ($trader['contact_person']): ?>
                                            <small class="text-muted">
                                                المسؤول: <?php echo htmlspecialchars($trader['contact_person']); ?>
                                            </small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>

                                <td>
                                    <div>
                                        <?php if ($trader['phone']): ?>
                                        <div class="mb-1">
                                            <i class="fas fa-phone text-success me-1"></i>
                                            <span class="font-monospace" dir="ltr"><?php echo htmlspecialchars($trader['phone']); ?></span>
                                        </div>
                                        <?php endif; ?>

                                        <?php if ($trader['email']): ?>
                                        <div class="mb-1">
                                            <i class="fas fa-envelope text-info me-1"></i>
                                            <small><?php echo htmlspecialchars($trader['email']); ?></small>
                                        </div>
                                        <?php endif; ?>

                                        <?php if ($trader['address']): ?>
                                        <div>
                                            <i class="fas fa-map-marker-alt text-warning me-1"></i>
                                            <small class="text-muted"><?php echo htmlspecialchars(substr($trader['address'], 0, 30)) . (strlen($trader['address']) > 30 ? '...' : ''); ?></small>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </td>

                                <td>
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="fw-bold text-primary"><?php echo number_format($trader['total_containers']); ?></div>
                                            <small class="text-muted">حاويات</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="fw-bold text-info"><?php echo number_format($trader['total_documents']); ?></div>
                                            <small class="text-muted">مستندات</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="fw-bold text-success"><?php echo number_format($trader['total_transfers']); ?></div>
                                            <small class="text-muted">تحويلات</small>
                                        </div>
                                    </div>
                                </td>

                                <td>
                                    <div class="text-center">
                                        <?php if ($trader['price'] > 0): ?>
                                        <div class="fw-bold text-danger"><?php echo number_format($trader['price'], 0); ?></div>
                                        <small class="text-muted">د.ع</small>
                                        <?php else: ?>
                                        <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </div>
                                </td>

                                <td>
                                    <?php if ($trader['active']): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check-circle me-1"></i>نشط
                                    </span>
                                    <?php else: ?>
                                    <span class="badge bg-warning">
                                        <i class="fas fa-pause-circle me-1"></i>غير نشط
                                    </span>
                                    <?php endif; ?>
                                </td>

                                <td>
                                    <div>
                                        <?php echo convertDateFromMysql($trader['created_at']); ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo convertDateFromMysql($trader['created_at'], true); ?>
                                    </small>
                                </td>

                                <td>
                                    <div class="btn-group-sm d-flex gap-1 flex-wrap">
                                        <!-- كشف الحساب -->
                                        <a href="index.php?page=trader_statement&trader_id=<?php echo $trader['id']; ?>"
                                           class="btn btn-primary btn-sm" title="كشف الحساب">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </a>

                                        <!-- طباعة كشف الحساب -->
                                        <a href="index.php?page=trader_statement&trader_id=<?php echo $trader['id']; ?>&print=1"
                                           target="_blank" class="btn btn-success btn-sm" title="طباعة كشف الحساب">
                                            <i class="fas fa-print"></i>
                                        </a>

                                        <!-- عرض التفاصيل -->
                                        <a href="index.php?page=customers&action=view&id=<?php echo $trader['id']; ?>"
                                           class="btn btn-info btn-sm" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>

                                        <!-- تعديل -->
                                        <a href="index.php?page=customers&action=edit&id=<?php echo $trader['id']; ?>"
                                           class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>

                                        <!-- الحاويات -->
                                        <a href="index.php?page=containers&trader_filter=<?php echo $trader['id']; ?>"
                                           class="btn btn-secondary btn-sm" title="عرض الحاويات">
                                            <i class="fas fa-boxes"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <div class="card-footer bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="text-muted">
                        عرض <?php echo (($current_page - 1) * $records_per_page) + 1; ?> إلى
                        <?php echo min($current_page * $records_per_page, $total_records); ?> من
                        <?php echo $total_records; ?> سجل
                    </div>

                    <nav>
                        <ul class="pagination pagination-sm mb-0">
                            <?php if ($current_page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=traders_list&page_num=<?php echo $current_page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status_filter; ?>&sort=<?php echo str_replace('t.', '', $sort); ?>&order=<?php echo strtolower($order); ?>">
                                    السابق
                                </a>
                            </li>
                            <?php endif; ?>

                            <?php
                            $start_page = max(1, $current_page - 2);
                            $end_page = min($total_pages, $current_page + 2);

                            for ($i = $start_page; $i <= $end_page; $i++):
                            ?>
                            <li class="page-item <?php echo $i == $current_page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=traders_list&page_num=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status_filter; ?>&sort=<?php echo str_replace('t.', '', $sort); ?>&order=<?php echo strtolower($order); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                            <?php endfor; ?>

                            <?php if ($current_page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=traders_list&page_num=<?php echo $current_page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status_filter; ?>&sort=<?php echo str_replace('t.', '', $sort); ?>&order=<?php echo strtolower($order); ?>">
                                    التالي
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
            </div>
            <?php endif; ?>
        </div>

    </div>
</div>

<style>
/* Enhanced Styles */
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    position: relative;
    overflow: hidden;
}

.page-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-hover {
    transition: all 0.3s ease;
    cursor: pointer;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.stat-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.card-hover:hover .stat-icon {
    transform: scale(1.1);
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: scale(1.01);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.avatar-sm {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, rgba(var(--bs-primary-rgb), 0.1) 0%, rgba(var(--bs-primary-rgb), 0.2) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(var(--bs-primary-rgb), 0.1);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.form-control, .form-select {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-1px);
}

.border-2 {
    border-width: 2px !important;
}

.pagination-info {
    border: 1px solid #dee2e6;
}

@media (max-width: 768px) {
    .content-header {
        padding: 1rem 0;
    }

    .page-icon {
        width: 50px;
        height: 50px;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
    }

    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .avatar-sm {
        width: 35px;
        height: 35px;
    }
}
</style>
