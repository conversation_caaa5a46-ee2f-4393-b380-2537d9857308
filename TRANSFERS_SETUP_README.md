# دليل إعداد نظام التحويلات

## المشكلة
كانت هناك مشكلة في إضافة التحويلات الجديدة بسبب:
1. عدم وجود الجداول المطلوبة في قاعدة البيانات
2. مشاكل في حالة الحاويات
3. أخطاء في الكود

## الحل المطبق

### 1. إنشاء الجداول المطلوبة
تم إنشاء الملفات التالية لحل المشكلة:

#### `setup_transfers_tables.sql`
- جدول `traders` - التجار
- جدول `drivers` - السائقين  
- جدول `container_transfers` - التحويلات الرئيسية
- جدول `transfer_containers` - تفاصيل الحاويات في كل تحويل
- جدول `transfer_tracking` - تتبع حالة التحويلات
- جدول `transfer_expenses` - مصاريف التحويل

#### `setup_transfers.php`
ملف PHP لتشغيل إعداد الجداول تلقائياً

### 2. إصلاح ملف النموذج
تم تحسين `pages/transfers_form.php`:
- إصلاح معالجة البيانات
- تحسين واجهة المستخدم
- إضافة JavaScript للتفاعل
- تحسين التحقق من صحة البيانات

### 3. إصلاح حالة الحاويات
تم إنشاء `fix_container_status.php` لإصلاح:
- الحاويات المحولة بدون تحويل صحيح
- إضافة حاويات تجريبية
- تصحيح حالات الحاويات

## خطوات التشغيل

### الخطوة 1: إعداد الجداول
```
http://localhost/ccis_appis/setup_transfers.php
```

### الخطوة 2: إصلاح حالة الحاويات
```
http://localhost/ccis_appis/fix_container_status.php
```

### الخطوة 3: فحص النظام
```
http://localhost/ccis_appis/check_transfers_db.php
```

### الخطوة 4: اختبار النظام
```
http://localhost/ccis_appis/index.php?page=transfers&action=add
```

## الميزات الجديدة

### 1. واجهة محسنة
- جدول تفاعلي للحاويات
- حساب المجموع تلقائياً
- اختيار جميع الحاويات
- تأكيد قبل الإرسال

### 2. تتبع التحويلات
- سجل تتبع لكل تحويل
- حالات مختلفة للتحويل
- تسجيل المصاريف الإضافية

### 3. إدارة السائقين
- معلومات مفصلة للسائقين
- ربط المركبات بالسائقين
- حساب الأجور

## هيكل الجداول

### container_transfers
```sql
- id: معرف التحويل
- transfer_number: رقم التحويل
- transfer_date: تاريخ التحويل
- trader_id: معرف التاجر
- driver_id: معرف السائق
- total_containers: عدد الحاويات
- total_amount: المبلغ الإجمالي
- status: حالة التحويل
```

### transfer_containers
```sql
- id: معرف السجل
- transfer_id: معرف التحويل
- container_id: معرف الحاوية
- transfer_fee: رسوم النقل
- container_condition: حالة الحاوية
```

### transfer_tracking
```sql
- id: معرف السجل
- transfer_id: معرف التحويل
- status: حالة التتبع
- update_time: وقت التحديث
- location: الموقع
- notes: ملاحظات
```

### transfer_expenses
```sql
- id: معرف المصروف
- transfer_id: معرف التحويل
- expense_type: نوع المصروف
- amount: المبلغ
- description: الوصف
```

## حالات التحويل

1. **pending** - في الانتظار
2. **in_progress** - جاري التنفيذ
3. **completed** - مكتمل
4. **cancelled** - ملغى

## حالات الحاويات

1. **pending** - قيد الانتظار (متاحة للتحويل)
2. **in_progress** - قيد التنفيذ (محولة)
3. **completed** - مكتملة
4. **cancelled** - ملغاة

## الصلاحيات المطلوبة

- `transfers` - إدارة التحويلات
- `drivers` - إدارة السائقين

## ملاحظات مهمة

1. يجب تشغيل ملفات الإعداد بالترتيب المحدد
2. تأكد من وجود بيانات تجريبية للتجار والسائقين
3. الحاويات يجب أن تكون في حالة "pending" لتكون متاحة للتحويل
4. يتم تحديث حالة الحاويات تلقائياً عند إنشاء التحويل

## استكشاف الأخطاء

### مشكلة: لا توجد حاويات متاحة
**الحل**: تشغيل `fix_container_status.php`

### مشكلة: خطأ في الجداول
**الحل**: تشغيل `setup_transfers.php`

### مشكلة: لا توجد تجار أو سائقين
**الحل**: إضافة بيانات تجريبية من ملف SQL

## الدعم

في حالة وجود مشاكل، تحقق من:
1. إعدادات قاعدة البيانات
2. صلاحيات المستخدم
3. سجلات الأخطاء في PHP
