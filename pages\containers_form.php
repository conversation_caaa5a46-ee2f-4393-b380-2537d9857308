<?php
// التحقق من الصلاحيات
if (!hasPermission('containers')) {
    header('Location: index.php?page=unauthorized');
    exit;
}

// تحديد ما إذا كان الإجراء إضافة أو تعديل
$isEdit = ($action == 'edit' && $containerId > 0);

// متغيرات النموذج
$container = [
    'id' => 0,
    'container_number' => '',
    'customer_id' => '',
        'trader_id' => '',

    'container_type' => '',
    'purchase_price' => 0,
    'selling_price' => 0,
    'content' => '',
    'entry_date' => date('Y-m-d'),
    'exit_date' => null,
    'status' => 'pending',
    'notes' => ''
];

$errors = [];

// إذا كان الإجراء تعديل، استرجاع بيانات الحاوية
if ($isEdit) {
    try {
        $stmt = $db->prepare("SELECT * FROM containers WHERE id = :id");
        $stmt->execute(['id' => $containerId]);
        $containerData = $stmt->fetch();

        if (!$containerData) {
            $_SESSION['error'] = 'الحاوية غير موجودة';
            header('Location: index.php?page=containers');
            exit;
        }

        $container = array_merge($container, $containerData);
    } catch (PDOException $e) {
        $_SESSION['error'] = 'حدث خطأ أثناء استرجاع بيانات الحاوية: ' . $e->getMessage();
        header('Location: index.php?page=containers');
        exit;
    }
}

// معالجة النموذج عند الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // استلام البيانات من النموذج
    $container['container_number'] = $_POST['container_number'] ?? '';
    $container['customer_id'] = $_POST['customer_id'] ?? '';
        $container['trader_id'] = $_POST['trader_id'] ?? '';

    $container['container_type'] = $_POST['container_type'] ?? '';
    $container['purchase_price'] = !empty($_POST['purchase_price']) ? (float)preg_replace('/[^0-9.]/', '', $_POST['purchase_price']) : 0;
    $container['selling_price'] = !empty($_POST['selling_price']) ? (float)preg_replace('/[^0-9.]/', '', $_POST['selling_price']) : 0;
    $container['content'] = $_POST['content'] ?? '';
    $container['entry_date'] = !empty($_POST['entry_date']) ? $_POST['entry_date'] : '';
    $container['exit_date'] = !empty($_POST['exit_date']) ? $_POST['exit_date'] : null;
    $container['status'] = $_POST['status'] ?? 'pending';
    $container['notes'] = $_POST['notes'] ?? '';

    // التحقق من صحة البيانات
    if (empty($container['container_number'])) {
        $errors['container_number'] = 'يرجى إدخال رقم الحاوية';
    }

    if (empty($container['customer_id'])) {
        $errors['customer_id'] = 'يرجى اختيار الشركة';
    }
  if (empty($container['trader_id'])) {
        $errors['trader_id'] = 'يرجى اختيار التاجر';
    }
    if (empty($container['entry_date'])) {
        $errors['entry_date'] = 'يرجى إدخال تاريخ الاستلام';
    }

    // التحقق من عدم تكرار رقم الحاوية
    try {
        $stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM containers
            WHERE container_number = :container_number
            AND id != :id
        ");
        $stmt->execute([
            'container_number' => $container['container_number'],
            'id' => $isEdit ? $containerId : 0
        ]);

        if ($stmt->fetch()['count'] > 0) {
            $errors['container_number'] = 'رقم الحاوية مستخدم بالفعل';
        }
    } catch (PDOException $e) {
        $errors['general'] = 'حدث خطأ أثناء التحقق من رقم الحاوية: ' . $e->getMessage();
    }

    // إذا لم تكن هناك أخطاء، حفظ البيانات
    if (empty($errors)) {
        try {
            if ($isEdit) {
                // تحديث الحاوية الموجودة
                $stmt = $db->prepare("
                    UPDATE containers
                    SET container_number = :container_number,
                        customer_id = :customer_id,
                                                trader_id = :trader_id,

                        container_type = :container_type,
                        purchase_price = :purchase_price,
                        selling_price = :selling_price,
                        content = :content,
                        entry_date = :entry_date,
                        exit_date = :exit_date,
                        status = :status,
                        notes = :notes,
                        weight = :weight
                    WHERE id = :id
                ");

                $params = [
                    'container_number' => $container['container_number'],
                    'customer_id' => (int)$container['customer_id'],
                     'trader_id' => (int)$container['trader_id'],

                    'container_type' => $container['container_type'] ?? '',
                    'purchase_price' => (float)$container['purchase_price'],
                    'selling_price' => (float)$container['selling_price'],
                    'content' => $container['content'] ?? '',
                    'entry_date' => $container['entry_date'],
                    'exit_date' => $container['exit_date'] ?: null,
                    'status' => $container['status'],
                    'notes' => $container['notes'] ?? '',
                    'weight' => null,
                    'id' => $containerId
                ];

                $stmt->execute($params);

                // تسجيل النشاط
                $activityStmt = $db->prepare("
                    INSERT INTO activity_log (user_id, activity_type, description, ip_address)
                    VALUES (:user_id, 'update_container', :description, :ip_address)
                ");
                $activityStmt->execute([
                    'user_id' => getCurrentUserId(),
                    'description' => 'تم تحديث بيانات الحاوية: ' . $container['container_number'],
                    'ip_address' => $_SERVER['REMOTE_ADDR']
                ]);

                $_SESSION['success'] = 'تم تحديث بيانات الحاوية "' . htmlspecialchars($container['container_number']) . '" بنجاح';
            } else {
                // إضافة حاوية جديدة
                $stmt = $db->prepare("
                    INSERT INTO containers (
                        container_number, customer_id, trader_id, container_type, purchase_price, selling_price, content,
                        entry_date, exit_date, status, notes, weight
                    ) VALUES (
                        :container_number, :customer_id, :trader_id, :container_type, :purchase_price, :selling_price, :content,
                        :entry_date, :exit_date, :status, :notes, :weight
                    )
                ");

                $params = [
                    'container_number' => $container['container_number'],
                    'customer_id' => (int)$container['customer_id'],
                    'trader_id' => (int)$container['trader_id'],

                    'container_type' => $container['container_type'] ?? '',
                    'purchase_price' => (float)$container['purchase_price'],
                    'selling_price' => (float)$container['selling_price'],
                    'content' => $container['content'] ?? '',
                    'entry_date' => $container['entry_date'],
                    'exit_date' => $container['exit_date'] ?: null,
                    'status' => $container['status'],
                    'notes' => $container['notes'] ?? '',
                    'weight' => null
                ];
                $stmt->execute($params);

                // الحصول على معرف الحاوية المضافة
                $newContainerId = $db->lastInsertId();

                // تسجيل النشاط
                $activityStmt = $db->prepare("
                    INSERT INTO activity_log (user_id, activity_type, description, ip_address)
                    VALUES (:user_id, 'add_container', :description, :ip_address)
                ");
                $activityStmt->execute([
                    'user_id' => getCurrentUserId(),
                    'description' => 'تمت إضافة حاوية جديدة: ' . $container['container_number'],
                    'ip_address' => $_SERVER['REMOTE_ADDR']
                ]);

                $_SESSION['success'] = 'تمت إضافة الحاوية "' . htmlspecialchars($container['container_number']) . '" بنجاح';
            }

            // إعادة التوجيه إلى قائمة الحاويات
            // $_SESSION['redirect'] = 'index.php?page=containers';
             // إعداد رسالة النجاح وإعادة التوجيه
            $redirectUrl = $isEdit ? 'index.php?page=containers&action=edit&id='. $containerId : 'index.php?page=containers';
            
            echo "<script>
                window.location.href = '$redirectUrl';
            </script>";
            
            // إغلاق اتصال قاعدة البيانات إذا كان موجوداً
            if(isset($db)) {
                $db = null;
            }
            exit();
            // return;
        } catch (PDOException $e) {
            $errors['general'] = 'حدث خطأ أثناء حفظ البيانات: ' . $e->getMessage();
        }
    }
}

// الحصول على قائمة الشركات
try {
    $customersStmt = $db->query("SELECT id, name FROM customers ORDER BY name");
    $customers = $customersStmt->fetchAll();
} catch (PDOException $e) {
    $errors['general'] = 'حدث خطأ أثناء استرجاع قائمة الشركات: ' . $e->getMessage();
    $customers = [];
}



try {
    $tradersStmt = $db->query("SELECT id, name FROM traders ORDER BY name");
    $traders = $tradersStmt->fetchAll();
} catch (PDOException $e) {
    $errors['general'] = 'حدث خطأ أثناء استرجاع قائمة الشركات: ' . $e->getMessage();
    $traders = [];
}
?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?php echo $isEdit ? 'تعديل بيانات الحاوية' : 'إضافة حاوية جديدة'; ?></h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php?page=containers">إدارة الحاويات</a></li>
                    <li class="breadcrumb-item active"><?php echo $isEdit ? 'تعديل' : 'إضافة جديدة'; ?></li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- Alerts -->
        <?php if (isset($errors['general'])): ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <h5><i class="icon fas fa-ban"></i> خطأ!</h5>
                <?php echo $errors['general']; ?>
            </div>
        <?php endif; ?>

        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-<?php echo $isEdit ? 'edit' : 'plus-circle'; ?>"></i>
                    <?php echo $isEdit ? 'تعديل بيانات الحاوية: ' . htmlspecialchars($container['container_number']) : 'بيانات الحاوية الجديدة'; ?>
                </h3>
                <div class="card-tools">
                    <a href="index.php?page=containers" class="btn btn-tool">
                        <i class="fas fa-arrow-right"></i> العودة إلى القائمة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- نموذج بعمودين -->
                <form method="post" action="index.php?page=containers&action=<?php echo $action; ?><?php echo $isEdit ? '&id=' . $containerId : ''; ?>">
                    <div class="row">
                        <!-- العمود الأول -->
                        <div class="col-md-6">
                            <div class="form-group">
    <label for="container_number">
        <i class="fas fa-barcode"></i> رقم الحاوية <span class="text-danger">*</span>
    </label>
    <input type="text" class="form-control <?php echo isset($errors['container_number']) ? 'is-invalid' : ''; ?>"
           id="container_number" name="container_number"
           value="<?php echo htmlspecialchars(strtoupper($container['container_number'])); ?>"
           placeholder="أدخل رقم الحاوية" 
           oninput="this.value = this.value.toUpperCase()"
           required>
    <?php if (isset($errors['container_number'])): ?>
        <span class="text-danger"><?php echo $errors['container_number']; ?></span>
    <?php endif; ?>
</div>

                            <div class="form-group">
                                <label for="customer_id">
                                    <i class="fas fa-user"></i> الشركة <span class="text-danger">*</span>
                                </label>
                                <div class="searchable-select-wrapper">
                                    <select class="form-control searchable-select <?php echo isset($errors['customer_id']) ? 'is-invalid' : ''; ?>"
                                            id="customer_id" name="customer_id" required data-placeholder="اختر الشركة أو ابحث...">
                                        <option value="">اختر الشركة</option>
                                        <?php foreach ($customers as $customer): ?>
                                            <option value="<?php echo $customer['id']; ?>" <?php echo $container['customer_id'] == $customer['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($customer['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <?php if (isset($errors['customer_id'])): ?>
                                    <span class="text-danger"><?php echo $errors['customer_id']; ?></span>
                                <?php endif; ?>
                            </div>
                            <div class="form-group">
                                <label for="trader_id">
                                    <i class="fas fa-user-tie"></i> التاجر <span class="text-danger">*</span>
                                </label>
                                <div class="searchable-select-wrapper">
                                    <select class="form-control searchable-select <?php echo isset($errors['trader_id']) ? 'is-invalid' : ''; ?>"
                                            id="trader_id" name="trader_id" required data-placeholder="اختر التاجر أو ابحث...">
                                        <option value="">اختر التاجر</option>
                                        <?php foreach ($traders as $trader): ?>
                                            <option value="<?php echo $trader['id']; ?>" <?php echo $container['trader_id'] == $trader['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($trader['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <?php if (isset($errors['trader_id'])): ?>
                                    <span class="text-danger"><?php echo $errors['trader_id']; ?></span>
                                <?php endif; ?>
                            </div>
                            <div class="form-group">
                                <label for="container_type">
                                    <i class="fas fa-box"></i> حجم الحاوية
                                </label>
                                <div class="searchable-select-wrapper">
                                    <select class="form-control searchable-select" id="container_type" name="container_type" data-placeholder="اختر حجم الحاوية...">
                                        <option value="">اختر حجم الحاوية</option>
                                        <option value="20" <?php echo ($container['container_type'] ?? '') == '20' ? 'selected' : ''; ?>>20 قدم</option>
                                        <option value="40" <?php echo ($container['container_type'] ?? '') == '40' ? 'selected' : ''; ?>>40 قدم</option>
                                        <option value="45" <?php echo ($container['container_type'] ?? '') == '45' ? 'selected' : ''; ?>>45 قدم</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="purchase_price">
                                    <i class="fas fa-money-bill-wave"></i> سعر الكلفة (د.ع) <span id="purchase_price_words" class="text-muted small"></span>
                                </label>
                                <input type="text" class="form-control" id="purchase_price" name="purchase_price"
                                       value="<?php echo $container['purchase_price'] ? number_format($container['purchase_price']) : ''; ?>"
                                       placeholder="سعر شراء الحاوية بالدينار العراقي"
                                       oninput="formatNumber(this)">
                            </div>

                            <div class="form-group">
                                <label for="entry_date">
                                    <i class="fas fa-calendar-plus"></i> تاريخ الاستلام <span class="text-danger">*</span>
                                </label>
                                <input type="date" class="form-control <?php echo isset($errors['entry_date']) ? 'is-invalid' : ''; ?>"
                                       id="entry_date" name="entry_date"
                                       value="<?php echo $container['entry_date'] ? date('Y-m-d', strtotime($container['entry_date'])) : date('Y-m-d'); ?>" required>
                                <?php if (isset($errors['entry_date'])): ?>
                                    <span class="text-danger"><?php echo $errors['entry_date']; ?></span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- العمود الثاني -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="selling_price">
                                    <i class="fas fa-hand-holding-usd"></i> سعر البيع (د.ع) <span id="selling_price_words" class="text-muted small"></span>
                                </label>
                                <input type="text" class="form-control" id="selling_price" name="selling_price"
                                       value="<?php echo $container['selling_price'] ? number_format($container['selling_price']) : ''; ?>"
                                       placeholder="سعر بيع الحاوية بالدينار العراقي"
                                       oninput="formatNumber(this)">
                            </div>

                            <div class="form-group">
                                <label for="content">
                                    <i class="fas fa-list"></i> محتوى الحاوية
                                </label>
                                <input type="text" class="form-control" id="content" name="content"
                                       value="<?php echo htmlspecialchars($container['content'] ?? ''); ?>"
                                       placeholder="وصف محتوى الحاوية">
                            </div>

                            <div class="form-group">
                                <label for="exit_date">
                                    <i class="fas fa-calendar-minus"></i> تاريخ الخروج
                                </label>
                                <input type="date" class="form-control" id="exit_date" name="exit_date"
                                       value="<?php echo $container['exit_date'] ? date('Y-m-d', strtotime($container['exit_date'])) : ''; ?>">
                            </div>

                            <div class="form-group">
                                <label for="status">
                                    <i class="fas fa-flag"></i> الحالة
                                </label>
                                <div class="searchable-select-wrapper">
                                    <select class="form-control searchable-select" id="status" name="status" data-placeholder="اختر الحالة...">
                                        <option value="pending" <?php echo $container['status'] == 'pending' ? 'selected' : ''; ?>>قيد الانتظار</option>
                                        <option value="in_progress" <?php echo $container['status'] == 'in_progress' ? 'selected' : ''; ?>>قيد التنفيذ</option>
                                        <option value="completed" <?php echo $container['status'] == 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                                        <option value="cancelled" <?php echo $container['status'] == 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="notes">
                                    <i class="fas fa-sticky-note"></i> ملاحظات
                                </label>
                                <textarea class="form-control" id="notes" name="notes" rows="4"
                                          placeholder="أي ملاحظات إضافية حول الحاوية"><?php echo htmlspecialchars($container['notes']); ?></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Info Alert -->
                    <div class="callout callout-info">
                        <h5><i class="fas fa-info-circle"></i> ملاحظة:</h5>
                        يرجى التأكد من ملء جميع الحقول المطلوبة (المشار إليها بعلامة <span class="text-danger">*</span>) قبل الضغط على زر الحفظ.
                    </div>

                    <!-- Action Buttons -->
                    <div class="row">
                        <div class="col-12 text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save"></i> <?php echo $isEdit ? 'حفظ التغييرات' : 'إضافة الحاوية'; ?>
                            </button>
                            <a href="index.php?page=containers" class="btn btn-secondary btn-lg ml-2">
                                <i class="fas fa-times"></i> إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->

<script>
// دالة لتنسيق الأرقام بفواصل
function formatNumber(input) {
    // إزالة جميع الأحرف غير الرقمية
    let num = input.value.replace(/\D/g,'');
    
    // تحويل إلى رقم وتنسيقه بفواصل
    if(num.length > 0) {
        let formatted = new Intl.NumberFormat().format(num);
        input.value = formatted;
    } else {
        input.value = '';
    }
    
    // تحديث السعر كتابة
    updatePriceInWords(input);
}

// دالة لتحويل الأرقام إلى كلمات
function numberToArabicWords(number) {
    const units = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
    const teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];
    const tens = ['', 'عشرة', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
    const hundreds = ['', 'مائة', 'مئتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'];
    const scales = ['', 'ألف', 'مليون', 'مليار', 'تريليون'];
    
    if (number == 0) return 'صفر';
    
    let words = [];
    let scaleIndex = 0;
    
    while (number > 0) {
        let chunk = number % 1000;
        number = Math.floor(number / 1000);
        
        if (chunk != 0) {
            let chunkWords = [];
            
            // المئات
            let hundred = Math.floor(chunk / 100);
            if (hundred > 0) {
                chunkWords.push(hundreds[hundred]);
            }
            
            // العشرات والآحاد
            let remainder = chunk % 100;
            if (remainder > 0) {
                if (remainder < 10) {
                    chunkWords.push(units[remainder]);
                } else if (remainder < 20) {
                    chunkWords.push(teens[remainder - 10]);
                } else {
                    let unit = remainder % 10;
                    let ten = Math.floor(remainder / 10);
                    if (unit > 0) {
                        chunkWords.push(units[unit] + ' و' + tens[ten]);
                    } else {
                        chunkWords.push(tens[ten]);
                    }
                }
            }
            
            if (scaleIndex > 0) {
                chunkWords.push(scales[scaleIndex]);
            }
            
            words.unshift(chunkWords.join(' و'));
        }
        
        scaleIndex++;
    }
    
    return words.join(' و') + ' دينار عراقي';
}

// دالة لتحديث السعر كتابة
function updatePriceInWords(input) {
    let num = input.value.replace(/\D/g,'');
    let priceInWords = '';
    
    if(num.length > 0) {
        let number = parseInt(num);
        priceInWords = numberToArabicWords(number);
    }
    
    let wordsElement = document.getElementById(input.id + '_words');
    if(wordsElement) {
        wordsElement.textContent = priceInWords;
    }
}

// تهيئة الحقول عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تنسيق الحقول الموجودة
    ['purchase_price', 'selling_price'].forEach(id => {
        let input = document.getElementById(id);
        if(input && input.value) {
            let num = input.value.replace(/\D/g,'');
            if(num.length > 0) {
                input.value = new Intl.NumberFormat().format(num);
                updatePriceInWords(input);
            }
        }
    });
});
</script>

<?php if (isset($_SESSION['redirect'])): ?>
<script>
    // إظهار إشعار نجاح قبل التوجيه
    <?php if (isset($_SESSION['success'])): ?>
    // إنشاء إشعار مؤقت
    const successAlert = document.createElement('div');
    successAlert.className = 'alert alert-success alert-dismissible position-fixed';
    successAlert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    successAlert.innerHTML = `
        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
        <h5><i class="icon fas fa-check"></i> نجح!</h5>
        <?php echo addslashes($_SESSION['success']); ?>
    `;
    document.body.appendChild(successAlert);

    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        if (successAlert.parentNode) {
            successAlert.parentNode.removeChild(successAlert);
        }
    }, 3000);
    <?php endif; ?>

    // التوجيه بعد ثانية واحدة
    setTimeout(() => {
        window.location.href = '<?php echo $_SESSION['redirect']; ?>';
    }, 1000);
</script>
<?php unset($_SESSION['redirect']); endif; ?>

<!-- Searchable Select Styles and Scripts -->
<style>
/* Searchable Select Styles */
.searchable-select-wrapper {
    position: relative;
}

.searchable-select {
    position: relative;
}

.searchable-select.open {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.select-search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-top: none;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    font-size: 14px;
    background: #fff;
    outline: none;
    display: none;
}

.select-search-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.searchable-select.open + .select-search-input {
    display: block;
}

.select-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.searchable-select.open ~ .select-options {
    display: block;
}

.select-option {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s;
}

.select-option:hover {
    background-color: #f8f9fa;
}

.select-option.selected {
    background-color: #007bff;
    color: white;
}

.select-option.hidden {
    display: none;
}

.no-results {
    padding: 8px 12px;
    color: #6c757d;
    font-style: italic;
    text-align: center;
}

/* Enhanced Select Styling */
.searchable-select-wrapper .form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-left: 2.5rem;
}

.searchable-select-wrapper .form-control:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23007bff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* RTL Support */
[dir="rtl"] .searchable-select-wrapper .form-control {
    background-position: right 0.75rem center;
    padding-right: 2.5rem;
    padding-left: 0.75rem;
}

/* Loading State */
.searchable-select.loading {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'%3e%3ccircle cx='12' cy='12' r='10' stroke='%23007bff' stroke-width='4' fill='none' opacity='0.25'/%3e%3cpath fill='%23007bff' d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'/%3e%3c/svg%3e");
    background-size: 1rem 1rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>

<script>
// Searchable Select Implementation
class SearchableSelect {
    constructor(selectElement) {
        this.select = selectElement;
        this.wrapper = selectElement.closest('.searchable-select-wrapper');
        this.options = Array.from(selectElement.options);
        this.filteredOptions = [...this.options];
        this.selectedIndex = -1;
        this.isOpen = false;

        this.init();
    }

    init() {
        // Create search input
        this.searchInput = document.createElement('input');
        this.searchInput.type = 'text';
        this.searchInput.className = 'select-search-input';
        this.searchInput.placeholder = 'ابحث...';

        // Create options container
        this.optionsContainer = document.createElement('div');
        this.optionsContainer.className = 'select-options';

        // Insert elements
        this.wrapper.appendChild(this.searchInput);
        this.wrapper.appendChild(this.optionsContainer);

        // Populate options
        this.populateOptions();

        // Bind events
        this.bindEvents();
    }

    populateOptions() {
        this.optionsContainer.innerHTML = '';

        if (this.filteredOptions.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'no-results';
            noResults.textContent = 'لا توجد نتائج';
            this.optionsContainer.appendChild(noResults);
            return;
        }

        this.filteredOptions.forEach((option, index) => {
            if (option.value === '') return; // Skip empty option

            const optionElement = document.createElement('div');
            optionElement.className = 'select-option';
            optionElement.textContent = option.text;
            optionElement.dataset.value = option.value;
            optionElement.dataset.index = index;

            if (option.selected) {
                optionElement.classList.add('selected');
            }

            optionElement.addEventListener('click', () => {
                this.selectOption(option.value, option.text);
            });

            this.optionsContainer.appendChild(optionElement);
        });
    }

    bindEvents() {
        // Select click
        this.select.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggle();
        });

        // Search input
        this.searchInput.addEventListener('input', (e) => {
            this.filter(e.target.value);
        });

        // Keyboard navigation
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });

        // Close on outside click
        document.addEventListener('click', (e) => {
            if (!this.wrapper.contains(e.target)) {
                this.close();
            }
        });

        // Prevent form submission on Enter in search
        this.searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
            }
        });
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    open() {
        this.isOpen = true;
        this.select.classList.add('open');
        this.searchInput.style.display = 'block';
        this.optionsContainer.style.display = 'block';
        this.searchInput.focus();
        this.searchInput.value = '';
        this.filter('');
    }

    close() {
        this.isOpen = false;
        this.select.classList.remove('open');
        this.searchInput.style.display = 'none';
        this.optionsContainer.style.display = 'none';
        this.selectedIndex = -1;
    }

    filter(searchTerm) {
        const term = searchTerm.toLowerCase().trim();

        this.filteredOptions = this.options.filter(option => {
            if (option.value === '') return false;
            return option.text.toLowerCase().includes(term);
        });

        this.populateOptions();
        this.selectedIndex = -1;
    }

    selectOption(value, text) {
        // Update select element
        this.select.value = value;

        // Update visual state
        const options = this.optionsContainer.querySelectorAll('.select-option');
        options.forEach(opt => opt.classList.remove('selected'));

        const selectedOption = this.optionsContainer.querySelector(`[data-value="${value}"]`);
        if (selectedOption) {
            selectedOption.classList.add('selected');
        }

        // Trigger change event
        const changeEvent = new Event('change', { bubbles: true });
        this.select.dispatchEvent(changeEvent);

        this.close();
    }

    handleKeydown(e) {
        const options = this.optionsContainer.querySelectorAll('.select-option:not(.hidden)');

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.selectedIndex = Math.min(this.selectedIndex + 1, options.length - 1);
                this.highlightOption();
                break;

            case 'ArrowUp':
                e.preventDefault();
                this.selectedIndex = Math.max(this.selectedIndex - 1, 0);
                this.highlightOption();
                break;

            case 'Enter':
                e.preventDefault();
                if (this.selectedIndex >= 0 && options[this.selectedIndex]) {
                    const option = options[this.selectedIndex];
                    this.selectOption(option.dataset.value, option.textContent);
                }
                break;

            case 'Escape':
                this.close();
                break;
        }
    }

    highlightOption() {
        const options = this.optionsContainer.querySelectorAll('.select-option');
        options.forEach((opt, index) => {
            opt.classList.toggle('selected', index === this.selectedIndex);
        });

        // Scroll into view
        if (options[this.selectedIndex]) {
            options[this.selectedIndex].scrollIntoView({
                block: 'nearest'
            });
        }
    }
}

// Initialize searchable selects when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    const searchableSelects = document.querySelectorAll('.searchable-select');
    searchableSelects.forEach(select => {
        new SearchableSelect(select);
    });
});
</script>
