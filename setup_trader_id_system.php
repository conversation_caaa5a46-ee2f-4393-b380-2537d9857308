<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام trader_id للحاويات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { color: blue; background: #d1ecf1; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .warning { color: orange; background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .progress { background: #e9ecef; border-radius: 5px; height: 20px; margin: 10px 0; }
        .progress-bar { background: #007bff; height: 100%; border-radius: 5px; transition: width 0.3s; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>إعداد نظام trader_id للحاويات</h1>
    <div class="info">هذا الملف سيقوم بإعداد نظام فلترة الحاويات حسب التاجر باستخدام trader_id</div>
    
    <?php
    require_once 'config/config.php';
    
    $totalSteps = 7;
    $currentStep = 0;
    
    function updateProgress($step, $total) {
        $percentage = ($step / $total) * 100;
        echo "<div class='progress'><div class='progress-bar' style='width: {$percentage}%'></div></div>";
        echo "<div class='info'>التقدم: $step من $total خطوات مكتملة ({$percentage}%)</div>";
    }
    
    try {
        $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // الخطوة 1: فحص هيكل جدول containers
        echo "<div class='step'>";
        echo "<h3>الخطوة 1: فحص هيكل جدول containers</h3>";
        
        $stmt = $db->query("DESCRIBE containers");
        $columns = $stmt->fetchAll();
        
        $hasTraderIdColumn = false;
        foreach ($columns as $column) {
            if ($column['Field'] == 'trader_id') {
                $hasTraderIdColumn = true;
                break;
            }
        }
        
        if ($hasTraderIdColumn) {
            echo "<div class='success'>✓ عمود trader_id موجود</div>";
        } else {
            echo "<div class='warning'>✗ عمود trader_id غير موجود - سيتم إضافته</div>";
        }
        
        $currentStep++;
        updateProgress($currentStep, $totalSteps);
        echo "</div>";
        
        // الخطوة 2: إضافة عمود trader_id
        echo "<div class='step'>";
        echo "<h3>الخطوة 2: إضافة عمود trader_id</h3>";
        
        if (!$hasTraderIdColumn) {
            try {
                $db->exec("ALTER TABLE containers ADD COLUMN trader_id INT(11) DEFAULT NULL AFTER customer_id");
                echo "<div class='success'>✓ تم إضافة عمود trader_id</div>";
                
                $db->exec("ALTER TABLE containers ADD INDEX idx_trader_id (trader_id)");
                echo "<div class='success'>✓ تم إضافة فهرس لعمود trader_id</div>";
                
                $hasTraderIdColumn = true;
            } catch (PDOException $e) {
                echo "<div class='error'>خطأ في إضافة عمود trader_id: " . $e->getMessage() . "</div>";
            }
        } else {
            echo "<div class='success'>عمود trader_id موجود بالفعل</div>";
        }
        
        $currentStep++;
        updateProgress($currentStep, $totalSteps);
        echo "</div>";
        
        // الخطوة 3: نسخ البيانات من customer_id
        echo "<div class='step'>";
        echo "<h3>الخطوة 3: نسخ البيانات من customer_id إلى trader_id</h3>";
        
        if ($hasTraderIdColumn) {
            try {
                $stmt = $db->prepare("UPDATE containers SET trader_id = customer_id WHERE trader_id IS NULL AND customer_id IS NOT NULL");
                $stmt->execute();
                $updatedRows = $stmt->rowCount();
                echo "<div class='success'>✓ تم تحديث $updatedRows حاوية</div>";
            } catch (PDOException $e) {
                echo "<div class='error'>خطأ في نسخ البيانات: " . $e->getMessage() . "</div>";
            }
        }
        
        $currentStep++;
        updateProgress($currentStep, $totalSteps);
        echo "</div>";
        
        // الخطوة 4: إضافة بيانات تجريبية
        echo "<div class='step'>";
        echo "<h3>الخطوة 4: إضافة بيانات تجريبية</h3>";
        
        $stmt = $db->query("SELECT COUNT(*) as count FROM containers WHERE trader_id IS NOT NULL");
        $existingContainers = $stmt->fetch()['count'];
        
        if ($existingContainers < 10) {
            echo "<div class='info'>عدد الحاويات قليل ($existingContainers). سيتم إضافة بيانات تجريبية...</div>";
            
            // إضافة حاويات تجريبية
            $testContainers = [
                ['CONT-TR1-TEST1', 1, 1, '20 قدم', 'أجهزة إلكترونية', 1200000, 1000000],
                ['CONT-TR1-TEST2', 1, 1, '40 قدم', 'قطع غيار سيارات', 1800000, 1500000],
                ['CONT-TR1-TEST3', 1, 1, '20 قدم', 'مواد كيميائية', 900000, 750000],
                ['CONT-TR2-TEST1', 2, 2, '40 قدم', 'أثاث منزلي', 2000000, 1600000],
                ['CONT-TR2-TEST2', 2, 2, '20 قدم', 'أدوات كهربائية', 1100000, 900000],
                ['CONT-TR2-TEST3', 2, 2, '40 قدم', 'مواد بناء', 1700000, 1400000],
                ['CONT-TR3-TEST1', 3, 3, '20 قدم', 'كتب ومجلات', 400000, 350000],
                ['CONT-TR3-TEST2', 3, 3, '40 قدم', 'أدوات رياضية', 1300000, 1100000],
                ['CONT-TR3-TEST3', 3, 3, '20 قدم', 'مستحضرات تجميل', 700000, 600000]
            ];
            
            $addedCount = 0;
            foreach ($testContainers as $container) {
                try {
                    $stmt = $db->prepare("
                        INSERT IGNORE INTO containers 
                        (container_number, trader_id, customer_id, entry_date, status, container_type, content, selling_price, purchase_price) 
                        VALUES (?, ?, ?, CURDATE(), 'pending', ?, ?, ?, ?)
                    ");
                    $stmt->execute($container);
                    if ($stmt->rowCount() > 0) {
                        $addedCount++;
                    }
                } catch (Exception $e) {
                    echo "<div class='error'>خطأ في إضافة الحاوية " . $container[0] . ": " . $e->getMessage() . "</div>";
                }
            }
            
            echo "<div class='success'>✓ تم إضافة $addedCount حاوية تجريبية</div>";
        } else {
            echo "<div class='success'>يوجد $existingContainers حاوية في النظام</div>";
        }
        
        $currentStep++;
        updateProgress($currentStep, $totalSteps);
        echo "</div>";
        
        // الخطوة 5: اختبار AJAX endpoint
        echo "<div class='step'>";
        echo "<h3>الخطوة 5: اختبار AJAX endpoint</h3>";
        
        // اختبار أول تاجر لديه حاويات
        $stmt = $db->query("
            SELECT t.id, t.name, COUNT(c.id) as containers_count
            FROM traders t
            LEFT JOIN containers c ON t.id = c.trader_id AND c.status = 'pending'
            GROUP BY t.id, t.name
            HAVING containers_count > 0
            ORDER BY containers_count DESC
            LIMIT 1
        ");
        $testTrader = $stmt->fetch();
        
        if ($testTrader) {
            echo "<div class='info'>اختبار مع التاجر: " . htmlspecialchars($testTrader['name']) . " (ID: " . $testTrader['id'] . ")</div>";
            
            // اختبار مباشر للـ endpoint
            $testUrl = "ajax/get_trader_containers.php?trader_id=" . $testTrader['id'];
            
            if (function_exists('curl_init')) {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/" . $testUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($httpCode == 200) {
                    $data = json_decode($response, true);
                    if ($data && isset($data['success']) && $data['success']) {
                        echo "<div class='success'>✓ AJAX endpoint يعمل بنجاح - تم جلب " . $data['count'] . " حاوية</div>";
                    } else {
                        echo "<div class='error'>AJAX endpoint يعيد خطأ: " . ($data['error'] ?? 'خطأ غير معروف') . "</div>";
                    }
                } else {
                    echo "<div class='error'>AJAX endpoint لا يستجيب (HTTP $httpCode)</div>";
                }
            } else {
                echo "<div class='warning'>لا يمكن اختبار AJAX endpoint (curl غير متاح)</div>";
            }
        } else {
            echo "<div class='warning'>لا يوجد تجار لديهم حاويات للاختبار</div>";
        }
        
        $currentStep++;
        updateProgress($currentStep, $totalSteps);
        echo "</div>";
        
        // الخطوة 6: إحصائيات النظام
        echo "<div class='step'>";
        echo "<h3>الخطوة 6: إحصائيات النظام</h3>";
        
        // إحصائيات التجار والحاويات
        $stmt = $db->query("
            SELECT 
                t.name as trader_name,
                COUNT(c.id) as total_containers,
                SUM(CASE WHEN c.status = 'pending' THEN 1 ELSE 0 END) as pending_containers,
                SUM(c.selling_price) as total_value
            FROM traders t
            LEFT JOIN containers c ON t.id = c.trader_id
            GROUP BY t.id, t.name
            ORDER BY total_containers DESC
            LIMIT 10
        ");
        $stats = $stmt->fetchAll();
        
        if (count($stats) > 0) {
            echo "<div class='success'>✓ إحصائيات التجار والحاويات:</div>";
            echo "<table>";
            echo "<tr><th>اسم التاجر</th><th>إجمالي الحاويات</th><th>المتاحة للتحويل</th><th>إجمالي القيمة</th></tr>";
            foreach ($stats as $stat) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($stat['trader_name']) . "</td>";
                echo "<td>" . $stat['total_containers'] . "</td>";
                echo "<td>" . $stat['pending_containers'] . "</td>";
                echo "<td>" . number_format($stat['total_value']) . " د.ع</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        $currentStep++;
        updateProgress($currentStep, $totalSteps);
        echo "</div>";
        
        // الخطوة 7: اختبار نهائي
        echo "<div class='step'>";
        echo "<h3>الخطوة 7: اختبار نهائي للنظام</h3>";
        
        echo "<div class='info'>اختبار تفاعلي للنظام:</div>";
        echo "<div style='margin: 15px 0;'>";
        echo "<label>اختر التاجر للاختبار:</label><br>";
        echo "<select id='testTraderSelect' style='padding: 8px; margin: 5px; width: 300px;'>";
        echo "<option value=''>اختر التاجر</option>";
        
        $stmt = $db->query("SELECT id, name FROM traders ORDER BY name");
        $allTraders = $stmt->fetchAll();
        foreach ($allTraders as $trader) {
            echo "<option value='" . $trader['id'] . "'>" . htmlspecialchars($trader['name']) . "</option>";
        }
        echo "</select><br>";
        echo "<button onclick='testTraderContainers()' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px;'>اختبار جلب الحاويات</button>";
        echo "</div>";
        echo "<div id='testResult'></div>";
        
        $currentStep++;
        updateProgress($currentStep, $totalSteps);
        echo "</div>";
        
        // النتيجة النهائية
        echo "<div class='step'>";
        echo "<h3>✅ تم إعداد نظام trader_id بنجاح!</h3>";
        echo "<div class='success'>النظام جاهز للاستخدام مع فلترة الحاويات حسب التاجر</div>";
        echo "<div style='margin: 20px 0;'>";
        echo "<a href='index.php?page=transfers&action=add' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>🚀 اختبار نظام التحويلات</a>";
        echo "<a href='test_trader_containers.php' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>🔧 اختبار AJAX</a>";
        echo "<a href='fix_trader_id_column.php' style='background: #6c757d; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>📊 تشخيص متقدم</a>";
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
    }
    ?>
    
    <script>
    function testTraderContainers() {
        const traderId = document.getElementById('testTraderSelect').value;
        const resultDiv = document.getElementById('testResult');
        
        if (!traderId) {
            resultDiv.innerHTML = '<div style="color: red; background: #f8d7da; padding: 10px; border-radius: 5px;">يرجى اختيار التاجر</div>';
            return;
        }
        
        resultDiv.innerHTML = '<div style="color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px;">جاري اختبار جلب الحاويات...</div>';
        
        fetch(`ajax/get_trader_containers.php?trader_id=${traderId}`)
            .then(response => response.json())
            .then(data => {
                console.log('Test Response:', data);
                
                if (data.success) {
                    let html = `<div style="color: green; background: #d4edda; padding: 10px; border-radius: 5px;">✅ نجح الاختبار! تم جلب ${data.count} حاوية</div>`;
                    
                    if (data.containers && data.containers.length > 0) {
                        html += '<table style="margin-top: 10px; width: 100%; border-collapse: collapse;">';
                        html += '<tr style="background: #f2f2f2;"><th style="border: 1px solid #ddd; padding: 8px;">رقم الحاوية</th><th style="border: 1px solid #ddd; padding: 8px;">المحتوى</th><th style="border: 1px solid #ddd; padding: 8px;">النوع</th><th style="border: 1px solid #ddd; padding: 8px;">السعر</th></tr>';
                        
                        data.containers.slice(0, 5).forEach(container => {
                            html += `<tr>
                                <td style="border: 1px solid #ddd; padding: 8px;">${container.container_number}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${container.content || 'غير محدد'}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${container.container_type || 'غير محدد'}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">${container.selling_price}</td>
                            </tr>`;
                        });
                        
                        html += '</table>';
                        
                        if (data.containers.length > 5) {
                            html += `<div style="color: #666; margin-top: 5px;">... و ${data.containers.length - 5} حاوية أخرى</div>`;
                        }
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div style="color: red; background: #f8d7da; padding: 10px; border-radius: 5px;">❌ خطأ: ${data.error || 'خطأ غير معروف'}</div>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = `<div style="color: red; background: #f8d7da; padding: 10px; border-radius: 5px;">❌ خطأ في الاتصال: ${error.message}</div>`;
            });
    }
    </script>
</body>
</html>
