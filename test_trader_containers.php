<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار فلترة حاويات التاجر</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { color: blue; background: #d1ecf1; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
        .form-group { margin: 10px 0; }
        .form-control { padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 200px; }
        .btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>اختبار فلترة حاويات التاجر</h1>
    
    <?php
    require_once 'config/config.php';
    
    try {
        $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار 1: فحص التجار المتاحين</h3>";
        
        // جلب التجار
        try {
            $stmt = $db->prepare("SELECT id, name FROM traders ORDER BY name LIMIT 10");
            $stmt->execute();
            $traders = $stmt->fetchAll();
            
            if (count($traders) > 0) {
                echo "<div class='success'>✓ تم العثور على " . count($traders) . " تاجر</div>";
                echo "<table>";
                echo "<tr><th>المعرف</th><th>الاسم</th><th>عدد الحاويات</th></tr>";
                
                foreach ($traders as $trader) {
                    // عدد حاويات كل تاجر
                    $containerStmt = $db->prepare("SELECT COUNT(*) as count FROM containers WHERE trader_id = ? AND status = 'pending'");
                    $containerStmt->execute([$trader['id']]);
                    $containerCount = $containerStmt->fetch()['count'];
                    
                    echo "<tr>";
                    echo "<td>" . $trader['id'] . "</td>";
                    echo "<td>" . htmlspecialchars($trader['name']) . "</td>";
                    echo "<td>" . $containerCount . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<div class='error'>لا توجد تجار في النظام</div>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>خطأ في جلب التجار: " . $e->getMessage() . "</div>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار 2: اختبار AJAX endpoint</h3>";
        
        if (count($traders) > 0) {
            $testTraderId = $traders[0]['id'];
            echo "<div class='info'>اختبار مع التاجر: " . htmlspecialchars($traders[0]['name']) . " (ID: $testTraderId)</div>";
            
            echo "<div class='form-group'>";
            echo "<label>اختر التاجر:</label>";
            echo "<select id='traderSelect' class='form-control' onchange='testLoadContainers()'>";
            echo "<option value=''>اختر التاجر</option>";
            foreach ($traders as $trader) {
                echo "<option value='" . $trader['id'] . "'>" . htmlspecialchars($trader['name']) . "</option>";
            }
            echo "</select>";
            echo "</div>";
            
            echo "<div class='form-group'>";
            echo "<button class='btn' onclick='testLoadContainers()'>اختبار تحميل الحاويات</button>";
            echo "</div>";
            
            echo "<div id='testResults'></div>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار 3: فحص هيكل قاعدة البيانات</h3>";
        
        // فحص جدول containers
        try {
            $stmt = $db->query("DESCRIBE containers");
            $columns = $stmt->fetchAll();
            
            echo "<div class='success'>✓ جدول containers موجود</div>";
            echo "<table>";
            echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th></tr>";
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>" . $column['Field'] . "</td>";
                echo "<td>" . $column['Type'] . "</td>";
                echo "<td>" . $column['Null'] . "</td>";
                echo "<td>" . $column['Key'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // فحص البيانات
            $stmt = $db->query("SELECT COUNT(*) as total FROM containers");
            $totalContainers = $stmt->fetch()['total'];
            
            $stmt = $db->query("SELECT COUNT(*) as pending FROM containers WHERE status = 'pending'");
            $pendingContainers = $stmt->fetch()['pending'];
            
            echo "<div class='info'>إجمالي الحاويات: $totalContainers | المتاحة للتحويل: $pendingContainers</div>";
            
        } catch (Exception $e) {
            echo "<div class='error'>خطأ في فحص جدول containers: " . $e->getMessage() . "</div>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
    }
    ?>
    
    <script>
    function testLoadContainers() {
        const traderId = document.getElementById('traderSelect').value;
        const resultsDiv = document.getElementById('testResults');
        
        if (!traderId) {
            resultsDiv.innerHTML = '<div class="error">يرجى اختيار التاجر</div>';
            return;
        }
        
        resultsDiv.innerHTML = '<div class="info">جاري تحميل الحاويات...</div>';
        
        fetch(`ajax/get_trader_containers.php?trader_id=${traderId}`)
            .then(response => response.json())
            .then(data => {
                console.log('Response:', data);
                
                if (data.success) {
                    let html = `<div class="success">✓ تم تحميل ${data.count} حاوية بنجاح</div>`;
                    
                    if (data.containers.length > 0) {
                        html += '<table>';
                        html += '<tr><th>المعرف</th><th>رقم الحاوية</th><th>المحتوى</th><th>النوع</th><th>السعر</th></tr>';
                        
                        data.containers.forEach(container => {
                            html += `<tr>
                                <td>${container.id}</td>
                                <td>${container.container_number}</td>
                                <td>${container.content || 'غير محدد'}</td>
                                <td>${container.container_type || 'غير محدد'}</td>
                                <td>${container.selling_price}</td>
                            </tr>`;
                        });
                        
                        html += '</table>';
                    }
                    
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `<div class="error">خطأ: ${data.error}</div>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resultsDiv.innerHTML = `<div class="error">خطأ في الاتصال: ${error.message}</div>`;
            });
    }
    </script>
    
    <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
        <h4>الخطوات التالية:</h4>
        <ol>
            <li>تأكد من أن جميع الاختبارات تعمل بنجاح</li>
            <li>اذهب إلى <a href="index.php?page=transfers&action=add">صفحة إضافة التحويل</a></li>
            <li>اختر تاجر وتأكد من ظهور حاوياته</li>
            <li>جرب الفلاتر المختلفة</li>
            <li>أنشئ تحويل تجريبي</li>
        </ol>
    </div>
</body>
</html>
