<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة السائقين</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { color: blue; background: #d1ecf1; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .warning { color: orange; background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
        .btn { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
    </style>
</head>
<body>
    <h1>اختبار صفحة السائقين</h1>
    
    <?php
    require_once 'config/config.php';
    
    try {
        $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار 1: فحص هيكل جدول drivers</h3>";
        
        // فحص هيكل جدول drivers
        $stmt = $db->query("DESCRIBE drivers");
        $columns = $stmt->fetchAll();
        
        echo "<div class='success'>✓ جدول drivers موجود</div>";
        echo "<table>";
        echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار 2: فحص البيانات الموجودة</h3>";
        
        // عدد السائقين
        $stmt = $db->query("SELECT COUNT(*) as count FROM drivers");
        $driversCount = $stmt->fetch()['count'];
        echo "<div class='info'>عدد السائقين: $driversCount</div>";
        
        if ($driversCount > 0) {
            // عرض بعض السائقين
            $stmt = $db->query("SELECT id, driver_name, phone, license_number, status FROM drivers LIMIT 5");
            $drivers = $stmt->fetchAll();
            
            echo "<table>";
            echo "<tr><th>المعرف</th><th>اسم السائق</th><th>الهاتف</th><th>رقم الرخصة</th><th>الحالة</th></tr>";
            foreach ($drivers as $driver) {
                echo "<tr>";
                echo "<td>" . $driver['id'] . "</td>";
                echo "<td>" . htmlspecialchars($driver['driver_name']) . "</td>";
                echo "<td>" . htmlspecialchars($driver['phone']) . "</td>";
                echo "<td>" . htmlspecialchars($driver['license_number']) . "</td>";
                echo "<td>" . $driver['status'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='warning'>لا يوجد سائقين في النظام</div>";
            echo "<div class='info'>سيتم إضافة بيانات تجريبية...</div>";
            
            // إضافة سائقين تجريبيين
            $testDrivers = [
                ['أحمد محمد', '07801234567', 'DL123456', 'شاحنة', 'TR001', 'active'],
                ['فاطمة علي', '07809876543', 'DL789012', 'نقل صغير', 'TR002', 'active'],
                ['محمد حسن', '07807654321', 'DL345678', 'شاحنة كبيرة', 'TR003', 'active']
            ];
            
            foreach ($testDrivers as $driver) {
                try {
                    $stmt = $db->prepare("
                        INSERT IGNORE INTO drivers 
                        (driver_name, phone, license_number, vehicle_type, vehicle_number, status, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, NOW())
                    ");
                    $stmt->execute($driver);
                    if ($stmt->rowCount() > 0) {
                        echo "<div class='success'>✓ تم إضافة السائق: " . $driver[0] . "</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='error'>خطأ في إضافة السائق: " . $e->getMessage() . "</div>";
                }
            }
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار 3: اختبار الاستعلامات المحدثة</h3>";
        
        // اختبار الاستعلام الأساسي
        try {
            $sql = "
                SELECT d.*, 
                       u.full_name as created_by_name,
                       (SELECT COUNT(*) FROM container_transfers WHERE driver_id = d.id) as total_transfers,
                       (SELECT COALESCE(SUM(total_amount), 0) FROM container_transfers WHERE driver_id = d.id) as total_revenue
                FROM drivers d
                LEFT JOIN users u ON d.created_by = u.id
                ORDER BY d.created_at DESC
                LIMIT 5
            ";
            
            $stmt = $db->prepare($sql);
            $stmt->execute();
            $drivers = $stmt->fetchAll();
            
            echo "<div class='success'>✓ الاستعلام الأساسي يعمل بنجاح</div>";
            echo "<div class='info'>تم جلب " . count($drivers) . " سائق</div>";
            
        } catch (PDOException $e) {
            echo "<div class='error'>خطأ في الاستعلام الأساسي: " . $e->getMessage() . "</div>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار 4: اختبار البحث والفلترة</h3>";
        
        // اختبار البحث
        try {
            $search = 'أحمد';
            $where_conditions = [];
            $params = [];
            
            $where_conditions[] = "(d.driver_name LIKE ? OR d.license_number LIKE ? OR d.phone LIKE ? OR d.vehicle_number LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
            
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            
            $sql = "SELECT COUNT(*) FROM drivers d $where_clause";
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            $searchResults = $stmt->fetchColumn();
            
            echo "<div class='success'>✓ البحث يعمل بنجاح</div>";
            echo "<div class='info'>نتائج البحث عن '$search': $searchResults سائق</div>";
            
        } catch (PDOException $e) {
            echo "<div class='error'>خطأ في البحث: " . $e->getMessage() . "</div>";
        }
        
        // اختبار فلتر الحالة
        try {
            $status_filter = 'active';
            $where_conditions = [];
            $params = [];
            
            $where_conditions[] = "d.status = ?";
            $params[] = $status_filter;
            
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
            
            $sql = "SELECT COUNT(*) FROM drivers d $where_clause";
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            $statusResults = $stmt->fetchColumn();
            
            echo "<div class='success'>✓ فلتر الحالة يعمل بنجاح</div>";
            echo "<div class='info'>السائقين النشطين: $statusResults سائق</div>";
            
        } catch (PDOException $e) {
            echo "<div class='error'>خطأ في فلتر الحالة: " . $e->getMessage() . "</div>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار 5: اختبار الترتيب</h3>";
        
        $sortFields = ['driver_name', 'license_number', 'phone', 'status', 'created_at'];
        
        foreach ($sortFields as $sortField) {
            try {
                $sql = "
                    SELECT d.id, d.driver_name, d.phone 
                    FROM drivers d
                    LEFT JOIN users u ON d.created_by = u.id
                    ORDER BY d.$sortField DESC
                    LIMIT 1
                ";
                
                $stmt = $db->prepare($sql);
                $stmt->execute();
                $result = $stmt->fetch();
                
                if ($result) {
                    echo "<div class='success'>✓ الترتيب حسب $sortField يعمل</div>";
                } else {
                    echo "<div class='warning'>لا توجد بيانات للترتيب حسب $sortField</div>";
                }
                
            } catch (PDOException $e) {
                echo "<div class='error'>خطأ في الترتيب حسب $sortField: " . $e->getMessage() . "</div>";
            }
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار 6: اختبار الصفحة الفعلية</h3>";
        
        echo "<div class='info'>روابط الاختبار:</div>";
        echo "<p><a href='index.php?page=drivers' target='_blank' class='btn'>صفحة السائقين</a></p>";
        echo "<p><a href='index.php?page=drivers&search=أحمد' target='_blank' class='btn'>بحث عن 'أحمد'</a></p>";
        echo "<p><a href='index.php?page=drivers&status=active' target='_blank' class='btn'>السائقين النشطين</a></p>";
        echo "<p><a href='index.php?page=drivers&sort=driver_name&order=asc' target='_blank' class='btn'>ترتيب حسب الاسم</a></p>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>✅ ملخص الاختبار</h3>";
        echo "<div class='success'>تم إصلاح مشكلة ambiguous column بنجاح!</div>";
        echo "<div class='info'>";
        echo "<strong>الإصلاحات المطبقة:</strong><br>";
        echo "• إضافة alias الجدول (d.) لجميع الأعمدة في WHERE clause<br>";
        echo "• إصلاح استعلام العد ليستخدم نفس البنية<br>";
        echo "• إصلاح الترتيب ليستخدم alias الجدول<br>";
        echo "• اختبار جميع وظائف البحث والفلترة<br>";
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
    }
    ?>
</body>
</html>
