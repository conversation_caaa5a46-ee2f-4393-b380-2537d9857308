<?php
// ملف AJAX منفصل لجلب حاويات التاجر في المستندات المالية
header('Content-Type: application/json; charset=utf-8');

// تضمين ملف الإعدادات
require_once '../config/config.php';

// التحقق من وجود المعاملات المطلوبة
if (!isset($_GET['customer_id']) || empty($_GET['customer_id'])) {
    echo json_encode([
        'success' => false,
        'error' => 'معرف التاجر مطلوب',
        'debug' => $_GET
    ]);
    exit;
}

$traderId = (int)$_GET['customer_id']; // customer_id هنا يشير إلى trader_id

try {
    // إنشاء اتصال قاعدة البيانات
    $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // بناء الاستعلام الأساسي
    $sql = "SELECT id, container_number, entry_date, exit_date, purchase_price, selling_price, 
                   content, status, container_type, notes, created_at
            FROM containers 
            WHERE trader_id = ? AND status != 'cancelled'";
    
    $params = [$traderId];
    
    // إضافة فلاتر إضافية إذا كانت موجودة
    if (!empty($_GET['search'])) {
        $sql .= " AND (container_number LIKE ? OR content LIKE ?)";
        $searchTerm = '%' . $_GET['search'] . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    if (!empty($_GET['date_from'])) {
        $sql .= " AND entry_date >= ?";
        $params[] = $_GET['date_from'];
    }
    
    if (!empty($_GET['date_to'])) {
        $sql .= " AND entry_date <= ?";
        $params[] = $_GET['date_to'];
    }
    
    if (!empty($_GET['status_filter'])) {
        $sql .= " AND status = ?";
        $params[] = $_GET['status_filter'];
    }
    
    // ترتيب النتائج
    $sql .= " ORDER BY entry_date DESC";
    
    // تنفيذ الاستعلام
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $containers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تنسيق البيانات
    foreach ($containers as &$container) {
        // تنسيق التواريخ
        if ($container['entry_date']) {
            $container['entry_date_formatted'] = date('Y-m-d', strtotime($container['entry_date']));
        }
        
        if ($container['exit_date']) {
            $container['exit_date_formatted'] = date('Y-m-d', strtotime($container['exit_date']));
        }
        
        // تنسيق الأسعار
        $container['purchase_price'] = (float)$container['purchase_price'];
        $container['selling_price'] = (float)$container['selling_price'];
        
        // تنسيق النصوص
        $container['container_number'] = htmlspecialchars($container['container_number']);
        $container['content'] = htmlspecialchars($container['content'] ?? '');
        $container['notes'] = htmlspecialchars($container['notes'] ?? '');
    }
    
    // إرجاع النتيجة
    echo json_encode([
        'success' => true,
        'trader_id' => $traderId,
        'containers' => $containers,
        'count' => count($containers),
        'query_params' => $params,
        'sql' => $sql,
        'filters' => [
            'search' => $_GET['search'] ?? '',
            'date_from' => $_GET['date_from'] ?? '',
            'date_to' => $_GET['date_to'] ?? '',
            'status_filter' => $_GET['status_filter'] ?? ''
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'trader_id' => $traderId,
        'sql' => $sql ?? '',
        'params' => $params ?? []
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'خطأ عام: ' . $e->getMessage(),
        'trader_id' => $traderId
    ], JSON_UNESCAPED_UNICODE);
}
?>
