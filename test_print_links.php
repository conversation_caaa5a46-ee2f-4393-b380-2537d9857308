<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار روابط الطباعة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { color: blue; background: #d1ecf1; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
    </style>
</head>
<body>
    <h1>اختبار روابط الطباعة</h1>
    
    <?php
    require_once 'config/config.php';
    
    try {
        $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='test-section'>";
        echo "<h3>فحص الصفحات المطلوبة</h3>";
        
        $requiredFiles = [
            'pages/transfer_print.php' => 'صفحة الطباعة المفصلة',
            'pages/transfer_print_simple.php' => 'صفحة الطباعة المبسطة',
            'pages/transfers_report_print.php' => 'صفحة طباعة التقرير',
            'index.php' => 'الملف الرئيسي'
        ];
        
        $allFilesExist = true;
        foreach ($requiredFiles as $file => $description) {
            if (file_exists($file)) {
                echo "<div class='success'>✓ $description موجود</div>";
            } else {
                echo "<div class='error'>✗ $description غير موجود</div>";
                $allFilesExist = false;
            }
        }
        
        if ($allFilesExist) {
            echo "<div class='success'>جميع الملفات المطلوبة موجودة</div>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>فحص التحويلات المتاحة</h3>";
        
        // جلب تحويل للاختبار
        $stmt = $db->query("
            SELECT id, transfer_number, status 
            FROM container_transfers 
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $transfer = $stmt->fetch();
        
        if ($transfer) {
            echo "<div class='success'>✓ تم العثور على تحويل للاختبار: " . htmlspecialchars($transfer['transfer_number']) . "</div>";
            $testId = $transfer['id'];
        } else {
            echo "<div class='error'>✗ لا توجد تحويلات في النظام</div>";
            echo "<div class='info'>سيتم إنشاء تحويل تجريبي...</div>";
            
            // إنشاء تحويل تجريبي
            try {
                $stmt = $db->prepare("
                    INSERT INTO container_transfers 
                    (transfer_number, transfer_date, trader_id, driver_id, pickup_location, delivery_location, total_containers, total_amount, status, created_by) 
                    VALUES (?, CURDATE(), 1, 1, 'المستودع الرئيسي', 'موقع التسليم', 1, 100000, 'pending', 1)
                ");
                $transferNumber = 'TEST-' . date('YmdHis');
                $stmt->execute([$transferNumber]);
                $testId = $db->lastInsertId();
                
                echo "<div class='success'>✓ تم إنشاء تحويل تجريبي: $transferNumber (ID: $testId)</div>";
            } catch (Exception $e) {
                echo "<div class='error'>خطأ في إنشاء التحويل التجريبي: " . $e->getMessage() . "</div>";
                $testId = 1; // استخدام ID افتراضي
            }
        }
        echo "</div>";
        
        if (isset($testId)) {
            echo "<div class='test-section'>";
            echo "<h3>اختبار روابط الطباعة</h3>";
            
            $printLinks = [
                "index.php?page=transfer_print&id=$testId" => ['طباعة مفصلة', 'btn-success'],
                "index.php?page=transfer_print_simple&id=$testId" => ['طباعة مبسطة', 'btn-warning'],
                "index.php?page=transfer_print_simple&id=$testId&auto_print=1" => ['طباعة مباشرة', 'btn-danger'],
                "index.php?page=transfers_report_print" => ['تقرير التحويلات', 'btn'],
                "index.php?page=transfers_report_print&auto_print=1" => ['تقرير مع طباعة مباشرة', 'btn']
            ];
            
            echo "<div class='info'>اضغط على الروابط التالية لاختبار الطباعة:</div>";
            
            foreach ($printLinks as $link => $info) {
                list($label, $class) = $info;
                echo "<p>";
                echo "<a href='$link' target='_blank' class='btn $class'>$label</a>";
                echo " - <small style='color: #666;'>$link</small>";
                echo "</p>";
            }
            echo "</div>";
            
            echo "<div class='test-section'>";
            echo "<h3>اختبار JavaScript</h3>";
            
            echo "<div class='info'>اختبار وظائف JavaScript للطباعة:</div>";
            
            echo "<button class='btn btn-success' onclick='testPrintFunction($testId)'>اختبار طباعة مفصلة</button>";
            echo "<button class='btn btn-warning' onclick='testSimplePrint($testId)'>اختبار طباعة مبسطة</button>";
            echo "<button class='btn' onclick='testReportPrint()'>اختبار تقرير</button>";
            
            echo "<div id='testResult' style='margin-top: 15px;'></div>";
            echo "</div>";
        }
        
        echo "<div class='test-section'>";
        echo "<h3>فحص إعدادات index.php</h3>";
        
        // قراءة محتوى index.php للتحقق من الإعدادات
        $indexContent = file_get_contents('index.php');
        
        if (strpos($indexContent, 'transfer_print') !== false) {
            echo "<div class='success'>✓ صفحات الطباعة مضافة إلى allowed_pages</div>";
        } else {
            echo "<div class='error'>✗ صفحات الطباعة غير مضافة إلى allowed_pages</div>";
        }
        
        if (strpos($indexContent, 'printPages') !== false) {
            echo "<div class='success'>✓ معالجة خاصة لصفحات الطباعة موجودة</div>";
        } else {
            echo "<div class='error'>✗ معالجة خاصة لصفحات الطباعة غير موجودة</div>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>✅ ملخص الاختبار</h3>";
        echo "<div class='success'>نظام الطباعة جاهز للاستخدام!</div>";
        echo "<div class='info'>";
        echo "<strong>الخطوات التالية:</strong><br>";
        echo "1. اذهب إلى صفحة التحويلات<br>";
        echo "2. اضغط على زر الطباعة بجانب أي تحويل<br>";
        echo "3. اختر نوع الطباعة المطلوب<br>";
        echo "4. تأكد من أن الصفحة تفتح في نافذة جديدة<br>";
        echo "</div>";
        
        echo "<div style='margin-top: 20px;'>";
        echo "<a href='index.php?page=transfers' class='btn btn-success'>اذهب إلى التحويلات</a>";
        echo "<a href='test_transfer_print.php' class='btn'>اختبار شامل</a>";
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
    }
    ?>
    
    <script>
    function testPrintFunction(transferId) {
        const resultDiv = document.getElementById('testResult');
        resultDiv.innerHTML = '<div style="color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px;">جاري اختبار الطباعة المفصلة...</div>';
        
        const printUrl = `index.php?page=transfer_print&id=${transferId}`;
        const printWindow = window.open(printUrl, '_blank', 'width=800,height=600');
        
        if (printWindow) {
            resultDiv.innerHTML = '<div style="color: green; background: #d4edda; padding: 10px; border-radius: 5px;">✓ تم فتح نافذة الطباعة المفصلة بنجاح</div>';
        } else {
            resultDiv.innerHTML = '<div style="color: red; background: #f8d7da; padding: 10px; border-radius: 5px;">✗ فشل في فتح نافذة الطباعة - تأكد من السماح بالنوافذ المنبثقة</div>';
        }
    }
    
    function testSimplePrint(transferId) {
        const resultDiv = document.getElementById('testResult');
        resultDiv.innerHTML = '<div style="color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px;">جاري اختبار الطباعة المبسطة...</div>';
        
        const printUrl = `index.php?page=transfer_print_simple&id=${transferId}`;
        const printWindow = window.open(printUrl, '_blank', 'width=800,height=600');
        
        if (printWindow) {
            resultDiv.innerHTML = '<div style="color: green; background: #d4edda; padding: 10px; border-radius: 5px;">✓ تم فتح نافذة الطباعة المبسطة بنجاح</div>';
        } else {
            resultDiv.innerHTML = '<div style="color: red; background: #f8d7da; padding: 10px; border-radius: 5px;">✗ فشل في فتح نافذة الطباعة - تأكد من السماح بالنوافذ المنبثقة</div>';
        }
    }
    
    function testReportPrint() {
        const resultDiv = document.getElementById('testResult');
        resultDiv.innerHTML = '<div style="color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px;">جاري اختبار تقرير التحويلات...</div>';
        
        const reportUrl = 'index.php?page=transfers_report_print';
        const reportWindow = window.open(reportUrl, '_blank', 'width=1000,height=700');
        
        if (reportWindow) {
            resultDiv.innerHTML = '<div style="color: green; background: #d4edda; padding: 10px; border-radius: 5px;">✓ تم فتح نافذة التقرير بنجاح</div>';
        } else {
            resultDiv.innerHTML = '<div style="color: red; background: #f8d7da; padding: 10px; border-radius: 5px;">✗ فشل في فتح نافذة التقرير - تأكد من السماح بالنوافذ المنبثقة</div>';
        }
    }
    </script>
</body>
</html>
