<?php
// التحقق من الصلاحيات (مؤقت - معطل)
/*
if (!hasPermission('drivers')) {
    $_SESSION['error'] = 'ليس لديك صلاحية الوصول إلى هذه الصفحة';
    echo '<script>window.location.href = "index.php?page=unauthorized";</script>';
    return;
}
*/

$action = isset($_GET['action']) ? $_GET['action'] : 'add';
$driverId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// بيانات السائق للتعديل
$driver = null;
if ($action === 'edit' && $driverId > 0) {
    try {
        $stmt = $db->prepare("SELECT * FROM drivers WHERE id = ?");
        $stmt->execute([$driverId]);
        $driver = $stmt->fetch();
        
        if (!$driver) {
            $_SESSION['error'] = 'السائق غير موجود';
            echo '<script>window.location.href = "index.php?page=drivers";</script>';
            return;
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = 'حدث خطأ أثناء استرجاع بيانات السائق';
        echo '<script>window.location.href = "index.php?page=drivers";</script>';
        return;
    }
}

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $errors = [];
    
    // التحقق من صحة البيانات
    $driver_name = trim($_POST['driver_name'] ?? '');
    $license_number = trim($_POST['license_number'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $national_id = trim($_POST['national_id'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $vehicle_type = trim($_POST['vehicle_type'] ?? '');
    $vehicle_number = trim($_POST['vehicle_number'] ?? '');
    $license_expiry = $_POST['license_expiry'] ?? '';
    $status = $_POST['status'] ?? 'active';
    $daily_rate = (float)($_POST['daily_rate'] ?? 0);
    $per_container_rate = (float)($_POST['per_container_rate'] ?? 0);
    $notes = trim($_POST['notes'] ?? '');
    
    // التحقق من البيانات المطلوبة
    if (empty($driver_name)) {
        $errors[] = 'اسم السائق مطلوب';
    }
    
    if (empty($license_number)) {
        $errors[] = 'رقم رخصة القيادة مطلوب';
    }
    
    if (empty($phone)) {
        $errors[] = 'رقم الهاتف مطلوب';
    }
    
    // التحقق من تنسيق الهاتف
    if (!empty($phone) && !preg_match('/^(07[0-9]{9}|00964[0-9]{10})$/', $phone)) {
        $errors[] = 'تنسيق رقم الهاتف غير صحيح';
    }
    
    // التحقق من تاريخ انتهاء الرخصة
    if (!empty($license_expiry)) {
        $expiry_date = DateTime::createFromFormat('Y-m-d', $license_expiry);
        if (!$expiry_date) {
            $errors[] = 'تاريخ انتهاء الرخصة غير صحيح';
        }
    }
    
    // التحقق من عدم تكرار رقم الرخصة
    if (!empty($license_number)) {
        $check_sql = "SELECT id FROM drivers WHERE license_number = ?";
        $check_params = [$license_number];
        
        if ($action === 'edit' && $driver) {
            $check_sql .= " AND id != ?";
            $check_params[] = $driver['id'];
        }
        
        $check_stmt = $db->prepare($check_sql);
        $check_stmt->execute($check_params);
        
        if ($check_stmt->fetchColumn()) {
            $errors[] = 'رقم رخصة القيادة مستخدم من قبل سائق آخر';
        }
    }
    
    // إذا لم توجد أخطاء، احفظ البيانات
    if (empty($errors)) {
        try {
            if ($action === 'add') {
                // إضافة سائق جديد
                $sql = "INSERT INTO drivers (driver_name, license_number, phone, national_id, address, 
                               vehicle_type, vehicle_number, license_expiry, status, daily_rate, 
                               per_container_rate, notes, created_by) 
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                
                $params = [
                    $driver_name, $license_number, $phone, $national_id, $address,
                    $vehicle_type, $vehicle_number, 
                    !empty($license_expiry) ? $license_expiry : null,
                    $status, $daily_rate, $per_container_rate, $notes, $_SESSION['user_id']
                ];
                
                $stmt = $db->prepare($sql);
                $stmt->execute($params);
                
                $_SESSION['success'] = 'تم إضافة السائق بنجاح';
                
            } else {
                // تحديث بيانات السائق
                $sql = "UPDATE drivers SET driver_name = ?, license_number = ?, phone = ?, 
                               national_id = ?, address = ?, vehicle_type = ?, vehicle_number = ?, 
                               license_expiry = ?, status = ?, daily_rate = ?, per_container_rate = ?, 
                               notes = ? WHERE id = ?";
                
                $params = [
                    $driver_name, $license_number, $phone, $national_id, $address,
                    $vehicle_type, $vehicle_number,
                    !empty($license_expiry) ? $license_expiry : null,
                    $status, $daily_rate, $per_container_rate, $notes, $driver['id']
                ];
                
                $stmt = $db->prepare($sql);
                $stmt->execute($params);
                
                $_SESSION['success'] = 'تم تحديث بيانات السائق بنجاح';
            }
            
            echo '<script>window.location.href = "index.php?page=drivers";</script>';
            return;
            
        } catch (PDOException $e) {
            $errors[] = 'حدث خطأ أثناء حفظ البيانات: ' . $e->getMessage();
        }
    }
    
    // عرض الأخطاء
    if (!empty($errors)) {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

$page_title = $action === 'edit' ? 'تعديل بيانات السائق' : 'إضافة سائق جديد';
?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">
                    <i class="fas fa-<?php echo $action === 'edit' ? 'edit' : 'plus'; ?> text-primary me-2"></i>
                    <?php echo $page_title; ?>
                </h1>
                <p class="text-muted mt-2">
                    <?php echo $action === 'edit' ? 'تحديث بيانات السائق ومعلومات المركبة' : 'إضافة سائق جديد إلى النظام'; ?>
                </p>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php?page=drivers">إدارة السائقين</a></li>
                    <li class="breadcrumb-item active"><?php echo $action === 'edit' ? 'تعديل' : 'إضافة'; ?></li>
                </ol>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div></div>
                    <a href="index.php?page=drivers" class="btn btn-secondary btn-lg shadow-sm">
                        <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="content">
    <div class="container-fluid">

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-tie me-2"></i>بيانات السائق
                    </h5>
                </div>
                
                <form method="POST" class="needs-validation" novalidate>
                    <div class="card-body">
                        <div class="row">
                            <!-- البيانات الشخصية -->
                            <div class="col-12 mb-4">
                                <h6 class="border-bottom pb-2 mb-3">
                                    <i class="fas fa-user me-2 text-primary"></i>البيانات الشخصية
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="driver_name" class="form-label required">اسم السائق</label>
                                <input type="text" class="form-control" id="driver_name" name="driver_name" 
                                       value="<?php echo htmlspecialchars($driver['driver_name'] ?? ''); ?>" 
                                       required maxlength="100">
                                <div class="invalid-feedback">اسم السائق مطلوب</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="national_id" class="form-label">رقم الهوية الوطنية</label>
                                <input type="text" class="form-control" id="national_id" name="national_id" 
                                       value="<?php echo htmlspecialchars($driver['national_id'] ?? ''); ?>" 
                                       maxlength="20" placeholder="اختياري">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label required">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($driver['phone'] ?? ''); ?>" 
                                       required maxlength="20" placeholder="07901234567">
                                <div class="form-text">مثال: 07901234567</div>
                                <div class="invalid-feedback">رقم الهاتف مطلوب</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">الحالة</label>
                                <div class="searchable-select-wrapper">
                                    <select class="form-select searchable-select" id="status" name="status" data-placeholder="اختر الحالة...">
                                        <option value="active" <?php echo ($driver['status'] ?? 'active') === 'active' ? 'selected' : ''; ?>>نشط</option>
                                        <option value="inactive" <?php echo ($driver['status'] ?? '') === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                                        <option value="suspended" <?php echo ($driver['status'] ?? '') === 'suspended' ? 'selected' : ''; ?>>موقوف</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="2" 
                                          placeholder="العنوان التفصيلي"><?php echo htmlspecialchars($driver['address'] ?? ''); ?></textarea>
                            </div>

                            <!-- بيانات الرخصة -->
                            <div class="col-12 mb-4 mt-3">
                                <h6 class="border-bottom pb-2 mb-3">
                                    <i class="fas fa-id-card me-2 text-info"></i>بيانات رخصة القيادة
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="license_number" class="form-label required">رقم رخصة القيادة</label>
                                <input type="text" class="form-control" id="license_number" name="license_number" 
                                       value="<?php echo htmlspecialchars($driver['license_number'] ?? ''); ?>" 
                                       required maxlength="50">
                                <div class="invalid-feedback">رقم رخصة القيادة مطلوب</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="license_expiry" class="form-label">تاريخ انتهاء الرخصة</label>
                                <input type="date" class="form-control" id="license_expiry" name="license_expiry" 
                                       value="<?php echo $driver['license_expiry'] ?? ''; ?>">
                                <div class="form-text">اتركه فارغاً إذا كان غير محدد</div>
                            </div>

                            <!-- بيانات المركبة -->
                            <div class="col-12 mb-4 mt-3">
                                <h6 class="border-bottom pb-2 mb-3">
                                    <i class="fas fa-truck me-2 text-success"></i>بيانات المركبة
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="vehicle_type" class="form-label">نوع المركبة</label>
                                <div class="searchable-select-wrapper">
                                    <select class="form-select searchable-select" id="vehicle_type" name="vehicle_type" data-placeholder="اختر نوع المركبة...">
                                        <option value="">اختر نوع المركبة</option>
                                        <option value="شاحنة كبيرة" <?php echo ($driver['vehicle_type'] ?? '') === 'شاحنة كبيرة' ? 'selected' : ''; ?>>شاحنة كبيرة</option>
                                        <option value="شاحنة متوسطة" <?php echo ($driver['vehicle_type'] ?? '') === 'شاحنة متوسطة' ? 'selected' : ''; ?>>شاحنة متوسطة</option>
                                        <option value="شاحنة صغيرة" <?php echo ($driver['vehicle_type'] ?? '') === 'شاحنة صغيرة' ? 'selected' : ''; ?>>شاحنة صغيرة</option>
                                        <option value="مقطورة" <?php echo ($driver['vehicle_type'] ?? '') === 'مقطورة' ? 'selected' : ''; ?>>مقطورة</option>
                                        <option value="تريلة" <?php echo ($driver['vehicle_type'] ?? '') === 'تريلة' ? 'selected' : ''; ?>>تريلة</option>
                                        <option value="أخرى" <?php echo ($driver['vehicle_type'] ?? '') === 'أخرى' ? 'selected' : ''; ?>>أخرى</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="vehicle_number" class="form-label">رقم المركبة</label>
                                <input type="text" class="form-control" id="vehicle_number" name="vehicle_number" 
                                       value="<?php echo htmlspecialchars($driver['vehicle_number'] ?? ''); ?>" 
                                       maxlength="20" placeholder="123-بغداد">
                            </div>

                            <!-- الأسعار والأجور -->
                            <div class="col-12 mb-4 mt-3">
                                <h6 class="border-bottom pb-2 mb-3">
                                    <i class="fas fa-money-bill-wave me-2 text-warning"></i>الأسعار والأجور
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="daily_rate" class="form-label">الأجر اليومي (بالدينار العراقي)</label>
                                <div class="input-group">
                                    <input type="number" class="form-control amount-input" id="daily_rate" name="daily_rate" 
                                           value="<?php echo $driver['daily_rate'] ?? '0'; ?>" 
                                           min="0" step="0.01">
                                    <span class="input-group-text">د.ع</span>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="per_container_rate" class="form-label">أجر الحاوية الواحدة (بالدينار العراقي)</label>
                                <div class="input-group">
                                    <input type="number" class="form-control amount-input" id="per_container_rate" name="per_container_rate" 
                                           value="<?php echo $driver['per_container_rate'] ?? '0'; ?>" 
                                           min="0" step="0.01">
                                    <span class="input-group-text">د.ع</span>
                                </div>
                            </div>

                            <!-- الملاحظات -->
                            <div class="col-12 mb-4 mt-3">
                                <h6 class="border-bottom pb-2 mb-3">
                                    <i class="fas fa-sticky-note me-2 text-secondary"></i>ملاحظات إضافية
                                </h6>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="notes" class="form-label">الملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" 
                                          placeholder="أي ملاحظات إضافية حول السائق أو المركبة"><?php echo htmlspecialchars($driver['notes'] ?? ''); ?></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="index.php?page=drivers" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    <?php echo $action === 'edit' ? 'تحديث البيانات' : 'إضافة السائق'; ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    </div>
    <!-- /.container-fluid -->
</div>
<!-- /.content -->

<script>
document.addEventListener('DOMContentLoaded', function() {
    
    // التحقق من صحة النموذج
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        form.classList.add('was-validated');
    });
    
    // تنسيق حقول المبالغ
    const amountInputs = document.querySelectorAll('.amount-input');
    amountInputs.forEach(input => {
        input.addEventListener('input', function() {
            let value = this.value.replace(/[^0-9.]/g, '');
            
            // التأكد من وجود نقطة عشرية واحدة فقط
            const parts = value.split('.');
            if (parts.length > 2) {
                value = parts[0] + '.' + parts.slice(1).join('');
            }
            
            this.value = value;
        });
        
        input.addEventListener('blur', function() {
            if (this.value) {
                this.value = parseFloat(this.value).toFixed(2);
            }
        });
    });
    
    // التحقق من تاريخ انتهاء الرخصة
    const licenseExpiryInput = document.getElementById('license_expiry');
    licenseExpiryInput.addEventListener('change', function() {
        if (this.value) {
            const expiryDate = new Date(this.value);
            const today = new Date();
            const diffTime = expiryDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            const alertDiv = document.getElementById('expiry-alert');
            if (alertDiv) alertDiv.remove();
            
            if (diffDays < 0) {
                // الرخصة منتهية
                const alert = document.createElement('div');
                alert.id = 'expiry-alert';
                alert.className = 'alert alert-danger mt-2';
                alert.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>الرخصة منتهية الصلاحية!';
                this.parentNode.appendChild(alert);
            } else if (diffDays <= 30) {
                // الرخصة تنتهي قريباً
                const alert = document.createElement('div');
                alert.id = 'expiry-alert';
                alert.className = 'alert alert-warning mt-2';
                alert.innerHTML = `<i class="fas fa-clock me-2"></i>الرخصة تنتهي خلال ${diffDays} يوم`;
                this.parentNode.appendChild(alert);
            }
        }
    });
    
    // تنسيق رقم الهاتف
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        // إزالة كل شيء ما عدا الأرقام
        let value = this.value.replace(/[^0-9]/g, '');
        
        // تحديد الحد الأقصى حسب التنسيق
        if (value.startsWith('00964')) {
            value = value.substring(0, 15); // 00964 + 10 digits
        } else if (value.startsWith('07')) {
            value = value.substring(0, 11); // 07 + 9 digits
        }
        
        this.value = value;
    });
    
    // التحقق من رقم الهاتف عند فقدان التركيز
    phoneInput.addEventListener('blur', function() {
        const phonePattern = /^(07[0-9]{9}|00964[0-9]{10})$/;
        const isValid = phonePattern.test(this.value);
        
        const existingFeedback = this.parentNode.querySelector('.phone-feedback');
        if (existingFeedback) existingFeedback.remove();
        
        if (this.value && !isValid) {
            const feedback = document.createElement('div');
            feedback.className = 'phone-feedback text-danger small mt-1';
            feedback.innerHTML = '<i class="fas fa-exclamation-circle me-1"></i>تنسيق رقم الهاتف غير صحيح';
            this.parentNode.appendChild(feedback);
            
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });
});
</script>

<style>
.required::after {
    content: ' *';
    color: #dc3545;
}

.amount-input {
    text-align: left;
    direction: ltr;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.border-bottom {
    border-bottom: 2px solid #dee2e6 !important;
}

.form-text {
    color: #6c757d;
    font-size: 0.875em;
}

.input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
}

.was-validated .form-control:valid {
    border-color: #198754;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.57-.58L1.76 5.1l.63-.57L1.15 3.29l.58-.57-1.09-1.1L0 2.26l1.09 1.09L3.93.51l.57.57-2.8 2.8-.86.85z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4M8.2 4.6l-2.4 2.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .form-label {
        font-size: 0.9rem;
        font-weight: 500;
    }
}
</style>