/**
 * JavaScript لتحسين تجربة طباعة التحويلات
 */

// وظائف الطباعة
const TransferPrint = {
    
    // طباعة مباشرة
    printDirect: function(transferId) {
        const printUrl = `index.php?page=transfer_print&id=${transferId}`;
        const printWindow = window.open(printUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
        
        if (printWindow) {
            printWindow.onload = function() {
                setTimeout(() => {
                    printWindow.print();
                }, 500);
            };
        } else {
            alert('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
        }
    },
    
    // طباعة مع معاينة
    printWithPreview: function(transferId) {
        const printUrl = `index.php?page=transfer_print&id=${transferId}`;
        window.open(printUrl, '_blank');
    },
    
    // طباعة متعددة
    printMultiple: function(transferIds) {
        if (!Array.isArray(transferIds) || transferIds.length === 0) {
            alert('يرجى اختيار تحويل واحد على الأقل للطباعة');
            return;
        }
        
        const confirmMsg = `هل تريد طباعة ${transferIds.length} تحويل؟`;
        if (!confirm(confirmMsg)) {
            return;
        }
        
        transferIds.forEach((id, index) => {
            setTimeout(() => {
                this.printWithPreview(id);
            }, index * 1000); // تأخير ثانية واحدة بين كل طباعة
        });
    },
    
    // تصدير إلى PDF (يتطلب مكتبة إضافية)
    exportToPDF: function(transferId) {
        // هذه الوظيفة تحتاج إلى مكتبة jsPDF أو مماثلة
        alert('ميزة التصدير إلى PDF ستكون متاحة قريباً');
    },
    
    // إعدادات الطباعة
    setupPrintSettings: function() {
        // إعدادات CSS للطباعة
        const printStyles = `
            @media print {
                @page {
                    size: A4;
                    margin: 15mm;
                }
                
                body {
                    font-size: 12pt;
                    line-height: 1.4;
                }
                
                .no-print {
                    display: none !important;
                }
                
                .print-break {
                    page-break-before: always;
                }
                
                .print-avoid-break {
                    page-break-inside: avoid;
                }
            }
        `;
        
        // إضافة الأنماط إلى الصفحة
        const styleSheet = document.createElement('style');
        styleSheet.textContent = printStyles;
        document.head.appendChild(styleSheet);
    }
};

// إضافة أحداث للأزرار
document.addEventListener('DOMContentLoaded', function() {
    
    // إعداد أنماط الطباعة
    TransferPrint.setupPrintSettings();
    
    // أزرار الطباعة المفردة
    document.querySelectorAll('.btn-print-transfer').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const transferId = this.dataset.transferId;
            if (transferId) {
                TransferPrint.printWithPreview(transferId);
            }
        });
    });
    
    // أزرار الطباعة المباشرة
    document.querySelectorAll('.btn-print-direct').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const transferId = this.dataset.transferId;
            if (transferId) {
                TransferPrint.printDirect(transferId);
            }
        });
    });
    
    // طباعة متعددة
    const printMultipleBtn = document.getElementById('printMultipleBtn');
    if (printMultipleBtn) {
        printMultipleBtn.addEventListener('click', function() {
            const selectedTransfers = [];
            document.querySelectorAll('.transfer-checkbox:checked').forEach(checkbox => {
                selectedTransfers.push(checkbox.value);
            });
            
            TransferPrint.printMultiple(selectedTransfers);
        });
    }
    
    // اختيار الكل للطباعة
    const selectAllCheckbox = document.getElementById('selectAllTransfers');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.transfer-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            
            updatePrintButtonState();
        });
    }
    
    // تحديث حالة أزرار الطباعة
    document.querySelectorAll('.transfer-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updatePrintButtonState);
    });
    
    function updatePrintButtonState() {
        const selectedCount = document.querySelectorAll('.transfer-checkbox:checked').length;
        const printMultipleBtn = document.getElementById('printMultipleBtn');
        
        if (printMultipleBtn) {
            if (selectedCount > 0) {
                printMultipleBtn.disabled = false;
                printMultipleBtn.textContent = `طباعة المحدد (${selectedCount})`;
            } else {
                printMultipleBtn.disabled = true;
                printMultipleBtn.textContent = 'طباعة المحدد';
            }
        }
    }
});

// وظائف مساعدة للطباعة
window.TransferPrintUtils = {
    
    // فتح نافذة طباعة مخصصة
    openCustomPrintWindow: function(content, title = 'طباعة') {
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        
        const printContent = `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>${title}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    @media print {
                        body { margin: 0; }
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                <button onclick="window.print()" class="no-print" style="margin-bottom: 20px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px;">طباعة</button>
                ${content}
            </body>
            </html>
        `;
        
        printWindow.document.write(printContent);
        printWindow.document.close();
        
        return printWindow;
    },
    
    // تحويل جدول إلى تنسيق قابل للطباعة
    formatTableForPrint: function(tableElement) {
        const table = tableElement.cloneNode(true);
        
        // إزالة الأعمدة غير المرغوب فيها في الطباعة
        const actionsColumns = table.querySelectorAll('.actions-column, .no-print');
        actionsColumns.forEach(col => col.remove());
        
        // تحسين تنسيق الجدول للطباعة
        table.style.width = '100%';
        table.style.borderCollapse = 'collapse';
        
        const cells = table.querySelectorAll('th, td');
        cells.forEach(cell => {
            cell.style.border = '1px solid #ddd';
            cell.style.padding = '8px';
            cell.style.fontSize = '12px';
        });
        
        return table.outerHTML;
    },
    
    // إضافة رأس وتذييل للطباعة
    addPrintHeaderFooter: function(content, title, subtitle = '') {
        const currentDate = new Date().toLocaleDateString('ar-SA');
        const currentTime = new Date().toLocaleTimeString('ar-SA');
        
        return `
            <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #007bff; padding-bottom: 20px;">
                <h1 style="color: #007bff; margin-bottom: 10px;">${title}</h1>
                ${subtitle ? `<h2 style="color: #666; margin-bottom: 10px;">${subtitle}</h2>` : ''}
                <p style="color: #666; font-size: 14px;">تاريخ الطباعة: ${currentDate} - ${currentTime}</p>
            </div>
            
            ${content}
            
            <div style="margin-top: 40px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #ddd; padding-top: 20px;">
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة التخليص الكمركي</p>
                <p>الصفحة <span id="pageNumber"></span></p>
            </div>
        `;
    }
};

// تصدير الوظائف للاستخدام العام
window.TransferPrint = TransferPrint;
