<?php
// التحقق من الصلاحيات
if (!hasPermission('financial') && !hasPermission('customers')) {
    $_SESSION['error'] = 'ليس لديك صلاحية الوصول إلى هذه الصفحة';
    echo '<script>window.location.href = "index.php?page=unauthorized";</script>';
    return;
}

try {
    // الحصول على جميع التجار النشطين
    $stmt = $db->query("
        SELECT t.id, t.name, t.contact_person, t.phone, t.price,
               (SELECT COUNT(*) FROM containers WHERE trader_id = t.id) as total_containers,
               (SELECT COUNT(*) FROM financial_documents WHERE customer_id = t.id) as total_documents,
               (SELECT COUNT(*) FROM container_transfers WHERE trader_id = t.id) as total_transfers
        FROM traders t 
        WHERE t.active = 1 
        ORDER BY t.name ASC
    ");
    $traders = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $_SESSION['error'] = 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage();
    $traders = [];
}
?>

<!-- Content Header -->
<div class="content-header bg-gradient-primary">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-8">
                <div class="d-flex align-items-center">
                    <div class="page-icon bg-white bg-opacity-25 rounded-circle p-3 me-3">
                        <i class="fas fa-file-invoice-dollar text-white fa-2x"></i>
                    </div>
                    <div>
                        <h1 class="m-0 text-white">كشف حساب التاجر</h1>
                        <p class="text-white-50 mb-0">اختر التاجر لعرض كشف حسابه الشامل</p>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <ol class="breadcrumb float-sm-right bg-transparent">
                    <li class="breadcrumb-item"><a href="index.php" class="text-white-50">الرئيسية</a></li>
                    <li class="breadcrumb-item"><span class="text-white-50">التقارير</span></li>
                    <li class="breadcrumb-item active text-white">كشف حساب تاجر</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="content">
    <div class="container-fluid">

        <!-- Instructions Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body bg-light">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="text-primary mb-2">
                                    <i class="fas fa-info-circle me-2"></i>
                                    كيفية عرض كشف الحساب
                                </h5>
                                <p class="mb-0 text-muted">
                                    اختر التاجر من القائمة أدناه لعرض كشف حسابه الشامل الذي يتضمن جميع الحاويات والمستندات المالية والتحويلات
                                </p>
                            </div>
                            <div class="col-md-4 text-end">
                                <a href="index.php?page=traders_list" class="btn btn-outline-primary">
                                    <i class="fas fa-list me-2"></i>
                                    قائمة التجار
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Selection Form -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            اختيار سريع للتاجر
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <input type="hidden" name="page" value="trader_statement">
                            
                            <div class="col-md-6">
                                <label class="form-label fw-semibold">
                                    <i class="fas fa-user-tie text-primary me-1"></i>
                                    اختر التاجر
                                </label>
                                <select name="trader_id" class="form-select form-select-lg border-2" required>
                                    <option value="">-- اختر التاجر --</option>
                                    <?php foreach ($traders as $trader): ?>
                                    <option value="<?php echo $trader['id']; ?>">
                                        <?php echo htmlspecialchars($trader['name']); ?>
                                        <?php if ($trader['contact_person']): ?>
                                        - <?php echo htmlspecialchars($trader['contact_person']); ?>
                                        <?php endif; ?>
                                        <?php if ($trader['price'] > 0): ?>
                                        (مستحق: <?php echo number_format($trader['price']); ?> د.ع)
                                        <?php endif; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label fw-semibold">
                                    <i class="fas fa-calendar-alt text-secondary me-1"></i>
                                    من تاريخ
                                </label>
                                <input type="date" name="date_from" class="form-control border-2" value="<?php echo date('Y-m-01'); ?>">
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label fw-semibold">
                                    <i class="fas fa-calendar-check text-secondary me-1"></i>
                                    إلى تاريخ
                                </label>
                                <input type="date" name="date_to" class="form-control border-2" value="<?php echo date('Y-m-d'); ?>">
                            </div>
                            
                            <div class="col-12">
                                <div class="d-flex gap-2 justify-content-center">
                                    <button type="submit" class="btn btn-primary btn-lg shadow-sm">
                                        <i class="fas fa-file-invoice-dollar me-2"></i>
                                        عرض كشف الحساب
                                    </button>
                                    <button type="button" class="btn btn-success btn-lg shadow-sm" onclick="openPrintVersion()">
                                        <i class="fas fa-print me-2"></i>
                                        عرض للطباعة
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Traders Grid -->
        <?php if (!empty($traders)): ?>
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="mb-0 text-primary">
                    <i class="fas fa-users me-2"></i>
                    التجار المتاحين
                    <span class="badge bg-primary ms-2"><?php echo count($traders); ?></span>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($traders as $trader): ?>
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card border-0 shadow-sm h-100 trader-card">
                            <div class="card-body">
                                <div class="d-flex align-items-start">
                                    <div class="avatar-lg bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                        <i class="fas fa-user-tie text-primary fa-lg"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1 fw-bold"><?php echo htmlspecialchars($trader['name']); ?></h6>
                                        
                                        <?php if ($trader['contact_person']): ?>
                                        <p class="text-muted mb-1 small">
                                            <i class="fas fa-user me-1"></i>
                                            <?php echo htmlspecialchars($trader['contact_person']); ?>
                                        </p>
                                        <?php endif; ?>
                                        
                                        <?php if ($trader['phone']): ?>
                                        <p class="text-muted mb-2 small">
                                            <i class="fas fa-phone me-1"></i>
                                            <span dir="ltr"><?php echo htmlspecialchars($trader['phone']); ?></span>
                                        </p>
                                        <?php endif; ?>
                                        
                                        <!-- Statistics -->
                                        <div class="row text-center mb-3">
                                            <div class="col-4">
                                                <div class="fw-bold text-primary"><?php echo $trader['total_containers']; ?></div>
                                                <small class="text-muted">حاويات</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="fw-bold text-info"><?php echo $trader['total_documents']; ?></div>
                                                <small class="text-muted">مستندات</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="fw-bold text-success"><?php echo $trader['total_transfers']; ?></div>
                                                <small class="text-muted">تحويلات</small>
                                            </div>
                                        </div>
                                        
                                        <!-- Outstanding Amount -->
                                        <?php if ($trader['price'] > 0): ?>
                                        <div class="alert alert-warning py-2 mb-3">
                                            <small>
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                مستحق: <strong><?php echo number_format($trader['price']); ?> د.ع</strong>
                                            </small>
                                        </div>
                                        <?php endif; ?>
                                        
                                        <!-- Action Buttons -->
                                        <div class="d-flex gap-1">
                                            <a href="index.php?page=trader_statement&trader_id=<?php echo $trader['id']; ?>" 
                                               class="btn btn-primary btn-sm flex-fill">
                                                <i class="fas fa-file-invoice-dollar me-1"></i>
                                                كشف الحساب
                                            </a>
                                            <a href="index.php?page=trader_statement&trader_id=<?php echo $trader['id']; ?>&print=1" 
                                               target="_blank" class="btn btn-success btn-sm">
                                                <i class="fas fa-print"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center py-5">
                <i class="fas fa-users text-muted" style="font-size: 4rem;"></i>
                <h4 class="mt-3 text-muted">لا يوجد تجار نشطين</h4>
                <p class="text-muted">لا يمكن عرض كشوف الحساب بدون وجود تجار في النظام</p>
                <a href="index.php?page=customers&action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة تاجر جديد
                </a>
            </div>
        </div>
        <?php endif; ?>

    </div>
</div>

<style>
/* Enhanced Styles */
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    position: relative;
    overflow: hidden;
}

.page-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.trader-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.trader-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.avatar-lg {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.form-control, .form-select {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-1px);
}

.border-2 {
    border-width: 2px !important;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
    .content-header {
        padding: 1rem 0;
    }
    
    .page-icon {
        width: 50px;
        height: 50px;
    }
    
    .avatar-lg {
        width: 50px;
        height: 50px;
    }
}
</style>

<script>
function openPrintVersion() {
    const traderId = document.querySelector('select[name="trader_id"]').value;
    const dateFrom = document.querySelector('input[name="date_from"]').value;
    const dateTo = document.querySelector('input[name="date_to"]').value;
    
    if (!traderId) {
        alert('يرجى اختيار التاجر أولاً');
        return;
    }
    
    let url = `index.php?page=trader_statement&trader_id=${traderId}&print=1`;
    if (dateFrom) url += `&date_from=${dateFrom}`;
    if (dateTo) url += `&date_to=${dateTo}`;
    
    window.open(url, '_blank');
}
</script>
