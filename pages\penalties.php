<?php
// التحقق من الصلاحيات
if (!hasPermission('penalties') && !hasPermission('financial')) {
    header('Location: index.php?page=unauthorized');
    exit;
}

// تحديد الإجراء
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$penaltyId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// معالجة الإجراءات
switch ($action) {
    case 'add':
    case 'edit':
        // معالجة النموذج قبل عرض الصفحة
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // التحقق من الصلاحيات
            if (!hasPermission('financial')) {
                header('Location: index.php?page=unauthorized');
                exit;
            }

            // تحديد ما إذا كان الإجراء إضافة أو تعديل
            $isEdit = ($action == 'edit' && $penaltyId > 0);

            // متغيرات النموذج
            $penalty = [
                'container_id' => $_POST['container_id'] ?? '',
                'customer_id' => $_POST['customer_id'] ?? '',
                'penalty_type' => $_POST['penalty_type'] ?? '',
                'amount' => $_POST['amount'] ?? '',
                'penalty_date' => $_POST['penalty_date'] ?? '',
                'due_date' => $_POST['due_date'] ?? '',
                'description' => $_POST['description'] ?? '',
                'is_paid' => isset($_POST['is_paid']) ? 1 : 0,
                'payment_date' => $_POST['payment_date'] ?? '',
                'payment_reference' => $_POST['payment_reference'] ?? '',
                'notes' => $_POST['notes'] ?? ''
            ];

            $errors = [];

            // التحقق من صحة البيانات
            if (empty($penalty['penalty_type'])) {
                $errors['penalty_type'] = 'يرجى اختيار نوع الغرامة';
            }

            if (empty($penalty['amount']) || !is_numeric($penalty['amount']) || $penalty['amount'] <= 0) {
                $errors['amount'] = 'يرجى إدخال مبلغ صحيح أكبر من 0';
            }

            if (empty($penalty['penalty_date'])) {
                $errors['penalty_date'] = 'يرجى إدخال تاريخ الغرامة';
            }

            if (empty($penalty['due_date'])) {
                $errors['due_date'] = 'يرجى إدخال تاريخ الاستحقاق';
            }

            if (empty($penalty['description'])) {
                $errors['description'] = 'يرجى إدخال وصف الغرامة';
            }

            if ($penalty['is_paid']) {
                if (empty($penalty['payment_date'])) {
                    $errors['payment_date'] = 'يرجى إدخال تاريخ الدفع';
                }

                if (empty($penalty['payment_reference'])) {
                    $errors['payment_reference'] = 'يرجى إدخال مرجع الدفع';
                }
            }

            // إذا لم تكن هناك أخطاء، حفظ البيانات
            if (empty($errors)) {
                try {
                    if ($isEdit) {
                        // تحديث الغرامة الموجودة
                        $sql = "
                            UPDATE penalties
                            SET container_id = :container_id,
                                customer_id = :customer_id,
                                penalty_type = :penalty_type,
                                amount = :amount,
                                penalty_date = :penalty_date,
                                due_date = :due_date,
                                description = :description,
                                is_paid = :is_paid,
                                payment_date = :payment_date,
                                payment_reference = :payment_reference,
                                notes = :notes,
                                updated_at = NOW()
                            WHERE id = :id
                        ";

                        $stmt = $db->prepare($sql);

                        $params = [
                            'container_id' => $penalty['container_id'] ?: null,
                            'customer_id' => $penalty['customer_id'] ?: null,
                            'penalty_type' => $penalty['penalty_type'],
                            'amount' => $penalty['amount'],
                            'penalty_date' => $penalty['penalty_date'],
                            'due_date' => $penalty['due_date'],
                            'description' => $penalty['description'],
                            'is_paid' => $penalty['is_paid'],
                            'payment_date' => $penalty['is_paid'] ? $penalty['payment_date'] : null,
                            'payment_reference' => $penalty['is_paid'] ? $penalty['payment_reference'] : null,
                            'notes' => $penalty['notes'],
                            'id' => $penaltyId
                        ];

                        $stmt->execute($params);

                        // تسجيل النشاط
                        $activityStmt = $db->prepare("
                            INSERT INTO activity_log (user_id, activity_type, description, ip_address)
                            VALUES (:user_id, 'update_penalty', :description, :ip_address)
                        ");
                        $activityStmt->execute([
                            'user_id' => getCurrentUserId(),
                            'description' => 'تم تحديث بيانات الغرامة رقم: ' . $penaltyId,
                            'ip_address' => $_SERVER['REMOTE_ADDR']
                        ]);

                        $_SESSION['success'] = 'تم تحديث بيانات الغرامة بنجاح';
                    } else {
                        // إضافة غرامة جديدة
                        $sql = "
                            INSERT INTO penalties (
                                container_id, customer_id, penalty_type, amount, penalty_date, due_date,
                                description, is_paid, payment_date, payment_reference, notes, created_at, updated_at
                            ) VALUES (
                                :container_id, :customer_id, :penalty_type, :amount, :penalty_date, :due_date,
                                :description, :is_paid, :payment_date, :payment_reference, :notes, NOW(), NOW()
                            )
                        ";

                        $stmt = $db->prepare($sql);

                        $params = [
                            'container_id' => $penalty['container_id'] ?: null,
                            'customer_id' => $penalty['customer_id'] ?: null,
                            'penalty_type' => $penalty['penalty_type'],
                            'amount' => $penalty['amount'],
                            'penalty_date' => $penalty['penalty_date'],
                            'due_date' => $penalty['due_date'],
                            'description' => $penalty['description'],
                            'is_paid' => $penalty['is_paid'],
                            'payment_date' => $penalty['is_paid'] ? $penalty['payment_date'] : null,
                            'payment_reference' => $penalty['is_paid'] ? $penalty['payment_reference'] : null,
                            'notes' => $penalty['notes']
                        ];

                        $stmt->execute($params);

                        // الحصول على معرف الغرامة المضافة
                        $newPenaltyId = $db->lastInsertId();

                        // تسجيل النشاط
                        $activityStmt = $db->prepare("
                            INSERT INTO activity_log (user_id, activity_type, description, ip_address)
                            VALUES (:user_id, 'add_penalty', :description, :ip_address)
                        ");
                        $activityStmt->execute([
                            'user_id' => getCurrentUserId(),
                            'description' => 'تمت إضافة غرامة جديدة برقم: ' . $newPenaltyId,
                            'ip_address' => $_SERVER['REMOTE_ADDR']
                        ]);

                        $_SESSION['success'] = 'تمت إضافة الغرامة بنجاح';
                    }

                    // إعادة التوجيه إلى قائمة الغرامات
                    header('Location: index.php?page=penalties');
                    exit;
                } catch (PDOException $e) {
                    $errors['general'] = 'حدث خطأ أثناء حفظ البيانات: ' . $e->getMessage();
                }
            }
        }

        include 'pages/penalties_form.php';
        break;

    case 'view':
        include 'pages/penalties_view.php';
        break;

    case 'delete':
        // التحقق من وجود الغرامة
        if ($penaltyId > 0) {
            try {
                // التحقق من عدم وجود مستندات مرتبطة بالغرامة
                $checkStmt = $db->prepare("
                    SELECT COUNT(*) as count
                    FROM financial_documents
                    WHERE penalty_id = :id
                ");
                $checkStmt->execute(['id' => $penaltyId]);
                $hasDocuments = $checkStmt->fetch()['count'] > 0;

                if ($hasDocuments) {
                    $_SESSION['error'] = 'لا يمكن حذف الغرامة لوجود مستندات مالية مرتبطة بها';
                } else {
                    // حذف الغرامة
                    $stmt = $db->prepare("DELETE FROM penalties WHERE id = :id");
                    $stmt->execute(['id' => $penaltyId]);

                    // تسجيل النشاط
                    $activityStmt = $db->prepare("
                        INSERT INTO activity_log (user_id, activity_type, description, ip_address)
                        VALUES (:user_id, 'delete_penalty', :description, :ip_address)
                    ");
                    $activityStmt->execute([
                        'user_id' => getCurrentUserId(),
                        'description' => 'تم حذف الغرامة رقم: ' . $penaltyId,
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    ]);

                    $_SESSION['success'] = 'تم حذف الغرامة بنجاح';
                }
            } catch (PDOException $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء حذف الغرامة: ' . $e->getMessage();
            }
        }

        // إعادة التوجيه إلى قائمة الغرامات
        header('Location: index.php?page=penalties');
        exit;
        break;

    case 'list':
    default:
        // استعلام البحث
        $searchTerm = isset($_GET['search']) ? $_GET['search'] : '';
        $containerFilter = isset($_GET['container_id']) ? (int)$_GET['container_id'] : 0;
        $customerFilter = isset($_GET['customer_id']) ? (int)$_GET['customer_id'] : 0;
        $statusFilter = isset($_GET['status']) ? (int)$_GET['status'] : -1;
        $typeFilter = isset($_GET['type']) ? $_GET['type'] : '';

        // بناء استعلام البحث
        $query = "
            SELECT p.*, c.container_number, cu.name as customer_name
            FROM penalties p
            LEFT JOIN containers c ON p.container_id = c.id
            LEFT JOIN customers cu ON p.customer_id = cu.id
            WHERE 1=1
        ";
        $params = [];

        if (!empty($searchTerm)) {
            $query .= " AND (p.description LIKE :search OR c.container_number LIKE :search OR cu.name LIKE :search)";
            $params['search'] = "%$searchTerm%";
        }

        if ($containerFilter > 0) {
            $query .= " AND p.container_id = :container_id";
            $params['container_id'] = $containerFilter;
        }

        if ($customerFilter > 0) {
            $query .= " AND p.customer_id = :customer_id";
            $params['customer_id'] = $customerFilter;
        }

        if ($statusFilter !== -1) {
            $query .= " AND p.is_paid = :status";
            $params['status'] = $statusFilter;
        }

        if (!empty($typeFilter)) {
            $query .= " AND p.penalty_type = :type";
            $params['type'] = $typeFilter;
        }

        $query .= " ORDER BY p.created_at DESC";

        try {
            // تنفيذ الاستعلام
            $stmt = $db->prepare($query);
            $stmt->execute($params);
            $penalties = $stmt->fetchAll();

            // الحصول على قائمة الحاويات للفلتر
            $containersStmt = $db->query("SELECT id, container_number FROM containers ORDER BY container_number");
            $containers = $containersStmt->fetchAll();

            // الحصول على قائمة الشركات للفلتر
            $customersStmt = $db->query("SELECT id, name FROM customers ORDER BY name");
            $customers = $customersStmt->fetchAll();

            // أنواع الغرامات
            $penaltyTypes = [
                'delay' => 'غرامة تأخير',
                'damage' => 'غرامة أضرار',
                'customs' => 'غرامة جمركية',
                'other' => 'غرامات أخرى'
            ];
        } catch (PDOException $e) {
            $_SESSION['error'] = 'حدث خطأ أثناء استرجاع بيانات الغرامات: ' . $e->getMessage();
            $penalties = [];
            $containers = [];
            $customers = [];
        }

        // عرض قائمة الغرامات
        ?>
        <!-- Content Header (Page header) -->
        <div class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1 class="m-0">إدارة الغرامات</h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                            <li class="breadcrumb-item active">إدارة الغرامات</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <!-- Action Buttons -->
                <div class="row mb-3">
                    <div class="col-12">
                        <a href="index.php?page=penalties&action=add" class="btn btn-primary">
                            <i class="fas fa-plus-circle"></i> إضافة غرامة جديدة
                        </a>
                        <a href="index.php?page=reports&type=penalties" class="btn btn-info">
                            <i class="fas fa-chart-line"></i> تقارير الغرامات
                        </a>
                        <button type="button" class="btn btn-secondary" onclick="window.print()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                    </div>
                </div>

                <!-- Alerts -->
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        <h5><i class="icon fas fa-check"></i> نجح!</h5>
                        <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        <h5><i class="icon fas fa-ban"></i> خطأ!</h5>
                        <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                    </div>
                <?php endif; ?>

                <!-- Search and Filter Card -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-search"></i> بحث وتصفية
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <form method="get" action="index.php">
                            <input type="hidden" name="page" value="penalties">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="search">
                                            <i class="fas fa-search"></i> بحث
                                        </label>
                                        <input type="text" class="form-control" id="search" name="search"
                                               value="<?php echo htmlspecialchars($searchTerm); ?>"
                                               placeholder="رقم الحاوية، اسم الشركة، الوصف">
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="container_id">
                                            <i class="fas fa-box"></i> الحاوية
                                        </label>
                                        <div class="searchable-select-wrapper">
                                            <select class="form-control searchable-select" id="container_id" name="container_id" data-placeholder="اختر الحاوية أو ابحث...">
                                                <option value="">جميع الحاويات</option>
                                                <?php foreach ($containers as $container): ?>
                                                    <option value="<?php echo $container['id']; ?>" <?php echo $containerFilter == $container['id'] ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($container['container_number']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="customer_id">
                                            <i class="fas fa-user"></i> الشركة
                                        </label>
                                        <div class="searchable-select-wrapper">
                                            <select class="form-control searchable-select" id="customer_id" name="customer_id" data-placeholder="اختر الشركة أو ابحث...">
                                                <option value="">جميع الشركات</option>
                                                <?php foreach ($customers as $customer): ?>
                                                    <option value="<?php echo $customer['id']; ?>" <?php echo $customerFilter == $customer['id'] ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($customer['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="type">
                                            <i class="fas fa-exclamation-triangle"></i> نوع الغرامة
                                        </label>
                                        <div class="searchable-select-wrapper">
                                            <select class="form-control searchable-select" id="type" name="type" data-placeholder="اختر نوع الغرامة...">
                                                <option value="">جميع الأنواع</option>
                                                <?php foreach ($penaltyTypes as $key => $value): ?>
                                                    <option value="<?php echo $key; ?>" <?php echo $typeFilter == $key ? 'selected' : ''; ?>>
                                                        <?php echo $value; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="status">
                                            <i class="fas fa-check-circle"></i> الحالة
                                        </label>
                                        <div class="searchable-select-wrapper">
                                            <select class="form-control searchable-select" id="status" name="status" data-placeholder="اختر الحالة...">
                                                <option value="-1">جميع الحالات</option>
                                                <option value="1" <?php echo $statusFilter === 1 ? 'selected' : ''; ?>>مدفوعة</option>
                                                <option value="0" <?php echo $statusFilter === 0 ? 'selected' : ''; ?>>غير مدفوعة</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <div class="d-block">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search"></i> بحث
                                            </button>
                                            <a href="index.php?page=penalties" class="btn btn-default">
                                                <i class="fas fa-redo"></i> إعادة تعيين
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Penalties Table -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-exclamation-triangle"></i> قائمة الغرامات
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button type="button" class="btn btn-tool" data-card-widget="maximize">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body table-responsive p-0">
                        <?php if (empty($penalties)): ?>
                            <div class="callout callout-info m-3">
                                <h5><i class="fas fa-info"></i> ملاحظة:</h5>
                                لا توجد غرامات مطابقة للبحث.
                            </div>
                        <?php else: ?>
                            <table class="table table-hover text-nowrap">
                                <thead>
                                    <tr>
                                        <th style="width: 50px">#</th>
                                        <th>رقم الحاوية</th>
                                        <th>الشركة</th>
                                        <th>نوع الغرامة</th>
                                        <th>المبلغ</th>
                                        <th>تاريخ الغرامة</th>
                                        <th>الحالة</th>
                                        <th style="width: 150px">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($penalties as $index => $penalty): ?>
                                        <tr>
                                            <td><?php echo $index + 1; ?></td>
                                            <td>
                                                <?php if ($penalty['container_id']): ?>
                                                    <a href="index.php?page=containers&action=view&id=<?php echo $penalty['container_id']; ?>" class="text-primary">
                                                        <strong><?php echo htmlspecialchars($penalty['container_number']); ?></strong>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($penalty['customer_id']): ?>
                                                    <a href="index.php?page=customers&action=view&id=<?php echo $penalty['customer_id']; ?>" class="text-primary">
                                                        <?php echo htmlspecialchars($penalty['customer_name']); ?>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $penaltyTypeText = isset($penaltyTypes[$penalty['penalty_type']]) ? $penaltyTypes[$penalty['penalty_type']] : $penalty['penalty_type'];
                                                $typeClass = '';
                                                switch ($penalty['penalty_type']) {
                                                    case 'delay': $typeClass = 'warning'; break;
                                                    case 'damage': $typeClass = 'danger'; break;
                                                    case 'customs': $typeClass = 'info'; break;
                                                    default: $typeClass = 'secondary'; break;
                                                }
                                                ?>
                                                <span class="badge badge-<?php echo $typeClass; ?>"><?php echo $penaltyTypeText; ?></span>
                                            </td>
                                            <td class="text-right">
                                                <strong><?php echo number_format($penalty['amount'], 2); ?></strong>
                                                <small class="text-muted">د.ع</small>
                                            </td>
                                            <td><?php echo date('d/m/Y', strtotime($penalty['penalty_date'])); ?></td>
                                            <td>
                                                <?php if ($penalty['is_paid']): ?>
                                                    <span class="badge badge-success">مدفوعة</span>
                                                <?php else: ?>
                                                    <span class="badge badge-danger">غير مدفوعة</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="index.php?page=penalties&action=view&id=<?php echo $penalty['id']; ?>"
                                                       class="btn btn-info btn-sm" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="index.php?page=penalties&action=edit&id=<?php echo $penalty['id']; ?>"
                                                       class="btn btn-primary btn-sm" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if (!$penalty['is_paid']): ?>
                                                        <a href="index.php?page=financial&action=add_new&type=receipt&penalty_id=<?php echo $penalty['id']; ?>"
                                                           class="btn btn-success btn-sm" title="دفع الغرامة">
                                                            <i class="fas fa-money-bill-wave"></i>
                                                        </a>
                                                        <a href="index.php?page=penalties&action=delete&id=<?php echo $penalty['id']; ?>"
                                                           class="btn btn-danger btn-sm" title="حذف"
                                                           onclick="return confirmDelete(event, 'هل أنت متأكد من حذف هذه الغرامة؟')">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <!-- /.container-fluid -->
        </section>
        <!-- /.content -->
        <?php
        break;
}
?>
