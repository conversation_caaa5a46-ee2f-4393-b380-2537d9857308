<div class="sidebar">
    <div class="sidebar-header">
        <div class="app-brand">
            <a href="index.php" class="app-brand-link">
                <span class="app-brand-logo">
                    <i class="fas fa-shipping-fast fa-2x"></i>
                </span>
                <span class="app-brand-text"><?php echo APP_NAME; ?></span>
            </a>
        </div>
    </div>
    <div class="sidebar-body">
        <ul class="nav">
            <li class="nav-item <?php echo $page == 'dashboard' ? 'active' : ''; ?>">
                <a class="nav-link" href="index.php?page=dashboard">
                    <i class="fas fa-tachometer-alt nav-icon"></i>
                    <span class="nav-text">لوحة التحكم</span>
                </a>
            </li>
            
            <?php if (hasPermission('containers')): ?>
            <li class="nav-item <?php echo $page == 'containers' ? 'active' : ''; ?>">
                <a class="nav-link" href="index.php?page=containers">
                    <i class="fas fa-shipping-container nav-icon"></i>
                    <span class="nav-text">إدارة الحاويات</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if (hasPermission('customers')): ?>
            <li class="nav-item <?php echo $page == 'customers' ? 'active' : ''; ?>">
                <a class="nav-link" href="index.php?page=customers">
                    <i class="fas fa-users nav-icon"></i>
                    <span class="nav-text">إدارة الشركات</span>
                </a>
            </li>
            <?php endif; ?>

            <?php if (hasPermission('financial')): ?>
            <li class="nav-item <?php echo $page == 'traders_list' ? 'active' : ''; ?>">
                <a class="nav-link" href="index.php?page=traders_list">
                    <i class="fas fa-user-tie nav-icon"></i>
                    <span class="nav-text">التجار</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if (hasPermission('financial')): ?>
            <li class="nav-item <?php echo in_array($page, ['financial', 'penalties']) ? 'active' : ''; ?>">
                <a class="nav-link dropdown-toggle" href="#financial-submenu" data-bs-toggle="collapse" role="button" aria-expanded="<?php echo in_array($page, ['financial', 'penalties']) ? 'true' : 'false'; ?>" aria-controls="financial-submenu">
                    <i class="fas fa-money-bill-wave nav-icon"></i>
                    <span class="nav-text">الإدارة المالية</span>
                </a>
                <ul class="collapse <?php echo in_array($page, ['financial', 'penalties']) ? 'show' : ''; ?>" id="financial-submenu">
                    <li class="nav-item <?php echo $page == 'financial' ? 'active' : ''; ?>">
                        <a class="nav-link" href="index.php?page=financial">
                            <i class="fas fa-file-invoice-dollar nav-icon"></i>
                            <span class="nav-text">المستندات المالية</span>
                        </a>
                    </li>
                    <li class="nav-item <?php echo $page == 'penalties' ? 'active' : ''; ?>">
                        <a class="nav-link" href="index.php?page=penalties">
                            <i class="fas fa-exclamation-triangle nav-icon"></i>
                            <span class="nav-text">إدارة الغرامات</span>
                        </a>
                    </li>
                </ul>
            </li>
            <?php endif; ?>
            
            <!-- النقل والتحويلات -->
            <?php if (hasPermission('transfers') || hasPermission('drivers')): ?>
            <li class="nav-item <?php echo in_array($page, ['transfers', 'drivers']) ? 'active' : ''; ?>">
                <a class="nav-link dropdown-toggle" href="#transport-submenu" data-bs-toggle="collapse" role="button" aria-expanded="<?php echo in_array($page, ['transfers', 'drivers']) ? 'true' : 'false'; ?>" aria-controls="transport-submenu">
                    <i class="fas fa-truck nav-icon"></i>
                    <span class="nav-text">النقل والتحويلات</span>
                </a>
                <ul class="collapse <?php echo in_array($page, ['transfers', 'drivers']) ? 'show' : ''; ?>" id="transport-submenu">
                    <?php if (hasPermission('transfers')): ?>
                    <li class="nav-item <?php echo $page == 'transfers' ? 'active' : ''; ?>">
                        <a class="nav-link" href="index.php?page=transfers">
                            <i class="fas fa-shipping-container nav-icon"></i>
                            <span class="nav-text">تحويلات الحاويات</span>
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php if (hasPermission('drivers')): ?>
                    <li class="nav-item <?php echo $page == 'drivers' ? 'active' : ''; ?>">
                        <a class="nav-link" href="index.php?page=drivers">
                            <i class="fas fa-user-tie nav-icon"></i>
                            <span class="nav-text">إدارة السائقين</span>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </li>
            <?php endif; ?>
            
            <?php if (hasPermission('reports')): ?>
            <li class="nav-item <?php echo $page == 'reports' ? 'active' : ''; ?>">
                <a class="nav-link dropdown-toggle" href="#reports-submenu" data-bs-toggle="collapse" role="button" aria-expanded="<?php echo $page == 'reports' ? 'true' : 'false'; ?>" aria-controls="reports-submenu">
                    <i class="fas fa-chart-bar nav-icon"></i>
                    <span class="nav-text">التقارير</span>
                </a>
                <ul class="collapse <?php echo $page == 'reports' ? 'show' : ''; ?>" id="reports-submenu">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php?page=reports&type=financial">
                            <i class="fas fa-file-invoice nav-icon"></i>
                            <span class="nav-text">التقارير المالية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php?page=reports&type=cash">
                            <i class="fas fa-cash-register nav-icon"></i>
                            <span class="nav-text">التقارير النقدية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php?page=reports&type=regulatory">
                            <i class="fas fa-clipboard-check nav-icon"></i>
                            <span class="nav-text">التقارير الرقابية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php?page=reports&type=penalties">
                            <i class="fas fa-exclamation-circle nav-icon"></i>
                            <span class="nav-text">تقارير الغرامات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php?page=reports&type=customer">
                            <i class="fas fa-user-check nav-icon"></i>
                            <span class="nav-text">كشف حساب شركة</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php?page=reports&type=customer_balances">
                            <i class="fas fa-users-cog nav-icon"></i>
                            <span class="nav-text">أرصدة العملاء</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php?page=export_customers">
                            <i class="fas fa-file-export fa-fw"></i> تصدير بيانات العملاء
                        </a>
                    </li>
                </ul>
            </li>
            <?php endif; ?>
            
            <?php if (hasPermission('users')): ?>
            <li class="nav-item <?php echo $page == 'users' ? 'active' : ''; ?>">
                <a class="nav-link" href="index.php?page=users">
                    <i class="fas fa-user-cog nav-icon"></i>
                    <span class="nav-text">إدارة المستخدمين</span>
                </a>
            </li>
            <?php endif; ?>
            
            <?php if (hasPermission('settings')): ?>
            <li class="nav-item <?php echo $page == 'settings' ? 'active' : ''; ?>">
                <a class="nav-link" href="index.php?page=settings">
                    <i class="fas fa-cogs nav-icon"></i>
                    <span class="nav-text">الإعدادات</span>
                </a>
            </li>
            <?php endif; ?>
            
            <li class="nav-item <?php echo $page == 'profile' ? 'active' : ''; ?>">
                <a class="nav-link" href="index.php?page=profile">
                    <i class="fas fa-user nav-icon"></i>
                    <span class="nav-text">الملف الشخصي</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt nav-icon"></i>
                    <span class="nav-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </div>
</div>
