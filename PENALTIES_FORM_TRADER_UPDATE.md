# تحديث نموذج إضافة وتعديل الغرامات للعمل مع التجار

## نظرة عامة

تم تحديث صفحة إضافة وتعديل الغرامات (`pages/penalties_form.php`) لتستخدم التجار بدلاً من الشركات، مع إضافة وظيفة عرض الحاويات الخاصة بالتاجر المحدد تلقائياً.

## التغييرات المطبقة

### ✅ **1. تحديث واجهة المستخدم**

#### **قبل التحديث:**
```html
<!-- Customer Selection -->
<label for="customer_id">
    <i class="fas fa-user text-success"></i> الشركة
    <span class="text-danger">*</span>
</label>
<select class="form-control" id="customer_id" name="customer_id" onchange="updateContainersList()" required>
    <option value="">اختر الشركة أولاً</option>
    <?php foreach ($customers as $customer): ?>
        <option value="<?php echo $customer['id']; ?>">
            <?php echo htmlspecialchars($customer['name']); ?>
        </option>
    <?php endforeach; ?>
</select>
```

#### **بعد التحديث:**
```html
<!-- Trader Selection -->
<label for="trader_id">
    <i class="fas fa-user-tie text-success"></i> التاجر
    <span class="text-danger">*</span>
</label>
<select class="form-control searchable-select" id="trader_id" name="trader_id" onchange="updateContainersList()" required>
    <option value="">اختر التاجر أولاً</option>
    <?php foreach ($traders as $trader): ?>
        <option value="<?php echo $trader['id']; ?>">
            <?php echo htmlspecialchars($trader['name']); ?>
        </option>
    <?php endforeach; ?>
</select>
```

### ✅ **2. تحديث قائمة الحاويات**

#### **قبل التحديث:**
```html
<option value="<?php echo $container['id']; ?>"
        data-customer-id="<?php echo $container['customer_id']; ?>"
        style="display: none;">
    <?php echo htmlspecialchars($container['container_number']); ?>
    - (<?php echo htmlspecialchars($container['customer_name']); ?>)
</option>
```

#### **بعد التحديث:**
```html
<option value="<?php echo $container['id']; ?>"
        data-trader-id="<?php echo $container['trader_id']; ?>"
        style="display: none;">
    <?php echo htmlspecialchars($container['container_number']); ?>
    - (<?php echo htmlspecialchars($container['customer_name']); ?>)
</option>
```

### ✅ **3. تحديث معالجة البيانات في PHP**

#### **البيانات الافتراضية:**
```php
// قبل التحديث
$penalty = [
    'id' => 0,
    'container_id' => '',
    'customer_id' => '',  // ❌
    'penalty_type' => '',
    // ...
];

// بعد التحديث
$penalty = [
    'id' => 0,
    'container_id' => '',
    'trader_id' => '',    // ✅
    'penalty_type' => '',
    // ...
];
```

#### **جمع البيانات من النموذج:**
```php
// قبل التحديث
$penaltyData = [
    'container_id' => !empty($_POST['container_id']) ? (int)$_POST['container_id'] : null,
    'customer_id' => (int)$_POST['customer_id'],  // ❌
    'penalty_type' => trim($_POST['penalty_type']),
    // ...
];

// بعد التحديث
$penaltyData = [
    'container_id' => !empty($_POST['container_id']) ? (int)$_POST['container_id'] : null,
    'trader_id' => (int)$_POST['trader_id'],       // ✅
    'penalty_type' => trim($_POST['penalty_type']),
    // ...
];
```

#### **التحقق من صحة البيانات:**
```php
// قبل التحديث
if (empty($penaltyData['customer_id'])) {
    $errors['customer_id'] = 'يجب اختيار الشركة';  // ❌
}

// بعد التحديث
if (empty($penaltyData['trader_id'])) {
    $errors['trader_id'] = 'يجب اختيار التاجر';    // ✅
}
```

### ✅ **4. تحديث استعلامات قاعدة البيانات**

#### **استعلام INSERT:**
```sql
-- قبل التحديث
INSERT INTO penalties (
    container_id, customer_id, penalty_type, amount, 
    penalty_date, due_date, description, is_paid, 
    payment_date, payment_reference, notes, created_at, updated_at
) VALUES (
    :container_id, :customer_id, :penalty_type, :amount,
    -- ...
)

-- بعد التحديث
INSERT INTO penalties (
    container_id, trader_id, penalty_type, amount, 
    penalty_date, due_date, description, is_paid, 
    payment_date, payment_reference, notes, created_at, updated_at
) VALUES (
    :container_id, :trader_id, :penalty_type, :amount,
    -- ...
)
```

#### **استعلام UPDATE:**
```sql
-- قبل التحديث
UPDATE penalties SET 
    container_id = :container_id,
    customer_id = :customer_id,
    penalty_type = :penalty_type,
    -- ...

-- بعد التحديث
UPDATE penalties SET 
    container_id = :container_id,
    trader_id = :trader_id,
    penalty_type = :penalty_type,
    -- ...
```

#### **استعلام تحميل الحاويات:**
```sql
-- قبل التحديث
SELECT c.id, c.container_number, c.customer_id, cu.name as customer_name
FROM containers c
LEFT JOIN customers cu ON c.customer_id = cu.id
ORDER BY cu.name, c.container_number

-- بعد التحديث
SELECT c.id, c.container_number, c.trader_id, c.customer_id, cu.name as customer_name, t.name as trader_name
FROM containers c
LEFT JOIN customers cu ON c.customer_id = cu.id
LEFT JOIN traders t ON c.trader_id = t.id
ORDER BY t.name, c.container_number
```

#### **تحميل قائمة التجار:**
```php
// قبل التحديث
$customersStmt = $db->query("SELECT id, name FROM customers ORDER BY name");
$customers = $customersStmt->fetchAll();

// بعد التحديث
$tradersStmt = $db->query("SELECT id, name FROM traders WHERE active = 1 ORDER BY name");
$traders = $tradersStmt->fetchAll();
```

### ✅ **5. تحديث دالة JavaScript**

#### **قبل التحديث:**
```javascript
// تحديث قائمة الحاويات عند تغيير الشركة
function updateContainersList() {
    const customerId = document.getElementById('customer_id').value;
    const containerSelect = document.getElementById('container_id');
    const containerOptions = containerSelect.querySelectorAll('option');

    // إعادة تعيين قائمة الحاويات
    containerSelect.value = '';

    // إخفاء جميع الخيارات ما عدا الخيار الأول
    for (let i = 1; i < containerOptions.length; i++) {
        const option = containerOptions[i];
        const optionCustomerId = option.getAttribute('data-customer-id');

        if (!customerId) {
            option.style.display = 'none';
        } else if (optionCustomerId == customerId) {
            option.style.display = '';
            visibleCount++;
        } else {
            option.style.display = 'none';
        }
    }

    // تحديث النص الافتراضي
    if (!customerId) {
        defaultOption.textContent = 'اختر الشركة أولاً لعرض حاوياته';
    } else if (visibleCount === 0) {
        defaultOption.textContent = 'لا توجد حاويات لهذا الشركة';
    }
}
```

#### **بعد التحديث:**
```javascript
// تحديث قائمة الحاويات عند تغيير التاجر
function updateContainersList() {
    const traderId = document.getElementById('trader_id').value;
    const containerSelect = document.getElementById('container_id');
    const containerOptions = containerSelect.querySelectorAll('option');

    // إعادة تعيين قائمة الحاويات
    containerSelect.value = '';

    // إخفاء جميع الخيارات ما عدا الخيار الأول
    for (let i = 1; i < containerOptions.length; i++) {
        const option = containerOptions[i];
        const optionTraderId = option.getAttribute('data-trader-id');

        if (!traderId) {
            option.style.display = 'none';
        } else if (optionTraderId == traderId) {
            option.style.display = '';
            visibleCount++;
        } else {
            option.style.display = 'none';
        }
    }

    // تحديث النص الافتراضي
    if (!traderId) {
        defaultOption.textContent = 'اختر التاجر أولاً لعرض حاوياته';
    } else if (visibleCount === 0) {
        defaultOption.textContent = 'لا توجد حاويات لهذا التاجر';
    }

    // تحديث Searchable Select
    if (window.SearchableSelectManager) {
        const instance = window.SearchableSelectManager.instances.get(containerSelect);
        if (instance) {
            instance.populateOptions();
        }
    }
}
```

## الميزات الجديدة

### 🎯 **اختيار التاجر أولاً:**
1. المستخدم يختار التاجر من القائمة المنسدلة
2. تظهر تلقائياً جميع الحاويات الخاصة بهذا التاجر فقط
3. إخفاء حاويات التجار الآخرين

### 🔍 **البحث في القوائم:**
- إمكانية البحث في قائمة التجار
- إمكانية البحث في قائمة الحاويات المفلترة

### 📋 **عرض معلومات إضافية:**
- عرض اسم الشركة مع رقم الحاوية
- ترتيب الحاويات حسب اسم التاجر

### ⚡ **تحديث تلقائي:**
- تحديث فوري لقائمة الحاويات عند تغيير التاجر
- رسائل توضيحية واضحة للمستخدم

## كيفية الاستخدام

### **إضافة غرامة جديدة:**
1. اذهب إلى: `index.php?page=penalties&action=add`
2. اختر التاجر من القائمة المنسدلة الأولى
3. ستظهر تلقائياً حاويات هذا التاجر في القائمة الثانية
4. اختر الحاوية المطلوبة
5. أكمل باقي بيانات الغرامة

### **تعديل غرامة موجودة:**
1. اذهب إلى: `index.php?page=penalties&action=edit&id=X`
2. ستظهر البيانات المحفوظة مع التاجر والحاوية المحددين
3. يمكن تغيير التاجر وستتحدث قائمة الحاويات تلقائياً

### **البحث في القوائم:**
1. اضغط على قائمة التجار
2. ابدأ بكتابة اسم التاجر للبحث
3. نفس الشيء مع قائمة الحاويات

## الفوائد المحققة

### 📊 **تنظيم أفضل:**
- ربط منطقي بين الغرامات والتجار المسؤولين
- سهولة تتبع الغرامات حسب التاجر
- تقليل الأخطاء في اختيار الحاوية الخاطئة

### 🚀 **تحسين الأداء:**
- عرض الحاويات ذات الصلة فقط
- تقليل الوقت المطلوب لإدخال البيانات
- واجهة أكثر وضوحاً وسهولة

### 💡 **تجربة مستخدم محسنة:**
- خطوات واضحة ومنطقية
- تحديث تلقائي للقوائم
- رسائل توضيحية مفيدة
- إمكانية البحث في جميع القوائم

## الاختبار والتحقق

### **نقاط الاختبار:**
1. **إضافة غرامة جديدة:** `index.php?page=penalties&action=add`
2. **تعديل غرامة موجودة:** `index.php?page=penalties&action=edit&id=1`
3. **اختبار تغيير التاجر** ومشاهدة تحديث قائمة الحاويات
4. **اختبار البحث** في قائمة التجار والحاويات
5. **اختبار الحفظ** والتأكد من حفظ البيانات بشكل صحيح

### **التحقق من البيانات:**
- ✅ حفظ معرف التاجر في قاعدة البيانات
- ✅ عرض الحاويات الصحيحة للتاجر المحدد
- ✅ عمل البحث في جميع القوائم
- ✅ التحديث التلقائي للقوائم
- ✅ عرض الرسائل التوضيحية الصحيحة

## الخلاصة

تم بنجاح تحديث نموذج إضافة وتعديل الغرامات ليعمل مع التجار بدلاً من الشركات، مما يوفر:

- ✅ **ربط منطقي** بين الغرامات والتجار المسؤولين
- ✅ **عرض تلقائي** للحاويات الخاصة بالتاجر المحدد
- ✅ **واجهة محسنة** مع إمكانية البحث
- ✅ **تجربة مستخدم سلسة** ومنطقية
- ✅ **تقليل الأخطاء** في إدخال البيانات
- ✅ **تحسين الكفاءة** في العمل

النظام الآن يوفر طريقة أكثر دقة وسهولة لإدارة الغرامات من منظور التجار! 🎉

---

**تاريخ التحديث:** 2024-12-19  
**الإصدار:** 2.0  
**المطور:** Augment Agent
