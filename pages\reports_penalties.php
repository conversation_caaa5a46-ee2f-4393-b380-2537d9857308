<?php
// التحقق من الصلاحيات
if (!hasPermission('reports')) {
    header('Location: index.php?page=unauthorized');
    exit;
}

// استعلام تقارير الغرامات
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$penaltyType = isset($_GET['penalty_type']) ? $_GET['penalty_type'] : '';
$isPaid = isset($_GET['is_paid']) ? $_GET['is_paid'] : '';

// بناء شرط الاستعلام
$conditions = [];
$params = [];

// إضافة شرط التاريخ
$conditions[] = "p.penalty_date BETWEEN :start_date AND :end_date";
$params['start_date'] = $startDate;
$params['end_date'] = $endDate;

// إضافة شرط نوع الغرامة إذا تم تحديده
if (!empty($penaltyType)) {
    $conditions[] = "p.penalty_type = :penalty_type";
    $params['penalty_type'] = $penaltyType;
}

// إضافة شرط حالة الدفع إذا تم تحديده
if ($isPaid !== '') {
    $conditions[] = "p.is_paid = :is_paid";
    $params['is_paid'] = $isPaid;
}

// بناء جملة الاستعلام
$whereClause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";

// استعلام إحصائيات الغرامات
$statsQuery = "
    SELECT
        COUNT(*) as total_penalties,
        SUM(CASE WHEN p.is_paid = 1 THEN 1 ELSE 0 END) as paid_penalties,
        SUM(CASE WHEN p.is_paid = 0 THEN 1 ELSE 0 END) as unpaid_penalties,
        SUM(p.amount) as total_amount,
        SUM(CASE WHEN p.is_paid = 1 THEN p.amount ELSE 0 END) as paid_amount,
        SUM(CASE WHEN p.is_paid = 0 THEN p.amount ELSE 0 END) as unpaid_amount
    FROM
        penalties p
    $whereClause
";

// استعلام تفاصيل الغرامات
$penaltiesQuery = "
    SELECT
        p.id,
        p.penalty_type,
        p.amount,
        p.penalty_date,
        p.due_date,
        p.is_paid,
        p.payment_date,
        p.description,
        c.container_number,
        cu.name as customer_name
    FROM
        penalties p
    LEFT JOIN
        containers c ON p.container_id = c.id
    LEFT JOIN
        customers cu ON p.customer_id = cu.id
    $whereClause
    ORDER BY
        p.penalty_date DESC, p.id DESC
";

try {
    // تنفيذ استعلام إحصائيات الغرامات
    $statsStmt = $db->prepare($statsQuery);
    $statsStmt->execute($params);
    $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);

    // تنفيذ استعلام تفاصيل الغرامات
    $penaltiesStmt = $db->prepare($penaltiesQuery);
    $penaltiesStmt->execute($params);
    $penalties = $penaltiesStmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // تسجيل الخطأ
    error_log("خطأ في استعلام تقارير الغرامات: " . $e->getMessage());
    $error = "حدث خطأ أثناء استرجاع البيانات. يرجى المحاولة مرة أخرى.";
}

// تحديد أنواع الغرامات للفلتر
$penaltyTypes = [
    'delay' => 'تأخير',
    'damage' => 'أضرار',
    'customs' => 'كمركية',
    'other' => 'أخرى'
];
?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">
                    <i class="fas fa-exclamation-triangle text-warning"></i>
                    تقارير الغرامات
                </h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php?page=reports">التقارير</a></li>
                    <li class="breadcrumb-item active">تقارير الغرامات</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">

        <!-- Navigation Tabs -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="btn-group" role="group">
                    <a href="index.php?page=reports&type=financial" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line"></i> التقارير المالية
                    </a>
                    <a href="index.php?page=reports&type=cash" class="btn btn-outline-primary">
                        <i class="fas fa-money-bill-wave"></i> التقارير النقدية
                    </a>
                    <a href="index.php?page=reports&type=regulatory" class="btn btn-outline-primary">
                        <i class="fas fa-clipboard-check"></i> التقارير الرقابية
                    </a>
                    <a href="index.php?page=reports&type=penalties" class="btn btn-primary">
                        <i class="fas fa-exclamation-triangle"></i> تقارير الغرامات
                    </a>
                </div>
                <button type="button" class="btn btn-success float-right" onclick="window.print()">
                    <i class="fas fa-print"></i> طباعة التقرير
                </button>
            </div>
        </div>

        <!-- Search and Filter Card -->
        <div class="card card-warning card-outline no-print">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter"></i> معايير التقرير
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form method="get" action="index.php">
                    <input type="hidden" name="page" value="reports">
                    <input type="hidden" name="type" value="penalties">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="start_date">
                                    <i class="fas fa-calendar-alt text-primary"></i> من تاريخ
                                </label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                    </div>
                                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $startDate; ?>">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="end_date">
                                    <i class="fas fa-calendar-alt text-primary"></i> إلى تاريخ
                                </label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                    </div>
                                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $endDate; ?>">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="penalty_type">
                                    <i class="fas fa-exclamation-triangle text-warning"></i> نوع الغرامة
                                </label>
                                <div class="searchable-select-wrapper">
                                    <select class="form-control searchable-select" id="penalty_type" name="penalty_type" data-placeholder="اختر نوع الغرامة...">
                                        <option value="">جميع الأنواع</option>
                                        <?php foreach ($penaltyTypes as $value => $label): ?>
                                            <option value="<?php echo $value; ?>" <?php echo $penaltyType === $value ? 'selected' : ''; ?>>
                                                <?php echo $label; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="is_paid">
                                    <i class="fas fa-credit-card text-success"></i> حالة الدفع
                                </label>
                                <div class="searchable-select-wrapper">
                                    <select class="form-control searchable-select" id="is_paid" name="is_paid" data-placeholder="اختر حالة الدفع...">
                                        <option value="">جميع الحالات</option>
                                        <option value="1" <?php echo $isPaid === '1' ? 'selected' : ''; ?>>مدفوعة</option>
                                        <option value="0" <?php echo $isPaid === '0' ? 'selected' : ''; ?>>غير مدفوعة</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> عرض التقرير
                            </button>
                            <a href="index.php?page=reports&type=penalties" class="btn btn-default">
                                <i class="fas fa-redo"></i> إعادة تعيين
                            </a>
                            <a href="index.php?page=penalties" class="btn btn-warning">
                                <i class="fas fa-list"></i> إدارة الغرامات
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php else: ?>
        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-lg-3 col-6">
                <!-- small box -->
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3><?php echo $stats['total_penalties']; ?></h3>
                        <p>إجمالي الغرامات</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <a href="#penalties-section" class="small-box-footer">
                        المزيد من التفاصيل <i class="fas fa-arrow-circle-right"></i>
                    </a>
                </div>
            </div>
            <!-- ./col -->
            <div class="col-lg-3 col-6">
                <!-- small box -->
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3><?php echo $stats['paid_penalties']; ?></h3>
                        <p>الغرامات المدفوعة</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <a href="#penalties-section" class="small-box-footer">
                        المزيد من التفاصيل <i class="fas fa-arrow-circle-right"></i>
                    </a>
                </div>
            </div>
            <!-- ./col -->
            <div class="col-lg-3 col-6">
                <!-- small box -->
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3><?php echo $stats['unpaid_penalties']; ?></h3>
                        <p>الغرامات غير المدفوعة</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <a href="#penalties-section" class="small-box-footer">
                        المزيد من التفاصيل <i class="fas fa-arrow-circle-right"></i>
                    </a>
                </div>
            </div>
            <!-- ./col -->
            <div class="col-lg-3 col-6">
                <!-- small box -->
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3><?php echo number_format($stats['total_amount'], 0); ?></h3>
                        <p>إجمالي المبالغ (د.ع)</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <a href="#penalties-section" class="small-box-footer">
                        المزيد من التفاصيل <i class="fas fa-arrow-circle-right"></i>
                    </a>
                </div>
            </div>
            <!-- ./col -->
        </div>
        <!-- /.row -->

        <!-- Financial Summary Cards -->
        <div class="row">
            <div class="col-md-6">
                <div class="info-box">
                    <span class="info-box-icon bg-success elevation-1">
                        <i class="fas fa-money-bill-wave"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">المبالغ المحصلة</span>
                        <span class="info-box-number">
                            <?php echo number_format($stats['paid_amount'], 0); ?>
                            <small>د.ع</small>
                        </span>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: <?php echo $stats['total_amount'] > 0 ? ($stats['paid_amount'] / $stats['total_amount']) * 100 : 0; ?>%"></div>
                        </div>
                        <span class="progress-description">
                            <?php echo $stats['total_amount'] > 0 ? round(($stats['paid_amount'] / $stats['total_amount']) * 100, 1) : 0; ?>% من إجمالي الغرامات
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-box">
                    <span class="info-box-icon bg-danger elevation-1">
                        <i class="fas fa-exclamation-circle"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">المبالغ المستحقة</span>
                        <span class="info-box-number">
                            <?php echo number_format($stats['unpaid_amount'], 0); ?>
                            <small>د.ع</small>
                        </span>
                        <div class="progress">
                            <div class="progress-bar bg-danger" style="width: <?php echo $stats['total_amount'] > 0 ? ($stats['unpaid_amount'] / $stats['total_amount']) * 100 : 0; ?>%"></div>
                        </div>
                        <span class="progress-description">
                            <?php echo $stats['total_amount'] > 0 ? round(($stats['unpaid_amount'] / $stats['total_amount']) * 100, 1) : 0; ?>% من إجمالي الغرامات
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- تفاصيل الغرامات -->
        <div class="card" id="penalties-section">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-list"></i> تفاصيل الغرامات
                </h3>
                <div class="card-tools">
                    <span class="badge badge-warning">الفترة: <?php echo $startDate; ?> - <?php echo $endDate; ?></span>
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-tool" data-card-widget="maximize">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>
            <div class="card-body table-responsive p-0">
                <table class="table table-hover text-nowrap">
                    <thead>
                        <tr>
                            <th style="width: 50px">#</th>
                            <th>نوع الغرامة</th>
                            <th>الشركة</th>
                            <th>رقم الحاوية</th>
                            <th>تاريخ الغرامة</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>المبلغ</th>
                            <th>حالة الدفع</th>
                            <th>تاريخ الدفع</th>
                            <th style="width: 100px" class="no-print">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($penalties)): ?>
                            <tr>
                                <td colspan="10" class="text-center py-4">لا توجد بيانات متاحة</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($penalties as $index => $penalty): ?>
                                <tr>
                                    <td><?php echo $index + 1; ?></td>
                                    <td>
                                        <?php
                                            $typeLabels = [
                                                'delay' => '<span class="badge badge-warning">تأخير</span>',
                                                'damage' => '<span class="badge badge-danger">أضرار</span>',
                                                'customs' => '<span class="badge badge-info">كمركية</span>',
                                                'other' => '<span class="badge badge-secondary">أخرى</span>'
                                            ];
                                            echo $typeLabels[$penalty['penalty_type']] ?? '<span class="badge badge-light">' . htmlspecialchars($penalty['penalty_type']) . '</span>';
                                        ?>
                                    </td>
                                    <td>
                                        <i class="fas fa-user-circle text-muted mr-1"></i>
                                        <?php echo htmlspecialchars($penalty['customer_name'] ?? '-'); ?>
                                    </td>
                                    <td>
                                        <strong class="text-primary"><?php echo htmlspecialchars($penalty['container_number'] ?? '-'); ?></strong>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar mr-1"></i>
                                            <?php echo date('d/m/Y', strtotime($penalty['penalty_date'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <i class="fas fa-clock mr-1"></i>
                                            <?php echo date('d/m/Y', strtotime($penalty['due_date'])); ?>
                                        </small>
                                    </td>
                                    <td class="text-right">
                                        <strong><?php echo number_format($penalty['amount'], 0); ?></strong>
                                        <small class="text-muted">د.ع</small>
                                    </td>
                                    <td>
                                        <?php if ($penalty['is_paid']): ?>
                                            <span class="badge badge-success">
                                                <i class="fas fa-check-circle"></i> مدفوعة
                                            </span>
                                        <?php else: ?>
                                            <span class="badge badge-danger">
                                                <i class="fas fa-times-circle"></i> غير مدفوعة
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($penalty['payment_date']): ?>
                                            <small class="text-success">
                                                <i class="fas fa-calendar-check mr-1"></i>
                                                <?php echo date('d/m/Y', strtotime($penalty['payment_date'])); ?>
                                            </small>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="no-print">
                                        <div class="btn-group">
                                            <a href="index.php?page=penalties&action=view&id=<?php echo $penalty['id']; ?>"
                                               class="btn btn-info btn-sm" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="index.php?page=penalties&action=edit&id=<?php echo $penalty['id']; ?>"
                                               class="btn btn-primary btn-sm" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>

    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->

<!-- أنماط الطباعة -->
<style media="print">
    @page {
        size: A4 landscape;
        margin: 1cm;
    }
    body {
        font-size: 12pt;
    }
    .no-print {
        display: none !important;
    }
    .card {
        border: 1px solid #ddd;
        margin-bottom: 20px;
        break-inside: avoid;
    }
    .table {
        width: 100%;
        border-collapse: collapse;
    }
    .table th, .table td {
        border: 1px solid #ddd;
        padding: 8px;
    }
    .content-header h1 {
        font-size: 18pt;
        margin-bottom: 10px;
    }
    .breadcrumb {
        display: none;
    }
    .small-box {
        border: 1px solid #ddd;
        margin-bottom: 10px;
    }
    .info-box {
        border: 1px solid #ddd;
        margin-bottom: 10px;
    }
</style>
