<?php
// التحقق من الصلاحيات
if (!hasPermission('reports')) {
    header('Location: index.php?page=unauthorized');
    exit;
}

// استعلام التقارير النقدية
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$documentType = isset($_GET['document_type']) ? $_GET['document_type'] : '';

// بناء شرط الاستعلام
$conditions = [];
$params = [];

// إضافة شرط التاريخ
$conditions[] = "document_date BETWEEN :start_date AND :end_date";
$params['start_date'] = $startDate;
$params['end_date'] = $endDate;

// إضافة شرط نوع المستند إذا تم تحديده
if (!empty($documentType)) {
    $conditions[] = "document_type = :document_type";
    $params['document_type'] = $documentType;
}

// بناء جملة الاستعلام
$whereClause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";

// استعلام الإيرادات
$revenueQuery = "
    SELECT 
        SUM(amount) as total_revenue
    FROM 
        financial_documents
    $whereClause
    AND document_type IN ('receipt', 'capital')
";

// استعلام المصروفات
$expenseQuery = "
    SELECT 
        SUM(amount) as total_expense
    FROM 
        financial_documents
    $whereClause
    AND document_type IN ('payment', 'expense')
";

// استعلام تفاصيل المستندات المالية
$documentsQuery = "
    SELECT 
        fd.id,
        fd.document_number,
        fd.document_type,
        fd.document_date,
        fd.amount,
        c.name as customer_name,
        fd.description,
        fd.payment_method,
        u.full_name as created_by_name
    FROM 
        financial_documents fd
    LEFT JOIN 
        customers c ON fd.customer_id = c.id
    LEFT JOIN 
        users u ON fd.created_by = u.id
    $whereClause
    ORDER BY 
        fd.document_date DESC, fd.id DESC
";

try {
    // تنفيذ استعلام الإيرادات
    $revenueStmt = $db->prepare($revenueQuery);
    $revenueStmt->execute($params);
    $revenueData = $revenueStmt->fetch(PDO::FETCH_ASSOC);
    $totalRevenue = $revenueData['total_revenue'] ?: 0;

    // تنفيذ استعلام المصروفات
    $expenseStmt = $db->prepare($expenseQuery);
    $expenseStmt->execute($params);
    $expenseData = $expenseStmt->fetch(PDO::FETCH_ASSOC);
    $totalExpense = $expenseData['total_expense'] ?: 0;

    // حساب الرصيد
    $balance = $totalRevenue - $totalExpense;

    // تنفيذ استعلام تفاصيل المستندات المالية
    $documentsStmt = $db->prepare($documentsQuery);
    $documentsStmt->execute($params);
    $documents = $documentsStmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // تسجيل الخطأ
    error_log("خطأ في استعلام التقارير النقدية: " . $e->getMessage());
    $error = "حدث خطأ أثناء استرجاع البيانات. يرجى المحاولة مرة أخرى.";
}

// تحديد أنواع المستندات للفلتر
$documentTypes = [
    'receipt' => 'إيصال استلام',
    'payment' => 'سند صرف',
    'expense' => 'مصروفات',
    'transfer' => 'تحويل',
    'capital' => 'رأس مال'
];
?>

<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h2 class="page-title">التقارير النقدية</h2>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index.php">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="index.php?page=reports">التقارير</a></li>
                    <li class="breadcrumb-item active">التقارير النقدية</li>
                </ul>
            </div>
            <div class="col-auto">
                <button type="button" class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print"></i> طباعة التقرير
                </button>
            </div>
        </div>
    </div>

    <!-- نموذج البحث -->
    <div class="card mb-4 no-print">
        <div class="card-body">
            <form method="get" action="index.php">
                <input type="hidden" name="page" value="reports">
                <input type="hidden" name="type" value="cash">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="start_date" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $startDate; ?>">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="end_date" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $endDate; ?>">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="document_type" class="form-label">نوع المستند</label>
                        <div class="searchable-select-wrapper">
                            <select class="form-select searchable-select" id="document_type" name="document_type" data-placeholder="اختر نوع المستند...">
                                <option value="">الكل</option>
                                <?php foreach ($documentTypes as $value => $label): ?>
                                    <option value="<?php echo $value; ?>" <?php echo $documentType === $value ? 'selected' : ''; ?>>
                                        <?php echo $label; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php else: ?>
        <!-- ملخص التقرير -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-success-light">
                    <div class="card-body text-center">
                        <h5 class="card-title">إجمالي الإيرادات</h5>
                        <h3 class="mb-0 number"><?php echo number_format($totalRevenue, 2); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-danger-light">
                    <div class="card-body text-center">
                        <h5 class="card-title">إجمالي المصروفات</h5>
                        <h3 class="mb-0 number"><?php echo number_format($totalExpense, 2); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card <?php echo $balance >= 0 ? 'bg-primary-light' : 'bg-warning-light'; ?>">
                    <div class="card-body text-center">
                        <h5 class="card-title">الرصيد</h5>
                        <h3 class="mb-0 number"><?php echo number_format($balance, 2); ?></h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- تفاصيل المستندات المالية -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">تفاصيل المستندات المالية</h5>
                <small class="text-muted">الفترة من <?php echo date('Y/m/d', strtotime($startDate)); ?> إلى <?php echo date('Y/m/d', strtotime($endDate)); ?></small>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>رقم المستند</th>
                                <th>نوع المستند</th>
                                <th>التاريخ</th>
                                <th>الشركة</th>
                                <th>الوصف</th>
                                <th>طريقة الدفع</th>
                                <th>المبلغ</th>
                                <th class="no-print">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($documents)): ?>
                                <tr>
                                    <td colspan="9" class="text-center">لا توجد بيانات متاحة</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($documents as $index => $document): ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td><?php echo htmlspecialchars($document['document_number']); ?></td>
                                        <td>
                                            <?php 
                                                $typeLabels = [
                                                    'receipt' => '<span class="badge bg-success">إيصال استلام</span>',
                                                    'payment' => '<span class="badge bg-danger">سند صرف</span>',
                                                    'expense' => '<span class="badge bg-warning">مصروفات</span>',
                                                    'transfer' => '<span class="badge bg-info">تحويل</span>',
                                                    'capital' => '<span class="badge bg-primary">رأس مال</span>'
                                                ];
                                                echo $typeLabels[$document['document_type']] ?? $document['document_type'];
                                            ?>
                                        </td>
                                        <td class="date"><?php echo date('Y/m/d', strtotime($document['document_date'])); ?></td>
                                        <td><?php echo htmlspecialchars($document['customer_name'] ?? '-'); ?></td>
                                        <td><?php echo htmlspecialchars($document['description'] ?? '-'); ?></td>
                                        <td><?php echo htmlspecialchars($document['payment_method'] ?? '-'); ?></td>
                                        <td class="number"><?php echo number_format($document['amount'], 2); ?></td>
                                        <td class="no-print">
                                            <a href="index.php?page=financial_view&id=<?php echo $document['id']; ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="index.php?page=financial_print&id=<?php echo $document['id']; ?>" class="btn btn-sm btn-secondary" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- أنماط الطباعة -->
<style media="print">
    @page {
        size: A4;
        margin: 1cm;
    }
    body {
        font-size: 12pt;
    }
    .no-print {
        display: none !important;
    }
    .card {
        border: 1px solid #ddd;
        margin-bottom: 20px;
        break-inside: avoid;
    }
    .table {
        width: 100%;
        border-collapse: collapse;
    }
    .table th, .table td {
        border: 1px solid #ddd;
        padding: 8px;
    }
    .page-header h2 {
        font-size: 18pt;
        margin-bottom: 10px;
    }
    .breadcrumb {
        display: none;
    }
</style>
