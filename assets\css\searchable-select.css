/**
 * Searchable Select Styles
 * أنماط CSS لمكون البحث في القوائم المنسدلة
 */

/* الحاوية الرئيسية */
.searchable-select-wrapper {
    position: relative;
    display: block;
}

.searchable-select-wrapper.searchable-select-initialized {
    z-index: 1;
}

/* القائمة المنسدلة الأصلية */
.searchable-select {
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.searchable-select:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.searchable-select.open {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-color: #007bff;
}

.searchable-select.just-selected {
    background-color: #d4edda;
    border-color: #28a745;
    transition: all 0.3s ease;
}

/* حقل البحث */
.select-search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-top: none;
    font-size: 14px;
    background: #fff;
    outline: none;
    display: none;
    transition: all 0.3s ease;
    z-index: 1001;
}

.select-search-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.searchable-select.open + .select-search-input {
    display: block;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

/* حاوية الخيارات */
.select-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #007bff;
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 250px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.searchable-select.open ~ .select-options {
    display: block;
}

/* خيارات القائمة */
.select-option {
    padding: 10px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: all 0.2s ease;
    position: relative;
}

.select-option:last-child {
    border-bottom: none;
}

.select-option:hover,
.select-option.highlighted {
    background-color: #f8f9fa;
    transform: translateX(2px);
}

.select-option.selected {
    background-color: #007bff;
    color: white;
}

.select-option.selected:hover {
    background-color: #0056b3;
}

.select-option.hidden {
    display: none;
}

/* محتوى الخيار */
.option-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.option-text {
    flex: 1;
}

.option-text mark {
    background-color: #fff3cd;
    color: #856404;
    padding: 1px 3px;
    border-radius: 2px;
    font-weight: 600;
}

.select-option.selected .option-text mark {
    background-color: rgba(255, 255, 255, 0.3);
    color: white;
}

/* رسالة عدم وجود نتائج */
.no-results {
    padding: 15px 12px;
    color: #6c757d;
    font-style: italic;
    text-align: center;
    background-color: #f8f9fa;
}

/* تحسينات للقائمة المنسدلة الأصلية */
.searchable-select-wrapper .form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-left: 2.5rem;
}

.searchable-select-wrapper .form-control:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23007bff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

.searchable-select.open + .select-search-input + .select-options .form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23007bff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 12 4-4 4 4'/%3e%3c/svg%3e");
}

/* دعم RTL */
[dir="rtl"] .searchable-select-wrapper .form-control {
    background-position: right 0.75rem center;
    padding-right: 2.5rem;
    padding-left: 0.75rem;
}

[dir="rtl"] .select-option:hover,
[dir="rtl"] .select-option.highlighted {
    transform: translateX(-2px);
}

/* حالة التحميل */
.searchable-select.loading {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'%3e%3ccircle cx='12' cy='12' r='10' stroke='%23007bff' stroke-width='4' fill='none' opacity='0.25'/%3e%3cpath fill='%23007bff' d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'/%3e%3c/svg%3e");
    background-size: 1rem 1rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .select-options {
        max-height: 200px;
        font-size: 14px;
    }
    
    .select-option {
        padding: 12px;
    }
    
    .select-search-input {
        font-size: 16px; /* منع التكبير في iOS */
        padding: 10px 12px;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .select-options {
        max-height: 150px;
        left: -5px;
        right: -5px;
        border-radius: 8px;
        border: 2px solid #007bff;
    }
    
    .select-option {
        padding: 15px 12px;
        font-size: 15px;
    }
}

/* تأثيرات إضافية */
.searchable-select-wrapper:hover .searchable-select {
    border-color: #007bff;
}

.select-option {
    position: relative;
    overflow: hidden;
}

.select-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.select-option:hover::before {
    left: 100%;
}

/* تحسينات الوصولية */
.searchable-select:focus-visible {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.select-search-input:focus-visible {
    outline: 2px solid #007bff;
    outline-offset: -2px;
}

/* أنماط للحالات المختلفة */
.searchable-select.is-valid {
    border-color: #28a745;
}

.searchable-select.is-invalid {
    border-color: #dc3545;
}

.searchable-select.is-valid:focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.searchable-select.is-invalid:focus {
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* تحسينات للطباعة */
@media print {
    .select-search-input,
    .select-options {
        display: none !important;
    }
    
    .searchable-select {
        background-image: none !important;
    }
}

/* تأثيرات الانتقال */
.select-options {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.searchable-select.open ~ .select-options {
    opacity: 1;
    transform: translateY(0);
}

.select-option {
    opacity: 0;
    animation: fadeInUp 0.3s ease forwards;
}

.select-option:nth-child(1) { animation-delay: 0.05s; }
.select-option:nth-child(2) { animation-delay: 0.1s; }
.select-option:nth-child(3) { animation-delay: 0.15s; }
.select-option:nth-child(4) { animation-delay: 0.2s; }
.select-option:nth-child(5) { animation-delay: 0.25s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
    .select-option {
        border-bottom: 2px solid #000;
    }
    
    .select-option.selected {
        background-color: #000;
        color: #fff;
    }
    
    .select-search-input:focus {
        border-color: #000;
        box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.5);
    }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    .searchable-select,
    .select-option,
    .select-options,
    .select-search-input {
        transition: none;
        animation: none;
    }
    
    .select-option::before {
        display: none;
    }
}
