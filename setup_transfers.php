<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد جداول التحويلات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { color: blue; background: #d1ecf1; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>إعداد جداول نظام التحويلات</h1>
    
    <?php
    require_once 'config/config.php';
    
    try {
        $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='step'>";
        echo "<h3>الخطوة 1: قراءة ملف SQL</h3>";
        
        $sqlFile = 'setup_transfers_tables.sql';
        if (!file_exists($sqlFile)) {
            echo "<div class='error'>ملف SQL غير موجود: $sqlFile</div>";
            exit;
        }
        
        $sql = file_get_contents($sqlFile);
        echo "<div class='success'>تم قراءة ملف SQL بنجاح</div>";
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h3>الخطوة 2: تنفيذ الاستعلامات</h3>";
        
        // تقسيم الاستعلامات
        $queries = explode(';', $sql);
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (empty($query) || strpos($query, '--') === 0) {
                continue;
            }
            
            try {
                $db->exec($query);
                $successCount++;
            } catch (PDOException $e) {
                echo "<div class='error'>خطأ في الاستعلام: " . $e->getMessage() . "</div>";
                $errorCount++;
            }
        }
        
        echo "<div class='success'>تم تنفيذ $successCount استعلام بنجاح</div>";
        if ($errorCount > 0) {
            echo "<div class='error'>فشل في تنفيذ $errorCount استعلام</div>";
        }
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h3>الخطوة 3: التحقق من الجداول</h3>";
        
        $tables = ['traders', 'drivers', 'container_transfers', 'transfer_containers', 'transfer_expenses', 'transfer_tracking'];
        
        foreach ($tables as $table) {
            $stmt = $db->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            if ($stmt->rowCount() > 0) {
                echo "<div class='success'>✓ الجدول $table موجود</div>";
            } else {
                echo "<div class='error'>✗ الجدول $table غير موجود</div>";
            }
        }
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h3>الخطوة 4: فحص البيانات</h3>";
        
        // فحص التجار
        try {
            $stmt = $db->query("SELECT COUNT(*) as count FROM traders");
            $count = $stmt->fetch()['count'];
            echo "<div class='info'>عدد التجار: $count</div>";
        } catch (Exception $e) {
            echo "<div class='error'>خطأ في جدول التجار: " . $e->getMessage() . "</div>";
        }
        
        // فحص السائقين
        try {
            $stmt = $db->query("SELECT COUNT(*) as count FROM drivers");
            $count = $stmt->fetch()['count'];
            echo "<div class='info'>عدد السائقين: $count</div>";
        } catch (Exception $e) {
            echo "<div class='error'>خطأ في جدول السائقين: " . $e->getMessage() . "</div>";
        }
        
        // فحص الحاويات المتاحة
        try {
            $stmt = $db->query("SELECT COUNT(*) as count FROM containers WHERE status = 'pending'");
            $count = $stmt->fetch()['count'];
            echo "<div class='info'>عدد الحاويات المتاحة للتحويل: $count</div>";
        } catch (Exception $e) {
            echo "<div class='error'>خطأ في جدول الحاويات: " . $e->getMessage() . "</div>";
        }
        
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h3>✅ تم إعداد نظام التحويلات بنجاح!</h3>";
        echo "<div class='success'>يمكنك الآن استخدام نظام التحويلات</div>";
        echo "<p><a href='index.php?page=transfers' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب إلى صفحة التحويلات</a></p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
    }
    ?>
</body>
</html>
