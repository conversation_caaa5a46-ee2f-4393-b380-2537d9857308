<?php
// التحقق من الصلاحيات
if (!hasPermission('financial')) {
    $_SESSION['error'] = 'ليس لديك صلاحية الوصول إلى هذه الصفحة';
    echo '<script>window.location.href = "index.php?page=unauthorized";</script>';
    return;
}

// الحصول على معرف التاجر والحاوية
$trader_id = isset($_GET['trader_id']) ? (int)$_GET['trader_id'] : 0;
$container_id = isset($_GET['container_id']) ? (int)$_GET['container_id'] : 0;
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-01');
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d');
$print_mode = isset($_GET['print']) && $_GET['print'] == '1';

if (!$trader_id) {
    $_SESSION['error'] = 'يجب تحديد التاجر لعرض كشف الحساب';
    echo '<script>window.location.href = "index.php?page=traders";</script>';
    return;
}

try {
    // الحصول على بيانات التاجر
    $stmt = $db->prepare("
        SELECT t.*, u.full_name as created_by_name 
        FROM traders t 
        LEFT JOIN users u ON t.created_by = u.id 
        WHERE t.id = ?
    ");
    $stmt->execute([$trader_id]);
    $trader = $stmt->fetch();
    
    if (!$trader) {
        $_SESSION['error'] = 'التاجر غير موجود';
        echo '<script>window.location.href = "index.php?page=traders";</script>';
        return;
    }
    
    // الحصول على جميع التجار للقائمة المنسدلة
    $traders_stmt = $db->query("SELECT id, name FROM traders WHERE active = 1 ORDER BY name");
    $all_traders = $traders_stmt->fetchAll();

    // الحصول على معلومات الحاوية المحددة إذا تم تحديدها
    $selected_container = null;
    if ($container_id > 0) {
        $container_stmt = $db->prepare("
            SELECT c.*, cu.name as customer_name
            FROM containers c
            LEFT JOIN customers cu ON c.customer_id = cu.id
            WHERE c.id = ? AND c.trader_id = ?
        ");
        $container_stmt->execute([$container_id, $trader_id]);
        $selected_container = $container_stmt->fetch();
    }

    // حساب الرصيد الافتتاحي (الدائن على التاجر)
    $opening_balance = (float)$trader['price'];
    
    // الحصول على الحاويات
    $containers_query = "
        SELECT c.*,
               (c.selling_price - c.purchase_price) as profit,
               DATE(c.created_at) as transaction_date,
               'container' as transaction_type
        FROM containers c
        WHERE c.trader_id = ?
        AND DATE(c.created_at) BETWEEN ? AND ?
    ";
    $containers_params = [$trader_id, $date_from, $date_to];

    // إضافة فلتر الحاوية إذا تم تحديدها
    if ($container_id > 0) {
        $containers_query .= " AND c.id = ?";
        $containers_params[] = $container_id;
    }

    $containers_query .= " ORDER BY c.created_at ASC";

    $containers_stmt = $db->prepare($containers_query);
    $containers_stmt->execute($containers_params);
    $containers = $containers_stmt->fetchAll();
    
    // الحصول على المستندات المالية
    $financial_query = "
        SELECT fd.*,
               DATE(fd.document_date) as transaction_date,
               'financial' as transaction_type,
               CASE
                   WHEN fd.document_type = 'receipt' THEN 'سند قبض'
                   WHEN fd.document_type = 'payment' THEN 'سند دفع'
                   WHEN fd.document_type = 'expense' THEN 'سند صرف'
                   WHEN fd.document_type = 'transfer' THEN 'حوالة مالية'
                   WHEN fd.document_type = 'capital' THEN 'رأس مال'
                   ELSE fd.document_type
               END as document_type_ar
        FROM financial_documents fd
        WHERE fd.customer_id = ?
        AND fd.document_date BETWEEN ? AND ?
    ";
    $financial_params = [$trader_id, $date_from, $date_to];

    // إضافة فلتر الحاوية إذا تم تحديدها
    if ($container_id > 0) {
        $financial_query .= " AND fd.container_id = ?";
        $financial_params[] = $container_id;
    }

    $financial_query .= " ORDER BY fd.document_date ASC, fd.created_at ASC";

    $financial_stmt = $db->prepare($financial_query);
    $financial_stmt->execute($financial_params);
    $financial_docs = $financial_stmt->fetchAll();
    
    // الحصول على التحويلات
    $transfers_query = "
        SELECT ct.*,
               DATE(ct.transfer_date) as transaction_date,
               'transfer' as transaction_type,
               d.driver_name,
               d.vehicle_number
        FROM container_transfers ct
        LEFT JOIN drivers d ON ct.driver_id = d.id
        WHERE ct.trader_id = ?
        AND ct.transfer_date BETWEEN ? AND ?
    ";
    $transfers_params = [$trader_id, $date_from, $date_to];

    // إضافة فلتر الحاوية إذا تم تحديدها
    if ($container_id > 0) {
        $transfers_query .= " AND ct.container_id = ?";
        $transfers_params[] = $container_id;
    }

    $transfers_query .= " ORDER BY ct.transfer_date ASC, ct.created_at ASC";

    $transfers_stmt = $db->prepare($transfers_query);
    $transfers_stmt->execute($transfers_params);
    $transfers = $transfers_stmt->fetchAll();
    
    // دمج جميع المعاملات وترتيبها حسب التاريخ
    $all_transactions = [];
    
    // إضافة الحاويات
    foreach ($containers as $container) {
        $all_transactions[] = [
            'date' => $container['transaction_date'],
            'type' => 'container',
            'description' => 'حاوية رقم: ' . $container['container_number'],
            'details' => $container['content'] ?: 'غير محدد',
            'debit' => $container['purchase_price'], // مدين (تكلفة الشراء)
            'credit' => $container['selling_price'], // دائن (سعر البيع)
            'reference' => $container['container_number'],
            'data' => $container
        ];
    }
    
    // إضافة المستندات المالية
    foreach ($financial_docs as $doc) {
        $debit = 0;
        $credit = 0;
        
        // تحديد المدين والدائن حسب نوع المستند
        switch ($doc['document_type']) {
            case 'receipt': // سند قبض - دائن للتاجر
                $credit = $doc['amount'];
                break;
            case 'payment': // سند دفع - مدين على التاجر
                $debit = $doc['amount'];
                break;
            case 'expense': // سند صرف - مدين على التاجر
                $debit = $doc['amount'];
                break;
            case 'transfer': // حوالة مالية
                $credit = $doc['amount'];
                break;
            case 'capital': // رأس مال
                $credit = $doc['amount'];
                break;
        }
        
        $all_transactions[] = [
            'date' => $doc['transaction_date'],
            'type' => 'financial',
            'description' => $doc['document_type_ar'] . ' رقم: ' . $doc['document_number'],
            'details' => $doc['description'] ?: 'غير محدد',
            'debit' => $debit,
            'credit' => $credit,
            'reference' => $doc['document_number'],
            'data' => $doc
        ];
    }
    
    // إضافة التحويلات
    foreach ($transfers as $transfer) {
        $all_transactions[] = [
            'date' => $transfer['transaction_date'],
            'type' => 'transfer',
            'description' => 'تحويل رقم: ' . $transfer['transfer_number'],
            'details' => 'السائق: ' . $transfer['driver_name'] . ' - عدد الحاويات: ' . $transfer['total_containers'],
            'debit' => $transfer['total_amount'], // مدين (تكلفة التحويل)
            'credit' => 0,
            'reference' => $transfer['transfer_number'],
            'data' => $transfer
        ];
    }
    
    // ترتيب المعاملات حسب التاريخ
    usort($all_transactions, function($a, $b) {
        return strtotime($a['date']) - strtotime($b['date']);
    });
    
    // حساب الأرصدة
    $running_balance = $opening_balance;
    $total_debit = 0;
    $total_credit = 0;
    
    foreach ($all_transactions as &$transaction) {
        $total_debit += $transaction['debit'];
        $total_credit += $transaction['credit'];
        $running_balance = $running_balance + $transaction['credit'] - $transaction['debit'];
        $transaction['balance'] = $running_balance;
    }
    
    $final_balance = $running_balance;
    
} catch (PDOException $e) {
    $_SESSION['error'] = 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage();
    echo '<script>window.location.href = "index.php?page=traders";</script>';
    return;
}
?>

<?php if ($print_mode): ?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>كشف حساب التاجر - <?php echo htmlspecialchars($trader['name']); ?></title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
            background: white;
        }
        .no-print { display: none !important; }

        .statement-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }

        .trader-info {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            border-right: 4px solid #007bff;
        }

        .balance-summary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
        }

        .transaction-table {
            font-size: 0.9rem;
        }

        .transaction-table th {
            background: #f8f9fa;
            font-weight: 600;
            border-top: 2px solid #007bff;
        }

        .transaction-row:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        .debit-amount {
            color: #dc3545;
            font-weight: 600;
        }

        .credit-amount {
            color: #28a745;
            font-weight: 600;
        }

        .balance-amount {
            font-weight: 600;
            color: #495057;
        }

        .transaction-type-container {
            background: #e3f2fd;
            color: #1976d2;
        }

        .transaction-type-financial {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .transaction-type-transfer {
            background: #e8f5e8;
            color: #388e3c;
        }

        @media print {
            .no-print { display: none !important; }
            body { font-size: 11px; }
            .statement-header { background: #007bff !important; }
            .balance-summary { background: #28a745 !important; }
        }
    </style>
</head>
<body>
<?php else: ?>
<!-- Non-print mode: use regular page structure -->
<?php endif; ?>

<!-- Content starts here -->
<div class="<?php echo $print_mode ? '' : 'content'; ?>">
    <div class="<?php echo $print_mode ? '' : 'container-fluid'; ?>">

        <!-- Header -->
        <div class="statement-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-file-invoice-dollar me-3"></i>
                        كشف حساب التاجر
                        <?php if ($selected_container): ?>
                        <small class="d-block mt-1" style="font-size: 0.6em;">
                            <i class="fas fa-shipping-fast me-1"></i>
                            الحاوية: <?php echo htmlspecialchars($selected_container['container_number']); ?>
                        </small>
                        <?php endif; ?>
                    </h1>
                    <p class="mb-0 opacity-75">
                        <?php if ($selected_container): ?>
                            تقرير مفصل للمعاملات المرتبطة بالحاوية المحددة
                        <?php else: ?>
                            تقرير شامل لجميع المعاملات المالية
                        <?php endif; ?>
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="text-white-50">
                        <small>تاريخ الطباعة: <?php echo date('Y-m-d H:i'); ?></small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters (Non-print only) -->
        <?php if (!$print_mode): ?>
        <div class="card mb-4 no-print">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>تصفية البيانات
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <input type="hidden" name="page" value="trader_statement">
                    
                    <div class="col-md-4">
                        <label class="form-label">التاجر</label>
                        <select name="trader_id" class="form-select" required>
                            <option value="">اختر التاجر</option>
                            <?php foreach ($all_traders as $t): ?>
                            <option value="<?php echo $t['id']; ?>" <?php echo $t['id'] == $trader_id ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($t['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" class="form-control" value="<?php echo $date_from; ?>">
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" class="form-control" value="<?php echo $date_to; ?>">
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>عرض
                            </button>
                            <a href="?page=trader_statement&trader_id=<?php echo $trader_id; ?>&date_from=<?php echo $date_from; ?>&date_to=<?php echo $date_to; ?>&print=1" 
                               target="_blank" class="btn btn-success btn-sm">
                                <i class="fas fa-print me-1"></i>طباعة
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <?php endif; ?>

        <!-- Trader Information -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="trader-info">
                    <h4 class="text-primary mb-3">
                        <i class="fas fa-user-tie me-2"></i>
                        بيانات التاجر
                    </h4>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>الاسم:</strong> <?php echo htmlspecialchars($trader['name']); ?></p>
                            <p><strong>الهاتف:</strong> <?php echo htmlspecialchars($trader['phone'] ?: 'غير محدد'); ?></p>
                            <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($trader['email'] ?: 'غير محدد'); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>الشخص المسؤول:</strong> <?php echo htmlspecialchars($trader['contact_person'] ?: 'غير محدد'); ?></p>
                            <p><strong>العنوان:</strong> <?php echo htmlspecialchars($trader['address'] ?: 'غير محدد'); ?></p>
                            <p><strong>الرقم الضريبي:</strong> <?php echo htmlspecialchars($trader['tax_number'] ?: 'غير محدد'); ?></p>
                        </div>
                    </div>
                </div>

                <?php if ($selected_container): ?>
                <div class="container-info mt-4">
                    <h5 class="text-info mb-3">
                        <i class="fas fa-shipping-fast me-2"></i>
                        معلومات الحاوية المحددة
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>رقم الحاوية:</strong> <?php echo htmlspecialchars($selected_container['container_number']); ?></p>
                            <p><strong>الشركة:</strong> <?php echo htmlspecialchars($selected_container['customer_name'] ?: 'غير محدد'); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>سعر الشراء:</strong> <?php echo number_format($selected_container['purchase_price'], 0); ?> د.ع</p>
                            <p><strong>سعر البيع:</strong> <?php echo number_format($selected_container['selling_price'], 0); ?> د.ع</p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <div class="col-md-4">
                <div class="balance-summary">
                    <h5 class="mb-3">
                        <i class="fas fa-balance-scale me-2"></i>
                        ملخص الحساب
                    </h5>
                    <div class="mb-2">
                        <small>الرصيد الافتتاحي</small>
                        <h4><?php echo number_format($opening_balance, 0); ?> د.ع</h4>
                    </div>
                    <hr class="bg-white opacity-25">
                    <div class="mb-2">
                        <small>الرصيد النهائي</small>
                        <h3 class="<?php echo $final_balance >= 0 ? 'text-success' : 'text-danger'; ?>">
                            <?php echo number_format($final_balance, 0); ?> د.ع
                        </h3>
                    </div>
                    <small class="opacity-75">
                        الفترة: <?php echo $date_from; ?> إلى <?php echo $date_to; ?>
                    </small>
                </div>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="text-primary mb-2">
                            <i class="fas fa-arrow-up fa-2x"></i>
                        </div>
                        <h4 class="text-primary"><?php echo number_format($total_credit, 0); ?></h4>
                        <small class="text-muted">إجمالي الدائن</small>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="text-danger mb-2">
                            <i class="fas fa-arrow-down fa-2x"></i>
                        </div>
                        <h4 class="text-danger"><?php echo number_format($total_debit, 0); ?></h4>
                        <small class="text-muted">إجمالي المدين</small>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="text-info mb-2">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                        <h4 class="text-info"><?php echo count($containers); ?></h4>
                        <small class="text-muted">عدد الحاويات</small>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="text-success mb-2">
                            <i class="fas fa-truck fa-2x"></i>
                        </div>
                        <h4 class="text-success"><?php echo count($transfers); ?></h4>
                        <small class="text-muted">عدد التحويلات</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transactions Table -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>
                    تفاصيل المعاملات
                    <span class="badge bg-primary ms-2"><?php echo count($all_transactions); ?></span>
                </h5>
            </div>

            <div class="card-body p-0">
                <?php if (empty($all_transactions)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد معاملات في الفترة المحددة</h5>
                    <p class="text-muted">جرب تغيير الفترة الزمنية أو التاجر</p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover transaction-table mb-0">
                        <thead>
                            <tr>
                                <th width="10%">التاريخ</th>
                                <th width="15%">نوع المعاملة</th>
                                <th width="25%">الوصف</th>
                                <th width="20%">التفاصيل</th>
                                <th width="10%" class="text-center">مدين</th>
                                <th width="10%" class="text-center">دائن</th>
                                <th width="10%" class="text-center">الرصيد</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Opening Balance Row -->
                            <tr class="table-info">
                                <td><?php echo $date_from; ?></td>
                                <td>
                                    <span class="badge bg-info">
                                        <i class="fas fa-balance-scale me-1"></i>
                                        رصيد افتتاحي
                                    </span>
                                </td>
                                <td>الرصيد المرحل من الفترات السابقة</td>
                                <td>-</td>
                                <td class="text-center">-</td>
                                <td class="text-center">-</td>
                                <td class="text-center balance-amount">
                                    <?php echo number_format($opening_balance, 0); ?>
                                </td>
                            </tr>

                            <?php foreach ($all_transactions as $transaction): ?>
                            <tr class="transaction-row transaction-type-<?php echo $transaction['type']; ?>">
                                <td><?php echo $transaction['date']; ?></td>
                                <td>
                                    <?php
                                    $type_info = [
                                        'container' => ['icon' => 'fas fa-box', 'text' => 'حاوية', 'class' => 'bg-primary'],
                                        'financial' => ['icon' => 'fas fa-file-invoice-dollar', 'text' => 'مستند مالي', 'class' => 'bg-purple'],
                                        'transfer' => ['icon' => 'fas fa-truck', 'text' => 'تحويل', 'class' => 'bg-success']
                                    ];
                                    $info = $type_info[$transaction['type']];
                                    ?>
                                    <span class="badge <?php echo $info['class']; ?>">
                                        <i class="<?php echo $info['icon']; ?> me-1"></i>
                                        <?php echo $info['text']; ?>
                                    </span>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($transaction['description']); ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        المرجع: <?php echo htmlspecialchars($transaction['reference']); ?>
                                    </small>
                                </td>
                                <td>
                                    <small><?php echo htmlspecialchars($transaction['details']); ?></small>
                                </td>
                                <td class="text-center">
                                    <?php if ($transaction['debit'] > 0): ?>
                                    <span class="debit-amount">
                                        <?php echo number_format($transaction['debit'], 0); ?>
                                    </span>
                                    <?php else: ?>
                                    -
                                    <?php endif; ?>
                                </td>
                                <td class="text-center">
                                    <?php if ($transaction['credit'] > 0): ?>
                                    <span class="credit-amount">
                                        <?php echo number_format($transaction['credit'], 0); ?>
                                    </span>
                                    <?php else: ?>
                                    -
                                    <?php endif; ?>
                                </td>
                                <td class="text-center balance-amount">
                                    <?php echo number_format($transaction['balance'], 0); ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>

                        <!-- Summary Row -->
                        <tfoot class="table-dark">
                            <tr>
                                <th colspan="4" class="text-center">الإجمالي</th>
                                <th class="text-center debit-amount">
                                    <?php echo number_format($total_debit, 0); ?>
                                </th>
                                <th class="text-center credit-amount">
                                    <?php echo number_format($total_credit, 0); ?>
                                </th>
                                <th class="text-center">
                                    <span class="<?php echo $final_balance >= 0 ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo number_format($final_balance, 0); ?>
                                    </span>
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Footer Information -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card border-0 bg-light">
                    <div class="card-body">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات إضافية
                        </h6>
                        <ul class="list-unstyled mb-0">
                            <li><small><strong>تاريخ الإنشاء:</strong> <?php echo convertDateFromMysql($trader['created_at'], true); ?></small></li>
                            <li><small><strong>آخر تحديث:</strong> <?php echo convertDateFromMysql($trader['updated_at'], true); ?></small></li>
                            <li><small><strong>أنشئ بواسطة:</strong> <?php echo htmlspecialchars($trader['created_by_name'] ?: 'غير محدد'); ?></small></li>
                            <?php if ($trader['notes']): ?>
                            <li><small><strong>ملاحظات:</strong> <?php echo htmlspecialchars($trader['notes']); ?></small></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card border-0 bg-light">
                    <div class="card-body">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-calculator me-2"></i>
                            ملخص الحسابات
                        </h6>
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="text-success mb-1"><?php echo number_format($total_credit, 0); ?></h6>
                                    <small class="text-muted">دائن</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="text-danger mb-1"><?php echo number_format($total_debit, 0); ?></h6>
                                    <small class="text-muted">مدين</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <h6 class="<?php echo $final_balance >= 0 ? 'text-success' : 'text-danger'; ?> mb-1">
                                    <?php echo number_format($final_balance, 0); ?>
                                </h6>
                                <small class="text-muted">الرصيد</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<?php if ($print_mode): ?>
<script>
// Auto print if in print mode
window.onload = function() {
    setTimeout(function() {
        window.print();
    }, 1000);
};
</script>

</body>
</html>
<?php endif; ?>
