<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار AJAX Endpoint للمستندات المالية</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { color: blue; background: #d1ecf1; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .json-output { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>اختبار AJAX Endpoint للمستندات المالية</h1>
    
    <?php
    require_once 'config/config.php';
    
    try {
        $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='test-section'>";
        echo "<h3>فحص التجار المتاحين</h3>";
        
        $stmt = $db->query("SELECT id, name FROM traders ORDER BY name LIMIT 5");
        $traders = $stmt->fetchAll();
        
        if (count($traders) > 0) {
            echo "<div class='success'>✓ تم العثور على " . count($traders) . " تجار</div>";
            echo "<ul>";
            foreach ($traders as $trader) {
                echo "<li>ID: " . $trader['id'] . " - " . htmlspecialchars($trader['name']) . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<div class='error'>لا توجد تجار في النظام</div>";
        }
        echo "</div>";
        
        if (count($traders) > 0) {
            $testTraderId = $traders[0]['id'];
            
            echo "<div class='test-section'>";
            echo "<h3>اختبار AJAX Endpoint</h3>";
            echo "<div class='info'>سيتم اختبار الـ endpoint مع التاجر: " . htmlspecialchars($traders[0]['name']) . " (ID: $testTraderId)</div>";
            
            echo "<button class='btn' onclick='testEndpoint($testTraderId)'>اختبار الـ Endpoint</button>";
            echo "<button class='btn' onclick='testWithFilters($testTraderId)'>اختبار مع فلاتر</button>";
            echo "<button class='btn' onclick='testInvalidId()'>اختبار ID غير صحيح</button>";
            
            echo "<div id='testResult'></div>";
            echo "</div>";
            
            echo "<div class='test-section'>";
            echo "<h3>اختبار مباشر للـ Endpoint</h3>";
            
            $testUrls = [
                "ajax/get_financial_containers.php?customer_id=$testTraderId" => "اختبار أساسي",
                "ajax/get_financial_containers.php?customer_id=$testTraderId&search=CONT" => "اختبار مع بحث",
                "ajax/get_financial_containers.php?customer_id=$testTraderId&status_filter=pending" => "اختبار مع فلتر الحالة",
                "ajax/get_financial_containers.php?customer_id=999" => "اختبار ID غير موجود",
                "ajax/get_financial_containers.php" => "اختبار بدون معاملات"
            ];
            
            foreach ($testUrls as $url => $description) {
                echo "<p>";
                echo "<strong>$description:</strong><br>";
                echo "<a href='$url' target='_blank' style='color: #007bff;'>$url</a>";
                echo "</p>";
            }
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
    }
    ?>
    
    <div class="test-section">
        <h3>فحص ملف AJAX</h3>
        <?php
        $ajaxFile = 'ajax/get_financial_containers.php';
        if (file_exists($ajaxFile)) {
            echo "<div class='success'>✓ ملف AJAX موجود: $ajaxFile</div>";
            echo "<div class='info'>حجم الملف: " . number_format(filesize($ajaxFile)) . " بايت</div>";
        } else {
            echo "<div class='error'>✗ ملف AJAX غير موجود: $ajaxFile</div>";
        }
        
        $configFile = 'config/config.php';
        if (file_exists($configFile)) {
            echo "<div class='success'>✓ ملف الإعدادات موجود: $configFile</div>";
        } else {
            echo "<div class='error'>✗ ملف الإعدادات غير موجود: $configFile</div>";
        }
        ?>
    </div>
    
    <script>
    function testEndpoint(traderId) {
        const resultDiv = document.getElementById('testResult');
        resultDiv.innerHTML = '<div style="color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px;">جاري اختبار الـ Endpoint...</div>';
        
        const url = `ajax/get_financial_containers.php?customer_id=${traderId}`;
        
        fetch(url)
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                
                let html = '';
                if (data.success) {
                    html = `<div style="color: green; background: #d4edda; padding: 10px; border-radius: 5px;">
                        ✅ نجح الاختبار!<br>
                        <strong>النتائج:</strong><br>
                        - معرف التاجر: ${data.trader_id}<br>
                        - عدد الحاويات: ${data.count}<br>
                        - الفلاتر المطبقة: ${JSON.stringify(data.filters)}<br>
                    </div>`;
                    
                    if (data.containers && data.containers.length > 0) {
                        html += '<div class="json-output">';
                        html += '<strong>أول 3 حاويات:</strong>\n';
                        html += JSON.stringify(data.containers.slice(0, 3), null, 2);
                        html += '</div>';
                    }
                } else {
                    html = `<div style="color: red; background: #f8d7da; padding: 10px; border-radius: 5px;">
                        ❌ فشل الاختبار!<br>
                        <strong>الخطأ:</strong> ${data.error}
                    </div>`;
                }
                
                html += '<div class="json-output"><strong>الاستجابة الكاملة:</strong>\n' + JSON.stringify(data, null, 2) + '</div>';
                resultDiv.innerHTML = html;
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = `<div style="color: red; background: #f8d7da; padding: 10px; border-radius: 5px;">
                    ❌ خطأ في الاتصال: ${error.message}
                </div>`;
            });
    }
    
    function testWithFilters(traderId) {
        const resultDiv = document.getElementById('testResult');
        resultDiv.innerHTML = '<div style="color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px;">جاري اختبار الـ Endpoint مع فلاتر...</div>';
        
        const url = `ajax/get_financial_containers.php?customer_id=${traderId}&search=CONT&status_filter=pending`;
        
        fetch(url)
            .then(response => response.json())
            .then(data => {
                console.log('Response with filters:', data);
                
                let html = '';
                if (data.success) {
                    html = `<div style="color: green; background: #d4edda; padding: 10px; border-radius: 5px;">
                        ✅ نجح اختبار الفلاتر!<br>
                        <strong>النتائج:</strong><br>
                        - عدد الحاويات المفلترة: ${data.count}<br>
                        - الفلاتر المطبقة: ${JSON.stringify(data.filters)}<br>
                    </div>`;
                } else {
                    html = `<div style="color: red; background: #f8d7da; padding: 10px; border-radius: 5px;">
                        ❌ فشل اختبار الفلاتر!<br>
                        <strong>الخطأ:</strong> ${data.error}
                    </div>`;
                }
                
                html += '<div class="json-output"><strong>الاستجابة:</strong>\n' + JSON.stringify(data, null, 2) + '</div>';
                resultDiv.innerHTML = html;
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = `<div style="color: red; background: #f8d7da; padding: 10px; border-radius: 5px;">
                    ❌ خطأ في الاتصال: ${error.message}
                </div>`;
            });
    }
    
    function testInvalidId() {
        const resultDiv = document.getElementById('testResult');
        resultDiv.innerHTML = '<div style="color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px;">جاري اختبار ID غير صحيح...</div>';
        
        const url = 'ajax/get_financial_containers.php?customer_id=999999';
        
        fetch(url)
            .then(response => response.json())
            .then(data => {
                console.log('Response with invalid ID:', data);
                
                let html = '';
                if (data.success) {
                    html = `<div style="color: orange; background: #fff3cd; padding: 10px; border-radius: 5px;">
                        ⚠️ الاختبار نجح لكن لم يتم العثور على حاويات<br>
                        <strong>النتائج:</strong><br>
                        - عدد الحاويات: ${data.count}
                    </div>`;
                } else {
                    html = `<div style="color: green; background: #d4edda; padding: 10px; border-radius: 5px;">
                        ✅ تم التعامل مع ID غير صحيح بشكل صحيح<br>
                        <strong>الخطأ:</strong> ${data.error}
                    </div>`;
                }
                
                html += '<div class="json-output"><strong>الاستجابة:</strong>\n' + JSON.stringify(data, null, 2) + '</div>';
                resultDiv.innerHTML = html;
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = `<div style="color: red; background: #f8d7da; padding: 10px; border-radius: 5px;">
                    ❌ خطأ في الاتصال: ${error.message}
                </div>`;
            });
    }
    </script>
</body>
</html>
