<?php
// التحقق من الصلاحيات
if (!hasPermission('transfers')) {
    header('Location: index.php?page=unauthorized');
    exit;
}

// معاملات التقرير
$dateFrom = $_GET['date_from'] ?? date('Y-m-01'); // بداية الشهر الحالي
$dateTo = $_GET['date_to'] ?? date('Y-m-d'); // اليوم الحالي
$status = $_GET['status'] ?? '';
$traderId = $_GET['trader_id'] ?? '';

try {
    // بناء الاستعلام
    $whereConditions = [];
    $params = [];
    
    // فلتر التاريخ
    if ($dateFrom) {
        $whereConditions[] = "ct.transfer_date >= ?";
        $params[] = $dateFrom;
    }
    
    if ($dateTo) {
        $whereConditions[] = "ct.transfer_date <= ?";
        $params[] = $dateTo;
    }
    
    // فلتر الحالة
    if ($status) {
        $whereConditions[] = "ct.status = ?";
        $params[] = $status;
    }
    
    // فلتر التاجر
    if ($traderId) {
        $whereConditions[] = "ct.trader_id = ?";
        $params[] = $traderId;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // جلب التحويلات
    $transfersStmt = $db->prepare("
        SELECT 
            ct.*,
            t.name as trader_name,
            t.phone as trader_phone,
            d.driver_name,
            d.phone as driver_phone,
            d.vehicle_number,
            (SELECT COUNT(*) FROM transfer_containers tc WHERE tc.transfer_id = ct.id) as actual_containers
        FROM container_transfers ct
        LEFT JOIN traders t ON ct.trader_id = t.id
        LEFT JOIN drivers d ON ct.driver_id = d.id
        $whereClause
        ORDER BY ct.transfer_date DESC, ct.created_at DESC
    ");
    $transfersStmt->execute($params);
    $transfers = $transfersStmt->fetchAll();
    
    // حساب الإحصائيات
    $totalTransfers = count($transfers);
    $totalAmount = array_sum(array_column($transfers, 'total_amount'));
    $totalContainers = array_sum(array_column($transfers, 'actual_containers'));
    
    // إحصائيات الحالات
    $statusStats = [];
    foreach ($transfers as $transfer) {
        $status = $transfer['status'];
        if (!isset($statusStats[$status])) {
            $statusStats[$status] = ['count' => 0, 'amount' => 0];
        }
        $statusStats[$status]['count']++;
        $statusStats[$status]['amount'] += $transfer['total_amount'];
    }
    
    // جلب قائمة التجار للفلتر
    $tradersStmt = $db->query("SELECT id, name FROM traders ORDER BY name");
    $traders = $tradersStmt->fetchAll();
    
} catch (PDOException $e) {
    $_SESSION['error'] = 'حدث خطأ أثناء جلب البيانات: ' . $e->getMessage();
    header('Location: index.php?page=transfers');
    exit;
}

$statusLabels = [
    'pending' => 'قيد الانتظار',
    'in_progress' => 'قيد التنفيذ',
    'completed' => 'مكتمل',
    'cancelled' => 'ملغى'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير التحويلات</title>
    
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: #fff;
        }
        
        .no-print {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }
        
        .no-print button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 5px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 24px;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .header h2 {
            font-size: 18px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .report-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            border: 1px solid #dee2e6;
        }
        
        .report-info h3 {
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        
        .info-value {
            color: #212529;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .transfers-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            font-size: 11px;
        }
        
        .transfers-table th,
        .transfers-table td {
            border: 1px solid #dee2e6;
            padding: 8px 6px;
            text-align: right;
        }
        
        .transfers-table th {
            background: #007bff;
            color: white;
            font-weight: bold;
        }
        
        .transfers-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-in_progress { background: #d1ecf1; color: #0c5460; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 30px;
        }
        
        .summary-table th,
        .summary-table td {
            border: 1px solid #333;
            padding: 10px;
            text-align: right;
        }
        
        .summary-table th {
            background: #f5f5f5;
            font-weight: bold;
        }
        
        .total-row {
            background: #e3f2fd;
            font-weight: bold;
            color: #1976d2;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 11px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
        }
        
        @media print {
            .no-print { display: none !important; }
            body { font-size: 10px; }
            .container { max-width: none; padding: 10px; }
            @page { size: A4 landscape; margin: 10mm; }
            .transfers-table { font-size: 9px; }
            .transfers-table th, .transfers-table td { padding: 4px 3px; }
        }
        
        @media (max-width: 768px) {
            .stats-grid { grid-template-columns: 1fr 1fr; }
            .info-grid { grid-template-columns: 1fr; }
            .transfers-table { font-size: 10px; }
        }
    </style>
</head>

<body>
    <div class="no-print">
        <button onclick="window.print()">طباعة</button>
        <button onclick="window.close()">إغلاق</button>
    </div>

    <div class="container">
        <!-- رأس التقرير -->
        <div class="header">
            <h1><?php echo COMPANY_NAME; ?></h1>
            <h2>تقرير التحويلات</h2>
            <p>تاريخ الطباعة: <?php echo date('Y-m-d H:i'); ?></p>
        </div>

        <!-- معلومات التقرير -->
        <div class="report-info">
            <h3>معايير التقرير</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">من تاريخ:</span>
                    <span class="info-value"><?php echo $dateFrom; ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">إلى تاريخ:</span>
                    <span class="info-value"><?php echo $dateTo; ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">الحالة:</span>
                    <span class="info-value"><?php echo $status ? ($statusLabels[$status] ?? $status) : 'جميع الحالات'; ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">التاجر:</span>
                    <span class="info-value">
                        <?php 
                        if ($traderId) {
                            $selectedTrader = array_filter($traders, fn($t) => $t['id'] == $traderId);
                            echo $selectedTrader ? reset($selectedTrader)['name'] : 'غير محدد';
                        } else {
                            echo 'جميع التجار';
                        }
                        ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- الإحصائيات -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $totalTransfers; ?></div>
                <div class="stat-label">إجمالي التحويلات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $totalContainers; ?></div>
                <div class="stat-label">إجمالي الحاويات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($totalAmount, 0); ?></div>
                <div class="stat-label">إجمالي المبلغ (د.ع)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $totalContainers > 0 ? number_format($totalAmount / $totalContainers, 0) : 0; ?></div>
                <div class="stat-label">متوسط قيمة الحاوية (د.ع)</div>
            </div>
        </div>

        <!-- جدول التحويلات -->
        <?php if (!empty($transfers)): ?>
        <table class="transfers-table">
            <thead>
                <tr>
                    <th width="8%">رقم التحويل</th>
                    <th width="10%">التاريخ</th>
                    <th width="15%">التاجر</th>
                    <th width="15%">السائق</th>
                    <th width="8%">عدد الحاويات</th>
                    <th width="12%">المبلغ (د.ع)</th>
                    <th width="10%">الحالة</th>
                    <th width="12%">موقع الاستلام</th>
                    <th width="10%">وقت الإنشاء</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($transfers as $transfer): ?>
                <tr>
                    <td><strong><?php echo htmlspecialchars($transfer['transfer_number']); ?></strong></td>
                    <td><?php echo date('Y-m-d', strtotime($transfer['transfer_date'])); ?></td>
                    <td><?php echo htmlspecialchars($transfer['trader_name'] ?? 'غير محدد'); ?></td>
                    <td>
                        <?php echo htmlspecialchars($transfer['driver_name'] ?? 'غير محدد'); ?>
                        <?php if ($transfer['vehicle_number']): ?>
                        <br><small><?php echo htmlspecialchars($transfer['vehicle_number']); ?></small>
                        <?php endif; ?>
                    </td>
                    <td class="text-center"><?php echo $transfer['actual_containers']; ?></td>
                    <td class="text-left"><?php echo number_format($transfer['total_amount'], 0); ?></td>
                    <td>
                        <span class="status-badge status-<?php echo $transfer['status']; ?>">
                            <?php echo $statusLabels[$transfer['status']] ?? $transfer['status']; ?>
                        </span>
                    </td>
                    <td><?php echo htmlspecialchars($transfer['pickup_location']); ?></td>
                    <td><?php echo date('H:i', strtotime($transfer['created_at'])); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <div style="text-align: center; padding: 40px; color: #666;">
            <h3>لا توجد تحويلات تطابق المعايير المحددة</h3>
        </div>
        <?php endif; ?>

        <!-- ملخص الحالات -->
        <?php if (!empty($statusStats)): ?>
        <table class="summary-table">
            <thead>
                <tr>
                    <th>الحالة</th>
                    <th>عدد التحويلات</th>
                    <th>المبلغ الإجمالي (د.ع)</th>
                    <th>النسبة المئوية</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($statusStats as $status => $stats): ?>
                <tr>
                    <td>
                        <span class="status-badge status-<?php echo $status; ?>">
                            <?php echo $statusLabels[$status] ?? $status; ?>
                        </span>
                    </td>
                    <td><?php echo $stats['count']; ?></td>
                    <td><?php echo number_format($stats['amount'], 0); ?></td>
                    <td><?php echo $totalTransfers > 0 ? number_format(($stats['count'] / $totalTransfers) * 100, 1) : 0; ?>%</td>
                </tr>
                <?php endforeach; ?>
                
                <tr class="total-row">
                    <td><strong>الإجمالي</strong></td>
                    <td><strong><?php echo $totalTransfers; ?></strong></td>
                    <td><strong><?php echo number_format($totalAmount, 0); ?></strong></td>
                    <td><strong>100%</strong></td>
                </tr>
            </tbody>
        </table>
        <?php endif; ?>

        <!-- التذييل -->
        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام <?php echo APP_NAME; ?> - <?php echo date('Y-m-d H:i:s'); ?></p>
            <p>عدد السجلات: <?php echo $totalTransfers; ?> | إجمالي القيمة: <?php echo number_format($totalAmount, 0); ?> د.ع</p>
        </div>
    </div>

    <script>
        // طباعة تلقائية
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('auto_print') === '1') {
            window.onload = function() {
                setTimeout(() => {
                    window.print();
                }, 1000);
            };
        }
        
        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            if (e.key === 'Escape') {
                window.close();
            }
        });
    </script>
</body>
</html>
