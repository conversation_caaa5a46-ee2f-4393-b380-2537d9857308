<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح شامل لنظام التحويلات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { color: blue; background: #d1ecf1; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .warning { color: orange; background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .progress { background: #e9ecef; border-radius: 5px; height: 20px; margin: 10px 0; }
        .progress-bar { background: #007bff; height: 100%; border-radius: 5px; transition: width 0.3s; }
    </style>
</head>
<body>
    <h1>إصلاح شامل لنظام التحويلات</h1>
    <div class="info">هذا الملف سيقوم بإصلاح جميع المشاكل في نظام التحويلات</div>
    
    <?php
    require_once 'config/config.php';
    
    $totalSteps = 6;
    $currentStep = 0;
    
    function updateProgress($step, $total) {
        $percentage = ($step / $total) * 100;
        echo "<div class='progress'><div class='progress-bar' style='width: {$percentage}%'></div></div>";
        echo "<div class='info'>التقدم: $step من $total خطوات مكتملة</div>";
    }
    
    try {
        $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // الخطوة 1: إنشاء جدول التجار
        echo "<div class='step'>";
        echo "<h3>الخطوة 1: إنشاء جدول التجار</h3>";
        
        try {
            $createTradersSQL = "
            CREATE TABLE IF NOT EXISTS `traders` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `name` varchar(100) NOT NULL,
              `contact_person` varchar(100) DEFAULT NULL,
              `phone` varchar(20) DEFAULT NULL,
              `email` varchar(100) DEFAULT NULL,
              `address` text DEFAULT NULL,
              `tax_number` varchar(50) DEFAULT NULL,
              `notes` text DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";
            
            $db->exec($createTradersSQL);
            echo "<div class='success'>✓ تم إنشاء جدول traders</div>";
            
            // نسخ البيانات من customers إذا كان فارغاً
            $stmt = $db->query("SELECT COUNT(*) as count FROM traders");
            $traderCount = $stmt->fetch()['count'];
            
            if ($traderCount == 0) {
                $copySQL = "
                INSERT INTO traders (name, contact_person, phone, email, address, tax_number, notes)
                SELECT name, contact_person, phone, email, address, tax_number, notes
                FROM customers
                ";
                
                $stmt = $db->prepare($copySQL);
                $stmt->execute();
                $copiedCount = $stmt->rowCount();
                echo "<div class='success'>✓ تم نسخ $copiedCount سجل من customers</div>";
            }
            
        } catch (PDOException $e) {
            echo "<div class='error'>خطأ: " . $e->getMessage() . "</div>";
        }
        
        $currentStep++;
        updateProgress($currentStep, $totalSteps);
        echo "</div>";
        
        // الخطوة 2: إنشاء جدول السائقين
        echo "<div class='step'>";
        echo "<h3>الخطوة 2: إنشاء جدول السائقين</h3>";
        
        try {
            $createDriversSQL = "
            CREATE TABLE IF NOT EXISTS `drivers` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `driver_name` varchar(100) NOT NULL,
              `license_number` varchar(50) NOT NULL,
              `phone` varchar(20) NOT NULL,
              `national_id` varchar(20) DEFAULT NULL,
              `address` text DEFAULT NULL,
              `vehicle_type` varchar(50) DEFAULT NULL,
              `vehicle_number` varchar(20) DEFAULT NULL,
              `license_expiry` date DEFAULT NULL,
              `status` enum('active','inactive','suspended') DEFAULT 'active',
              `daily_rate` decimal(10,2) DEFAULT 0.00,
              `per_container_rate` decimal(10,2) DEFAULT 0.00,
              `notes` text DEFAULT NULL,
              `created_by` int(11) NOT NULL DEFAULT 1,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `license_number` (`license_number`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";
            
            $db->exec($createDriversSQL);
            echo "<div class='success'>✓ تم إنشاء جدول drivers</div>";
            
            // إضافة سائقين تجريبيين
            $stmt = $db->query("SELECT COUNT(*) as count FROM drivers");
            $driverCount = $stmt->fetch()['count'];
            
            if ($driverCount == 0) {
                $testDrivers = [
                    ['أحمد محمد علي', 'DL001234567', '07901234567', '19850101001', 'بغداد - الكرادة', 'شاحنة كبيرة', '123-بغداد', 150000.00, 25000.00],
                    ['عمر حسن جاسم', 'DL002345678', '07912345678', '19900215002', 'البصرة - المعقل', 'شاحنة متوسطة', '456-بصرة', 120000.00, 20000.00],
                    ['محمد عبدالله يوسف', 'DL003456789', '07923456789', '19880310003', 'أربيل - عنكاوا', 'شاحنة صغيرة', '789-أربيل', 100000.00, 15000.00]
                ];
                
                foreach ($testDrivers as $driver) {
                    $stmt = $db->prepare("
                        INSERT IGNORE INTO drivers 
                        (driver_name, license_number, phone, national_id, address, vehicle_type, vehicle_number, daily_rate, per_container_rate) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute($driver);
                }
                echo "<div class='success'>✓ تم إضافة " . count($testDrivers) . " سائق تجريبي</div>";
            }
            
        } catch (PDOException $e) {
            echo "<div class='error'>خطأ: " . $e->getMessage() . "</div>";
        }
        
        $currentStep++;
        updateProgress($currentStep, $totalSteps);
        echo "</div>";
        
        // الخطوة 3: إنشاء جدول التحويلات
        echo "<div class='step'>";
        echo "<h3>الخطوة 3: إنشاء جدول التحويلات</h3>";
        
        try {
            $createTransfersSQL = "
            CREATE TABLE IF NOT EXISTS `container_transfers` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `transfer_number` varchar(50) NOT NULL,
              `transfer_date` date NOT NULL,
              `trader_id` int(11) NOT NULL,
              `driver_id` int(11) NOT NULL,
              `pickup_location` varchar(255) NOT NULL DEFAULT 'المستودع الرئيسي',
              `delivery_location` varchar(255) NOT NULL DEFAULT 'موقع التسليم',
              `total_containers` int(11) NOT NULL DEFAULT 0,
              `total_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
              `currency` varchar(10) DEFAULT 'IQD',
              `status` enum('pending','in_progress','completed','cancelled') DEFAULT 'pending',
              `pickup_time` datetime DEFAULT NULL,
              `delivery_time` datetime DEFAULT NULL,
              `notes` text DEFAULT NULL,
              `created_by` int(11) NOT NULL DEFAULT 1,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `transfer_number` (`transfer_number`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";
            
            $db->exec($createTransfersSQL);
            echo "<div class='success'>✓ تم إنشاء جدول container_transfers</div>";
            
        } catch (PDOException $e) {
            echo "<div class='error'>خطأ: " . $e->getMessage() . "</div>";
        }
        
        $currentStep++;
        updateProgress($currentStep, $totalSteps);
        echo "</div>";
        
        // الخطوة 4: إنشاء جدول تفاصيل التحويلات
        echo "<div class='step'>";
        echo "<h3>الخطوة 4: إنشاء جدول تفاصيل التحويلات</h3>";
        
        try {
            $createTransferContainersSQL = "
            CREATE TABLE IF NOT EXISTS `transfer_containers` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `transfer_id` int(11) NOT NULL,
              `container_id` int(11) NOT NULL,
              `transfer_fee` decimal(10,2) NOT NULL DEFAULT 0.00,
              `pickup_time` datetime DEFAULT NULL,
              `delivery_time` datetime DEFAULT NULL,
              `container_condition` enum('good','damaged','sealed','unsealed') DEFAULT 'good',
              `notes` varchar(500) DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `unique_transfer_container` (`transfer_id`,`container_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";
            
            $db->exec($createTransferContainersSQL);
            echo "<div class='success'>✓ تم إنشاء جدول transfer_containers</div>";
            
        } catch (PDOException $e) {
            echo "<div class='error'>خطأ: " . $e->getMessage() . "</div>";
        }
        
        $currentStep++;
        updateProgress($currentStep, $totalSteps);
        echo "</div>";
        
        // الخطوة 5: إنشاء جدول التتبع
        echo "<div class='step'>";
        echo "<h3>الخطوة 5: إنشاء جدول التتبع</h3>";
        
        try {
            $createTrackingSQL = "
            CREATE TABLE IF NOT EXISTS `transfer_tracking` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `transfer_id` int(11) NOT NULL,
              `status` enum('created','pickup_scheduled','picked_up','in_transit','delivered','delayed','cancelled') NOT NULL,
              `update_time` datetime NOT NULL,
              `location` varchar(255) DEFAULT NULL,
              `latitude` decimal(10,8) DEFAULT NULL,
              `longitude` decimal(11,8) DEFAULT NULL,
              `notes` text DEFAULT NULL,
              `updated_by` int(11) NOT NULL DEFAULT 1,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";
            
            $db->exec($createTrackingSQL);
            echo "<div class='success'>✓ تم إنشاء جدول transfer_tracking</div>";
            
        } catch (PDOException $e) {
            echo "<div class='error'>خطأ: " . $e->getMessage() . "</div>";
        }
        
        $currentStep++;
        updateProgress($currentStep, $totalSteps);
        echo "</div>";
        
        // الخطوة 6: إضافة الصلاحيات
        echo "<div class='step'>";
        echo "<h3>الخطوة 6: إضافة الصلاحيات</h3>";
        
        try {
            // إضافة صلاحيات التحويلات
            $stmt = $db->prepare("INSERT IGNORE INTO permissions (name, permission_key, description) VALUES (?, ?, ?)");
            $stmt->execute(['إدارة التحويلات', 'transfers', 'إدارة تحويلات الحاويات وتتبعها']);
            $stmt->execute(['إدارة السائقين', 'drivers', 'إدارة بيانات السائقين ومركباتهم']);
            
            // منح الصلاحيات للمدير
            $stmt = $db->prepare("
                INSERT IGNORE INTO role_permissions (role_id, permission_id) 
                SELECT 1, id FROM permissions WHERE permission_key IN ('transfers', 'drivers')
            ");
            $stmt->execute();
            
            echo "<div class='success'>✓ تم إضافة الصلاحيات</div>";
            
        } catch (PDOException $e) {
            echo "<div class='error'>خطأ: " . $e->getMessage() . "</div>";
        }
        
        $currentStep++;
        updateProgress($currentStep, $totalSteps);
        echo "</div>";
        
        // النتيجة النهائية
        echo "<div class='step'>";
        echo "<h3>✅ تم إصلاح نظام التحويلات بنجاح!</h3>";
        
        // فحص نهائي
        $stmt = $db->query("SELECT COUNT(*) as count FROM traders");
        $traderCount = $stmt->fetch()['count'];
        
        $stmt = $db->query("SELECT COUNT(*) as count FROM drivers");
        $driverCount = $stmt->fetch()['count'];
        
        $stmt = $db->query("SELECT COUNT(*) as count FROM containers WHERE status = 'pending'");
        $containerCount = $stmt->fetch()['count'];
        
        echo "<div class='success'>إحصائيات النظام:</div>";
        echo "<ul>";
        echo "<li>عدد التجار: $traderCount</li>";
        echo "<li>عدد السائقين: $driverCount</li>";
        echo "<li>عدد الحاويات المتاحة: $containerCount</li>";
        echo "</ul>";
        
        echo "<div class='success'>يمكنك الآن استخدام نظام التحويلات بشكل طبيعي</div>";
        echo "<p><a href='index.php?page=transfers&action=add' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إضافة تحويل جديد</a></p>";
        echo "<p><a href='index.php?page=transfers' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>عرض التحويلات</a></p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
    }
    ?>
</body>
</html>
