<?php
// منع أي output قبل JSON
ob_start();

// تضمين config مباشرة
require_once '../config/config.php';

// مسح أي output سابق
ob_end_clean();

// تعيين headers
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');

try {
    // اختبار الاتصال بقاعدة البيانات
    $testQuery = $db->query("SELECT 1");
    
    // التحقق من وجود trader_id
    $traderId = isset($_GET['trader_id']) ? (int)$_GET['trader_id'] : 0;
    
    if ($traderId <= 0) {
        echo json_encode(['success' => false, 'error' => 'معرف التاجر مطلوب', 'received' => $_GET]);
        exit;
    }
    
    // جلب الحاويات المتاحة للنقل (غير المرتبطة بتحويلات نشطة)
    $query = "
        SELECT
            c.id,
            c.container_number,
            c.entry_date,
            c.purchase_price,
            c.selling_price,
            c.content,
            c.container_type,
            c.status,
            DATE_FORMAT(c.entry_date, '%Y-%m-%d') as entry_date_formatted
        FROM containers c
        WHERE c.trader_id = ?
        AND c.status = 'pending'
        AND c.id NOT IN (
            SELECT DISTINCT tc.container_id
            FROM transfer_containers tc
            JOIN container_transfers ct ON tc.transfer_id = ct.id
            WHERE ct.status IN ('pending', 'in_progress')
            AND tc.container_id IS NOT NULL
        )
    ";
    $params = [$traderId];
    
    // إضافة شروط البحث
    if (!empty($_GET['search'])) {
        $query .= " AND (c.container_number LIKE ? OR c.content LIKE ?)";
        $params[] = "%" . $_GET['search'] . "%";
        $params[] = "%" . $_GET['search'] . "%";
    }
    
    if (!empty($_GET['status'])) {
        $query .= " AND c.status = ?";
        $params[] = $_GET['status'];
    }
    
    if (!empty($_GET['date_from'])) {
        $query .= " AND c.entry_date >= ?";
        $params[] = $_GET['date_from'];
    }
    
    if (!empty($_GET['date_to'])) {
        $query .= " AND c.entry_date <= ?";
        $params[] = $_GET['date_to'];
    }
    
    $query .= " ORDER BY c.entry_date DESC, c.container_number ASC";
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $containers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تنسيق البيانات
    require_once '../includes/functions.php';
    
    foreach ($containers as &$container) {
        // تنسيق السعر
        $container['purchase_price'] = (float)$container['purchase_price'];
        $container['selling_price'] = (float)$container['selling_price'];
        
        // تنسيق التاريخ للعرض
        $container['entry_date_display'] = convertDateFromMysql($container['entry_date']);
        
        // معلومات إضافية
        $container['content'] = $container['content'] ?: '';
        $container['container_type'] = $container['container_type'] ?: '';
    }
    
    echo json_encode([
        'success' => true,
        'trader_id' => $traderId,
        'containers' => $containers,
        'count' => count($containers),
        'total_value' => array_sum(array_column($containers, 'purchase_price'))
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => __FILE__,
        'line' => __LINE__
    ]);
}

// التأكد من عدم وجود أي output إضافي
exit;
?>