<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام طباعة التحويلات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { color: blue; background: #d1ecf1; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
        .btn { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
    </style>
</head>
<body>
    <h1>اختبار نظام طباعة التحويلات</h1>
    
    <?php
    require_once 'config/config.php';
    
    try {
        $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار 1: فحص التحويلات المتاحة</h3>";
        
        // جلب التحويلات
        $stmt = $db->query("
            SELECT 
                ct.id,
                ct.transfer_number,
                ct.transfer_date,
                ct.status,
                ct.total_containers,
                ct.total_amount,
                t.name as trader_name,
                d.driver_name
            FROM container_transfers ct
            LEFT JOIN traders t ON ct.trader_id = t.id
            LEFT JOIN drivers d ON ct.driver_id = d.id
            ORDER BY ct.created_at DESC
            LIMIT 10
        ");
        $transfers = $stmt->fetchAll();
        
        if (count($transfers) > 0) {
            echo "<div class='success'>✓ تم العثور على " . count($transfers) . " تحويل</div>";
            echo "<table>";
            echo "<tr><th>المعرف</th><th>رقم التحويل</th><th>التاجر</th><th>السائق</th><th>الحالة</th><th>الإجراءات</th></tr>";
            
            foreach ($transfers as $transfer) {
                echo "<tr>";
                echo "<td>" . $transfer['id'] . "</td>";
                echo "<td>" . htmlspecialchars($transfer['transfer_number']) . "</td>";
                echo "<td>" . htmlspecialchars($transfer['trader_name'] ?? 'غير محدد') . "</td>";
                echo "<td>" . htmlspecialchars($transfer['driver_name'] ?? 'غير محدد') . "</td>";
                echo "<td>" . $transfer['status'] . "</td>";
                echo "<td>";
                echo "<a href='index.php?page=transfer_print&id=" . $transfer['id'] . "' target='_blank' class='btn'>طباعة مفصلة</a>";
                echo "<a href='index.php?page=transfer_print_simple&id=" . $transfer['id'] . "' target='_blank' class='btn btn-success'>طباعة مبسطة</a>";
                echo "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='error'>لا توجد تحويلات في النظام</div>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار 2: فحص ملفات الطباعة</h3>";
        
        $printFiles = [
            'pages/transfer_print.php' => 'صفحة الطباعة المفصلة',
            'pages/transfer_print_simple.php' => 'صفحة الطباعة المبسطة',
            'pages/transfers_report_print.php' => 'صفحة طباعة التقرير',
            'assets/js/transfer-print.js' => 'ملف JavaScript للطباعة'
        ];
        
        foreach ($printFiles as $file => $description) {
            if (file_exists($file)) {
                echo "<div class='success'>✓ $description موجود</div>";
            } else {
                echo "<div class='error'>✗ $description غير موجود</div>";
            }
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار 3: اختبار روابط الطباعة</h3>";
        
        if (count($transfers) > 0) {
            $testTransfer = $transfers[0];
            $testId = $testTransfer['id'];
            
            echo "<div class='info'>اختبار مع التحويل: " . htmlspecialchars($testTransfer['transfer_number']) . "</div>";
            
            $printLinks = [
                "index.php?page=transfer_print&id=$testId" => 'طباعة مفصلة',
                "index.php?page=transfer_print_simple&id=$testId" => 'طباعة مبسطة',
                "index.php?page=transfer_print_simple&id=$testId&auto_print=1" => 'طباعة مباشرة',
                "index.php?page=transfers_report_print" => 'تقرير التحويلات',
                "index.php?page=transfers_report_print&auto_print=1" => 'تقرير مع طباعة مباشرة'
            ];
            
            foreach ($printLinks as $link => $description) {
                echo "<p>";
                echo "<a href='$link' target='_blank' class='btn'>$description</a>";
                echo " - <small>$link</small>";
                echo "</p>";
            }
        } else {
            echo "<div class='warning'>لا يمكن اختبار الروابط بدون تحويلات</div>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار 4: فحص البيانات المطلوبة للطباعة</h3>";
        
        if (count($transfers) > 0) {
            $testId = $transfers[0]['id'];
            
            // فحص بيانات التحويل الكاملة
            $stmt = $db->prepare("
                SELECT 
                    ct.*,
                    t.name as trader_name,
                    t.contact_person,
                    t.phone as trader_phone,
                    t.address as trader_address,
                    d.driver_name,
                    d.phone as driver_phone,
                    d.license_number,
                    d.vehicle_type,
                    d.vehicle_number
                FROM container_transfers ct
                LEFT JOIN traders t ON ct.trader_id = t.id
                LEFT JOIN drivers d ON ct.driver_id = d.id
                WHERE ct.id = ?
            ");
            $stmt->execute([$testId]);
            $transferData = $stmt->fetch();
            
            if ($transferData) {
                echo "<div class='success'>✓ بيانات التحويل كاملة</div>";
                
                // فحص الحاويات
                $stmt = $db->prepare("
                    SELECT COUNT(*) as count 
                    FROM transfer_containers tc
                    JOIN containers c ON tc.container_id = c.id
                    WHERE tc.transfer_id = ?
                ");
                $stmt->execute([$testId]);
                $containerCount = $stmt->fetch()['count'];
                
                echo "<div class='info'>عدد الحاويات في التحويل: $containerCount</div>";
                
                // فحص سجل التتبع
                $stmt = $db->prepare("
                    SELECT COUNT(*) as count 
                    FROM transfer_tracking 
                    WHERE transfer_id = ?
                ");
                $stmt->execute([$testId]);
                $trackingCount = $stmt->fetch()['count'];
                
                echo "<div class='info'>عدد سجلات التتبع: $trackingCount</div>";
                
            } else {
                echo "<div class='error'>لا يمكن جلب بيانات التحويل</div>";
            }
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>اختبار 5: اختبار تقرير التحويلات</h3>";
        
        $reportParams = [
            '' => 'تقرير شامل',
            'status=pending' => 'تقرير التحويلات المعلقة',
            'date_from=' . date('Y-m-01') . '&date_to=' . date('Y-m-d') => 'تقرير الشهر الحالي',
            'status=completed&date_from=' . date('Y-m-01') => 'تقرير التحويلات المكتملة هذا الشهر'
        ];
        
        foreach ($reportParams as $params => $description) {
            $link = "index.php?page=transfers_report_print" . ($params ? "?$params" : "");
            echo "<p>";
            echo "<a href='$link' target='_blank' class='btn btn-warning'>$description</a>";
            echo " - <small>$link</small>";
            echo "</p>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>✅ ملخص الاختبار</h3>";
        echo "<div class='success'>نظام الطباعة جاهز للاستخدام!</div>";
        echo "<div class='info'>";
        echo "<strong>الميزات المتاحة:</strong><br>";
        echo "• طباعة مفصلة للتحويلات<br>";
        echo "• طباعة مبسطة للتحويلات<br>";
        echo "• طباعة مباشرة<br>";
        echo "• تقارير شاملة للتحويلات<br>";
        echo "• فلترة التقارير حسب التاريخ والحالة والتاجر<br>";
        echo "• تصميم متجاوب للطباعة<br>";
        echo "• دعم كامل للغة العربية<br>";
        echo "</div>";
        
        echo "<div style='margin-top: 20px;'>";
        echo "<a href='index.php?page=transfers' class='btn'>العودة إلى التحويلات</a>";
        echo "<a href='index.php?page=transfers_report_print' target='_blank' class='btn btn-success'>عرض تقرير التحويلات</a>";
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
    }
    ?>
</body>
</html>
