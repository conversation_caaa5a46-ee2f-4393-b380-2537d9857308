<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title><?php echo APP_NAME; ?></title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#007bff">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="CCIS">
    <meta name="description" content="نظام إدارة التخليص الجمركي للحاويات">
    <meta name="format-detection" content="telephone=no">
    <!-- PWA Icons -->
    <link rel="apple-touch-icon" href="assets/img/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="192x192" href="assets/img/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="assets/img/icon-512x512.png">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- User Data for Mobile App -->
    <?php if (isLoggedIn()): ?>
        <?php
        // Get current user data for mobile app
        try {
            $userStmt = $db->prepare("
                SELECT u.profile_image, u.full_name, r.name as role_name
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.id
                WHERE u.id = :id
            ");
            $userStmt->execute(['id' => getCurrentUserId()]);
            $currentUser = $userStmt->fetch();
        } catch (PDOException $e) {
            $currentUser = null;
        }
        ?>
        <meta name="user-name" content="<?php echo htmlspecialchars($currentUser['full_name'] ?? getCurrentUsername()); ?>">
        <meta name="user-role" content="<?php echo htmlspecialchars($currentUser['role_name'] ?? ''); ?>">
        <?php if (!empty($currentUser['profile_image'])): ?>
        <meta name="user-image" content="<?php echo htmlspecialchars($currentUser['profile_image']); ?>">
        <?php endif; ?>
    <?php endif; ?>

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="assets/adminlte/plugins/fontawesome-free/css/all.min.css">

    <!-- AdminLTE Theme style -->
    <link rel="stylesheet" href="assets/adminlte/dist/css/adminlte.min.css">

    <!-- Custom CSS for RTL and system customizations -->
    <link rel="stylesheet" href="assets/css/custom-rtl.css?v=<?php echo time(); ?>">

    <!-- Custom CSS for pages styling with AdminLTE features -->
    <link rel="stylesheet" href="assets/css/pages-style.css">

    <!-- Mobile PWA CSS -->
    <link rel="stylesheet" href="assets/css/mobile.css">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#007bff">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <link rel="manifest" href="manifest.json">
    <link rel="apple-touch-icon" href="assets/img/icon-192x192.png">

    <!-- Mobile Detection Script -->
    <script src="js/mobile-detection.js"></script>
</head>
<body class="hold-transition sidebar-mini layout-fixed" dir="rtl">
<div class="wrapper">
  <!-- Navbar -->
  <nav class="main-header navbar navbar-expand navbar-white navbar-light">
    <!-- Right navbar links (في RTL تصبح يمين) -->
    <ul class="navbar-nav">
      <li class="nav-item navbar-toggle-visible">
        <a class="nav-link" data-widget="pushmenu" href="#" role="button" title="فتح/إغلاق القائمة الجانبية">
          <i class="fas fa-bars"></i>
        </a>
      </li>
    </ul>

    <!-- Logo and App Name (shown only when sidebar is collapsed) -->
    <div class="navbar-brand-area navbar-menu-collapsed">
      <img src="assets/img/icon-512x512.png" alt="<?php echo APP_NAME; ?>" class="navbar-logo">
      <span class="navbar-app-name"><?php echo APP_NAME; ?></span>
    </div>

    <!-- Center navbar links (hidden by default, shown when sidebar is collapsed) -->
    <ul class="navbar-nav mx-auto navbar-menu-collapsed" style="display: none;">
      <?php if (isLoggedIn()): ?>

      <!-- Dashboard -->
      <li class="nav-item">
        <a href="index.php?page=dashboard" class="nav-link <?php echo $page == 'dashboard' ? 'active' : ''; ?>">
          <i class="fas fa-tachometer-alt"></i>
          <span class="nav-text">لوحة التحكم</span>
        </a>
      </li>

      <?php if (hasPermission('containers')): ?>
      <!-- Containers -->
      <li class="nav-item">
        <a href="index.php?page=containers" class="nav-link <?php echo $page == 'containers' ? 'active' : ''; ?>">
          <i class="fas fa-shipping-fast"></i>
          <span class="nav-text">الحاويات</span>
        </a>
      </li>
      <?php endif; ?>

      <?php if (hasPermission('customers')): ?>
      <!-- Customers -->
      <li class="nav-item">
        <a href="index.php?page=customers" class="nav-link <?php echo $page == 'customers' ? 'active' : ''; ?>">
          <i class="fas fa-users"></i>
          <span class="nav-text">الشركات</span>
        </a>
      </li>

      <!-- Traders -->
      <li class="nav-item">
        <a href="index.php?page=traders_list" class="nav-link <?php echo $page == 'traders_list' ? 'active' : ''; ?>">
          <i class="fas fa-user-tie"></i>
          <span class="nav-text">التجار</span>
        </a>
      </li>
      <?php endif; ?>

      <?php if (hasPermission('financial')): ?>
      <!-- Financial Dropdown -->
      <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle <?php echo $page == 'financial' || $page == 'penalties' ? 'active' : ''; ?>" href="#" id="financialDropdown" role="button" data-toggle="dropdown">
          <i class="fas fa-file-invoice-dollar"></i>
          <span class="nav-text">المالية</span>
        </a>
        <div class="dropdown-menu">
          <a class="dropdown-item" href="index.php?page=financial">
            <i class="fas fa-file-invoice-dollar"></i> المستندات المالية
          </a>
          <a class="dropdown-item" href="index.php?page=financial&type=receipt">
            <i class="fas fa-receipt"></i> سندات القبض
          </a>
          <a class="dropdown-item" href="index.php?page=financial&type=payment">
            <i class="fas fa-money-check-alt"></i> سندات الدفع
          </a>
          <a class="dropdown-item" href="index.php?page=financial&type=expense">
            <i class="fas fa-hand-holding-usd"></i> سندات الصرف
          </a>
          <a class="dropdown-item" href="index.php?page=financial&type=transfer">
            <i class="fas fa-exchange-alt"></i> الحوالات المالية
          </a>
          <a class="dropdown-item" href="index.php?page=financial&type=capital">
            <i class="fas fa-piggy-bank"></i> رأس المال
          </a>
          <a class="dropdown-item" href="index.php?page=penalties">
            <i class="fas fa-exclamation-triangle"></i> إدارة الغرامات
          </a>
        </div>
      </li>
      <?php endif; ?>

      <?php if (hasPermission('reports')): ?>
      <!-- Reports Dropdown -->
      <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle <?php echo $page == 'reports' ? 'active' : ''; ?>" href="#" id="reportsDropdown" role="button" data-toggle="dropdown">
          <i class="fas fa-chart-line"></i>
          <span class="nav-text">التقارير</span>
        </a>
        <div class="dropdown-menu">
          <a class="dropdown-item" href="index.php?page=reports&type=financial">
            <i class="fas fa-chart-line"></i> التقارير المالية
          </a>
          <a class="dropdown-item" href="index.php?page=reports&type=cash">
            <i class="fas fa-coins"></i> التقارير النقدية
          </a>
          <a class="dropdown-item" href="index.php?page=reports&type=regulatory">
            <i class="fas fa-clipboard-check"></i> التقارير الرقابية
          </a>
          <a class="dropdown-item" href="index.php?page=reports&type=penalties">
            <i class="fas fa-exclamation-circle"></i> تقارير الغرامات
          </a>
          <a class="dropdown-item" href="index.php?page=reports&type=customer">
            <i class="fas fa-user-check"></i> كشف حساب شركة
          </a>
          <a class="dropdown-item" href="index.php?page=reports&type=customer_balances">
            <i class="fas fa-users-cog"></i> أرصدة العملاء
          </a>
        </div>
      </li>
      <?php endif; ?>

      <?php if (hasPermission('users')): ?>
      <!-- Users -->
      <li class="nav-item">
        <a href="index.php?page=users" class="nav-link <?php echo $page == 'users' ? 'active' : ''; ?>">
          <i class="fas fa-user-shield"></i>
          <span class="nav-text">المستخدمين</span>
        </a>
      </li>
      <?php endif; ?>

      <?php if (hasPermission('settings')): ?>
      <!-- Settings Dropdown -->
      <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle <?php echo $page == 'settings' ? 'active' : ''; ?>" href="#" id="settingsDropdown" role="button" data-toggle="dropdown">
          <i class="fas fa-cogs"></i>
          <span class="nav-text">الإعدادات</span>
        </a>
        <div class="dropdown-menu">
          <a class="dropdown-item" href="index.php?page=settings&action=general">
            <i class="fas fa-cogs"></i> الإعدادات العامة
          </a>
          <a class="dropdown-item" href="index.php?page=settings&action=roles">
            <i class="fas fa-user-cog"></i> الأدوار والصلاحيات
          </a>
          <a class="dropdown-item" href="index.php?page=settings&action=backup">
            <i class="fas fa-database"></i> النسخ الاحتياطي
          </a>
        </div>
      </li>
      <?php endif; ?>

      <?php endif; ?>
    </ul>

    <!-- Left navbar links (في RTL تصبح يسار) -->
    <ul class="navbar-nav ml-auto">
      <?php if (isLoggedIn()): ?>
      <!-- Notifications Dropdown Menu -->
      <li class="nav-item dropdown">
        <a class="nav-link" data-toggle="dropdown" href="#">
          <i class="far fa-bell"></i>
          <?php
          // استرجاع عدد الإشعارات غير المقروءة
          $unreadCount = 0;
          try {
              $notificationsStmt = $db->prepare("
                  SELECT COUNT(*) as count
                  FROM notifications
                  WHERE user_id = :user_id AND is_read = 0
              ");
              $notificationsStmt->execute(['user_id' => getCurrentUserId()]);
              $unreadCount = $notificationsStmt->fetch()['count'];
          } catch (PDOException $e) {
              // تجاهل الخطأ إذا كان الجدول غير موجود
              error_log('خطأ في استرجاع الإشعارات: ' . $e->getMessage());
          }

          if ($unreadCount > 0):
          ?>
          <span class="badge badge-warning navbar-badge"><?php echo $unreadCount; ?></span>
          <?php endif; ?>
        </a>
        <div class="dropdown-menu dropdown-menu-lg dropdown-menu-left">
          <span class="dropdown-header"><?php echo $unreadCount; ?> إشعارات</span>
          <div class="dropdown-divider"></div>
          <?php
          // استرجاع آخر 5 إشعارات
          $notifications = [];
          try {
              $notificationsStmt = $db->prepare("
                  SELECT * FROM notifications
                  WHERE user_id = :user_id
                  ORDER BY created_at DESC
                  LIMIT 5
              ");
              $notificationsStmt->execute(['user_id' => getCurrentUserId()]);
              $notifications = $notificationsStmt->fetchAll();
          } catch (PDOException $e) {
              // تجاهل الخطأ إذا كان الجدول غير موجود
              error_log('خطأ في استرجاع الإشعارات: ' . $e->getMessage());
          }

          if (empty($notifications)):
          ?>
          <a href="#" class="dropdown-item">
            <i class="fas fa-info-circle ml-2"></i> لا توجد إشعارات
          </a>
          <?php else: ?>
            <?php foreach ($notifications as $notification): ?>
            <a href="<?php echo $notification['link']; ?>" class="dropdown-item">
              <i class="fas fa-<?php echo $notification['icon'] ?: 'bell'; ?> ml-2"></i> <?php echo htmlspecialchars($notification['message']); ?>
              <span class="float-left text-muted text-sm"><?php echo timeAgo($notification['created_at']); ?></span>
            </a>
            <div class="dropdown-divider"></div>
            <?php endforeach; ?>
            <a href="index.php?page=notifications" class="dropdown-item dropdown-footer">عرض جميع الإشعارات</a>
          <?php endif; ?>
        </div>
      </li>

      <!-- Quick Logout Button -->
      <!-- <li class="nav-item">
        <a class="nav-link" href="logout.php" title="تسجيل الخروج" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
          <i class="fas fa-sign-out-alt text-danger"></i>
        </a>
      </li> -->

      <!-- User Dropdown Menu -->
      <li class="nav-item dropdown">
        <a class="nav-link" data-toggle="dropdown" href="#">
          <?php if (!empty($currentUserImage) && file_exists('uploads/profile_images/' . $currentUserImage)): ?>
            <img src="uploads/profile_images/<?php echo htmlspecialchars($currentUserImage); ?>"
                 class="img-circle"
                 alt="صورة المستخدم"
                 style="width: 25px; height: 25px; object-fit: cover; margin-left: 5px;">
          <?php else: ?>
            <i class="far fa-user"></i>
          <?php endif; ?>
          <?php echo htmlspecialchars(@$currentUserFullName ?: @getCurrentUsername()); ?>
        </a>
        <div class="dropdown-menu dropdown-menu-lg dropdown-menu-left">
          <div class="dropdown-header text-center">
            <?php if (!empty($currentUserImage) && file_exists('uploads/profile_images/' . $currentUserImage)): ?>
              <img src="uploads/profile_images/<?php echo htmlspecialchars($currentUserImage); ?>"
                   class="img-circle mb-2"
                   alt="صورة المستخدم"
                   style="width: 50px; height: 50px; object-fit: cover;">
              <br>
            <?php endif; ?>
            <strong><?php echo htmlspecialchars(@$currentUserFullName ?: @getCurrentUsername()); ?></strong>
            <?php if (@$currentUserRole): ?>
              <br><small class="text-muted"><?php echo htmlspecialchars(@$currentUserRole); ?></small>
            <?php endif; ?>
          </div>
          <div class="dropdown-divider"></div>
          <a href="index.php?page=profile" class="dropdown-item">
            <i class="fas fa-id-card ml-2"></i> الملف الشخصي
          </a>
          <a href="index.php?page=profile&action=change_password" class="dropdown-item">
            <i class="fas fa-key ml-2"></i> تغيير كلمة المرور
          </a>
          <div class="dropdown-divider"></div>
          <a href="logout.php" class="dropdown-item text-danger" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
            <i class="fas fa-sign-out-alt ml-2"></i> تسجيل الخروج
          </a>
        </div>
      </li>
      <?php endif; ?>
    </ul>
  </nav>
  <!-- /.navbar -->

  <?php if (isLoggedIn()): ?>
  <!-- Main Sidebar Container -->
  <aside class="main-sidebar sidebar-dark-primary elevation-4">
    <!-- Brand Logo -->
    <a href="index.php" class="brand-link">
               <span style="text-align:right; margin-right:10px; " class="brand-text font-weight-bold"><?php echo APP_NAME; ?></span>
<img src="logo.png" alt="<?php echo APP_NAME; ?>" class="" style="width:17%; border-radius:15%; ">
 </a>

    <!-- Sidebar -->
    <div class="sidebar">
      <!-- Sidebar user panel (optional) -->
      <div class="user-panel mt-3 pb-3 mb-3 d-flex">
        <div class="image">
          <?php
          // الحصول على صورة المستخدم الحالي
          $currentUserImage = '';
          $currentUserFullName = '';
          $currentUserRole = '';
          try {
              $userStmt = $db->prepare("
                  SELECT u.profile_image, u.full_name, r.name as role_name
                  FROM users u
                  LEFT JOIN roles r ON u.role_id = r.id
                  WHERE u.id = :id
              ");
              $userStmt->execute(['id' => getCurrentUserId()]);
              $currentUser = $userStmt->fetch();
              if ($currentUser) {
                  $currentUserImage = $currentUser['profile_image'];
                  $currentUserFullName = $currentUser['full_name'];
                  $currentUserRole = $currentUser['role_name'];
              }
          } catch (PDOException $e) {
              // تجاهل الخطأ
          }
          ?>

          <?php if (!empty($currentUserImage) && file_exists('uploads/profile_images/' . $currentUserImage)): ?>
            <img src="uploads/profile_images/<?php echo htmlspecialchars($currentUserImage); ?>"
                 class="img-circle elevation-2"
                 alt="صورة المستخدم"
                 style="width: 34px; height: 34px; object-fit: cover;">
          <?php else: ?>
            <i class="fas fa-user-circle fa-2x text-white"></i>
          <?php endif; ?>
        </div>
        <div class="info">
          <a href="index.php?page=profile" class="d-block text-white">
            <?php echo htmlspecialchars($currentUserFullName ?: getCurrentUsername()); ?>
          </a>
          <?php if ($currentUserRole): ?>
            <small class="text-muted"><?php echo htmlspecialchars($currentUserRole); ?></small>
          <?php endif; ?>
        </div>
      </div>

      <!-- Sidebar Menu -->
      <nav class="mt-2">
        <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">

          <!-- Dashboard -->
          <li class="nav-item">
            <a href="index.php?page=dashboard" class="nav-link <?php echo $page == 'dashboard' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-tachometer-alt"></i>
              <p>لوحة التحكم</p>
            </a>
          </li>

          <?php if (hasPermission('containers')): ?>
          <!-- Containers -->
          <li class="nav-item">
            <a href="index.php?page=containers" class="nav-link <?php echo $page == 'containers' ? 'active' : ''; ?>">
              <i class="nav-icon fas fas fa-box"></i>
              <p>إدارة الحاويات</p>
            </a>
          </li>
          <?php endif; ?>

          <?php if (hasPermission('customers')): ?>
            <li class="nav-header">الشركات والتجار</li>
          <!-- Customers -->
          <li class="nav-item">
            <a href="index.php?page=customers" class="nav-link <?php echo $page == 'customers' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-users"></i>
              <p>إدارة الشركات</p>
            </a>
          </li>

            <li class="nav-item">
            <a href="index.php?page=traders_list" class="nav-link <?php echo $page == 'traders_list' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-user-tie"></i>
              <p>التجار</p>
            </a>
          </li>
          <?php endif; ?>

          <?php if (hasPermission('financial')): ?>
          <!-- Financial Management Label -->
          <li class="nav-header">الإدارة المالية</li>

          <!-- Financial Documents -->
          <li class="nav-item">
            <a href="index.php?page=financial" class="nav-link <?php echo $page == 'financial' && !isset($_GET['type']) ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-file-invoice-dollar"></i>
              <p>المستندات المالية</p>
            </a>
          </li>

          <!-- Receipt Vouchers -->
          <li class="nav-item">
            <a href="index.php?page=financial&type=receipt" class="nav-link <?php echo $page == 'financial' && isset($_GET['type']) && $_GET['type'] == 'receipt' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-receipt"></i>
              <p>سندات القبض</p>
            </a>
          </li>

          <!-- Payment Vouchers -->
          <li class="nav-item">
            <a href="index.php?page=financial&type=payment" class="nav-link <?php echo $page == 'financial' && isset($_GET['type']) && $_GET['type'] == 'payment' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-money-check-alt"></i>
              <p>سندات الدفع</p>
            </a>
          </li>

          <!-- Expense Vouchers -->
          <li class="nav-item">
            <a href="index.php?page=financial&type=expense" class="nav-link <?php echo $page == 'financial' && isset($_GET['type']) && $_GET['type'] == 'expense' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-hand-holding-usd"></i>
              <p>سندات الصرف</p>
            </a>
          </li>

          <!-- Financial Transfers -->
          <li class="nav-item">
            <a href="index.php?page=financial&type=transfer" class="nav-link <?php echo $page == 'financial' && isset($_GET['type']) && $_GET['type'] == 'transfer' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-exchange-alt"></i>
              <p>الحوالات المالية</p>
            </a>
          </li>

          <!-- Capital -->
          <li class="nav-item">
            <a href="index.php?page=financial&type=capital" class="nav-link <?php echo $page == 'financial' && isset($_GET['type']) && $_GET['type'] == 'capital' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-piggy-bank"></i>
              <p>رأس المال</p>
            </a>
          </li>

          <!-- Penalties -->
          <li class="nav-item">
            <a href="index.php?page=penalties" class="nav-link <?php echo $page == 'penalties' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-exclamation-triangle"></i>
              <p>إدارة الغرامات</p>
            </a>
          </li>

             <!-- Penalties -->
          <li class="nav-item">
            <a href="index.php?page=wages" class="nav-link <?php echo $page == 'wages' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-money-bill-wave"></i>
              <p>اجور اخرى</p>
            </a>
          </li>
          <?php endif; ?>

          <!-- إدارة التحويلات (مؤقت بدون فحص الصلاحيات) -->
          <li class="nav-header">النقل والتحويلات</li>

          <!-- Container Transfers -->
          <li class="nav-item">
            <a href="index.php?page=transfers" class="nav-link <?php echo $page == 'transfers' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-shipping-fast"></i>
              <p>تحويلات الحاويات</p>
            </a>
          </li>

          <!-- Drivers Management -->
          <li class="nav-item">
            <a href="index.php?page=drivers" class="nav-link <?php echo $page == 'drivers' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-user-tie"></i>
              <p>إدارة السائقين</p>
            </a>
          </li>

          <?php if (hasPermission('reports')): ?>
          <!-- Reports Label -->
          <li class="nav-header">التقارير</li>

          <!-- Financial Reports -->
          <li class="nav-item">
            <a href="index.php?page=reports&type=financial" class="nav-link <?php echo $page == 'reports' && isset($_GET['type']) && $_GET['type'] == 'financial' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-chart-line"></i>
              <p>التقارير المالية</p>
            </a>
          </li>

          <!-- Cash Reports -->
          <li class="nav-item">
            <a href="index.php?page=reports&type=cash" class="nav-link <?php echo $page == 'reports' && isset($_GET['type']) && $_GET['type'] == 'cash' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-coins"></i>
              <p>التقارير النقدية</p>
            </a>
          </li>

          <!-- Regulatory Reports -->
          <li class="nav-item">
            <a href="index.php?page=reports&type=regulatory" class="nav-link <?php echo $page == 'reports' && isset($_GET['type']) && $_GET['type'] == 'regulatory' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-clipboard-check"></i>
              <p>التقارير الرقابية</p>
            </a>
          </li>

          <!-- Penalties Reports -->
          <li class="nav-item">
            <a href="index.php?page=reports&type=penalties" class="nav-link <?php echo $page == 'reports' && isset($_GET['type']) && $_GET['type'] == 'penalties' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-exclamation-circle"></i>
              <p>تقارير الغرامات</p>
            </a>
          </li>

          <!-- Customer Account Statement -->
          <li class="nav-item">
            <a href="index.php?page=reports&type=customer" class="nav-link <?php echo $page == 'reports' && isset($_GET['type']) && $_GET['type'] == 'customer' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-user-check"></i>
              <p>كشف حساب شركة</p>
            </a>
          </li>

          <!-- Trader Account Statement -->
          <li class="nav-item">
            <a href="index.php?page=trader_statement_select" class="nav-link <?php echo $page == 'trader_statement_select' || $page == 'trader_statement' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-file-invoice-dollar"></i>
              <p>كشف حساب تاجر</p>
            </a>
          </li>
          <?php endif; ?>

          <?php if (hasPermission('users')): ?>
          <!-- Users -->
          <li class="nav-item">
            <a href="index.php?page=users" class="nav-link <?php echo $page == 'users' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-user-shield"></i>
              <p>إدارة المستخدمين</p>
            </a>
          </li>
          <?php endif; ?>

          <?php if (hasPermission('settings')): ?>
          <!-- Settings Label -->
          <li class="nav-header">الإعدادات</li>

          <!-- General Settings -->
          <li class="nav-item">
            <a href="index.php?page=settings&action=general" class="nav-link <?php echo $page == 'settings' && isset($_GET['action']) && $_GET['action'] == 'general' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-cogs"></i>
              <p>الإعدادات العامة</p>
            </a>
          </li>

          <!-- Roles and Permissions -->
          <li class="nav-item">
            <a href="index.php?page=settings&action=roles" class="nav-link <?php echo $page == 'settings' && isset($_GET['action']) && $_GET['action'] == 'roles' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-user-cog"></i>
              <p>الأدوار والصلاحيات</p>
            </a>
          </li>

          <!-- Backup -->
          <li class="nav-item">
            <a href="index.php?page=settings&action=backup" class="nav-link <?php echo $page == 'settings' && isset($_GET['action']) && $_GET['action'] == 'backup' ? 'active' : ''; ?>">
              <i class="nav-icon fas fa-database"></i>
              <p>النسخ الاحتياطي</p>
            </a>
          </li>
          <?php endif; ?>

        </ul>
      </nav>
      <!-- /.sidebar-menu -->
    </div>
    <!-- /.sidebar -->
  </aside>
  <?php endif; ?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <!-- <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0 text-dark">
              <?php
              $pageTitle = '';
              switch($page) {
                case 'dashboard': $pageTitle = 'لوحة التحكم'; break;
                case 'containers': $pageTitle = 'إدارة الحاويات'; break;
                case 'customers': $pageTitle = 'إدارة الشركات'; break;
                case 'traders_list': $pageTitle = 'التجار'; break;
                case 'trader_statement_select': $pageTitle = 'كشف حساب تاجر'; break;
                case 'trader_statement': $pageTitle = 'كشف حساب تاجر'; break;
                case 'financial': $pageTitle = 'الإدارة المالية'; break;
                case 'reports': $pageTitle = 'التقارير'; break;
                case 'users': $pageTitle = 'إدارة المستخدمين'; break;
                case 'settings': $pageTitle = 'الإعدادات'; break;
                case 'profile': $pageTitle = 'الملف الشخصي'; break;
                case 'notifications': $pageTitle = 'الإشعارات'; break;
                default: $pageTitle = 'الصفحة الرئيسية';
              }
              echo $pageTitle;
              ?>
            </h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-left">
              <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
              <li class="breadcrumb-item active"><?php echo $pageTitle; ?></li>
            </ol>
          </div>
        </div>
      </div>
    </div> -->
    <!-- /.content-header -->

    <!-- Main content -->
    <div class="content">
      <div class="container-fluid">

<?php
// دالة لحساب الوقت المنقضي
function timeAgo($datetime) {
    $now = new DateTime();
    $ago = new DateTime($datetime);
    $diff = $now->diff($ago);

    if ($diff->y > 0) {
        return $diff->y . ' سنة' . ($diff->y > 1 ? '' : '') . ' مضت';
    } elseif ($diff->m > 0) {
        return $diff->m . ' شهر' . ($diff->m > 1 ? '' : '') . ' مضت';
    } elseif ($diff->d > 0) {
        return $diff->d . ' يوم' . ($diff->d > 1 ? '' : '') . ' مضت';
    } elseif ($diff->h > 0) {
        return $diff->h . ' ساعة' . ($diff->h > 1 ? '' : '') . ' مضت';
    } elseif ($diff->i > 0) {
        return $diff->i . ' دقيقة' . ($diff->i > 1 ? '' : '') . ' مضت';
    } else {
        return 'الآن';
    }
}
?>
