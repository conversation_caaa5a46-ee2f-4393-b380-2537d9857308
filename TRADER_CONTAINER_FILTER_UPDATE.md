# تحديث نظام فلترة الحاويات حسب التاجر

## نظرة عامة

تم تحديث صفحة اختيار التاجر وكشف الحساب لتدعم فلترة الحاويات حسب التاجر المحدد، مما يوفر تقارير أكثر دقة وتفصيلاً.

## التغييرات المطبقة

### ✅ **1. صفحة اختيار التاجر (`trader_statement_select.php`)**

#### **إضافة قسم اختيار الحاوية:**
```html
<div class="col-md-6">
    <label class="form-label fw-semibold">
        <i class="fas fa-shipping-fast text-info me-1"></i>
        اختر الحاوية (اختياري)
    </label>
    <div class="searchable-select-wrapper">
        <select name="container_id" id="container_select" class="form-select searchable-select">
            <option value="">اختر التاجر أولاً لعرض حاوياته</option>
            <?php foreach ($containers as $container): ?>
            <option value="<?php echo $container['id']; ?>" 
                    data-trader-id="<?php echo $container['trader_id']; ?>"
                    style="display: none;">
                <?php echo htmlspecialchars($container['container_number']); ?>
                - (<?php echo htmlspecialchars($container['customer_name']); ?>)
            </option>
            <?php endforeach; ?>
        </select>
    </div>
    <small class="form-text text-muted">
        <i class="fas fa-info-circle"></i>
        اتركه فارغاً لعرض جميع حاويات التاجر
    </small>
</div>
```

#### **دالة JavaScript للفلترة التلقائية:**
```javascript
function updateContainersList() {
    const traderSelect = document.querySelector('select[name="trader_id"]');
    const containerSelect = document.getElementById('container_select');
    const traderId = traderSelect.value;
    
    // إعادة تعيين قائمة الحاويات
    containerSelect.value = '';
    
    // إخفاء جميع الخيارات ما عدا الخيار الأول
    let visibleCount = 0;
    for (let i = 1; i < containerOptions.length; i++) {
        const option = containerOptions[i];
        const optionTraderId = option.getAttribute('data-trader-id');
        
        if (!traderId) {
            option.style.display = 'none';
        } else if (optionTraderId == traderId) {
            option.style.display = '';
            visibleCount++;
        } else {
            option.style.display = 'none';
        }
    }
    
    // تحديث النص الافتراضي
    const defaultOption = containerOptions[0];
    if (!traderId) {
        defaultOption.textContent = 'اختر التاجر أولاً لعرض حاوياته';
    } else if (visibleCount === 0) {
        defaultOption.textContent = 'لا توجد حاويات لهذا التاجر';
    } else {
        defaultOption.textContent = 'جميع الحاويات (اختياري)';
    }
}
```

### ✅ **2. صفحة كشف الحساب (`trader_statement.php`)**

#### **إضافة دعم فلتر الحاوية:**
```php
// الحصول على معرف التاجر والحاوية
$trader_id = isset($_GET['trader_id']) ? (int)$_GET['trader_id'] : 0;
$container_id = isset($_GET['container_id']) ? (int)$_GET['container_id'] : 0;
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-01');
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d');
```

#### **تحديث استعلام الحاويات:**
```php
$containers_query = "
    SELECT c.*, 
           (c.selling_price - c.purchase_price) as profit,
           DATE(c.created_at) as transaction_date,
           'container' as transaction_type
    FROM containers c 
    WHERE c.trader_id = ? 
    AND DATE(c.created_at) BETWEEN ? AND ?
";
$containers_params = [$trader_id, $date_from, $date_to];

// إضافة فلتر الحاوية إذا تم تحديدها
if ($container_id > 0) {
    $containers_query .= " AND c.id = ?";
    $containers_params[] = $container_id;
}

$containers_query .= " ORDER BY c.created_at ASC";
```

#### **تحديث استعلام المستندات المالية:**
```php
$financial_query = "
    SELECT fd.*, 
           DATE(fd.document_date) as transaction_date,
           'financial' as transaction_type,
           -- ... باقي الحقول
    FROM financial_documents fd 
    WHERE fd.customer_id = ? 
    AND fd.document_date BETWEEN ? AND ?
";
$financial_params = [$trader_id, $date_from, $date_to];

// إضافة فلتر الحاوية إذا تم تحديدها
if ($container_id > 0) {
    $financial_query .= " AND fd.container_id = ?";
    $financial_params[] = $container_id;
}
```

#### **تحديث استعلام التحويلات:**
```php
$transfers_query = "
    SELECT ct.*, 
           DATE(ct.transfer_date) as transaction_date,
           'transfer' as transaction_type,
           d.driver_name,
           d.vehicle_number
    FROM container_transfers ct 
    LEFT JOIN drivers d ON ct.driver_id = d.id
    WHERE ct.trader_id = ? 
    AND ct.transfer_date BETWEEN ? AND ?
";
$transfers_params = [$trader_id, $date_from, $date_to];

// إضافة فلتر الحاوية إذا تم تحديدها
if ($container_id > 0) {
    $transfers_query .= " AND ct.container_id = ?";
    $transfers_params[] = $container_id;
}
```

#### **عرض معلومات الحاوية المحددة:**
```php
// الحصول على معلومات الحاوية المحددة إذا تم تحديدها
$selected_container = null;
if ($container_id > 0) {
    $container_stmt = $db->prepare("
        SELECT c.*, cu.name as customer_name 
        FROM containers c 
        LEFT JOIN customers cu ON c.customer_id = cu.id 
        WHERE c.id = ? AND c.trader_id = ?
    ");
    $container_stmt->execute([$container_id, $trader_id]);
    $selected_container = $container_stmt->fetch();
}
```

#### **تحديث واجهة المستخدم:**
```html
<?php if ($selected_container): ?>
<div class="container-info mt-4">
    <h5 class="text-info mb-3">
        <i class="fas fa-shipping-fast me-2"></i>
        معلومات الحاوية المحددة
    </h5>
    <div class="row">
        <div class="col-md-6">
            <p><strong>رقم الحاوية:</strong> <?php echo htmlspecialchars($selected_container['container_number']); ?></p>
            <p><strong>الشركة:</strong> <?php echo htmlspecialchars($selected_container['customer_name'] ?: 'غير محدد'); ?></p>
        </div>
        <div class="col-md-6">
            <p><strong>سعر الشراء:</strong> <?php echo number_format($selected_container['purchase_price'], 0); ?> د.ع</p>
            <p><strong>سعر البيع:</strong> <?php echo number_format($selected_container['selling_price'], 0); ?> د.ع</p>
        </div>
    </div>
</div>
<?php endif; ?>
```

#### **تحديث عنوان التقرير:**
```html
<h1 class="mb-2">
    <i class="fas fa-file-invoice-dollar me-3"></i>
    كشف حساب التاجر
    <?php if ($selected_container): ?>
    <small class="d-block mt-1" style="font-size: 0.6em;">
        <i class="fas fa-shipping-fast me-1"></i>
        الحاوية: <?php echo htmlspecialchars($selected_container['container_number']); ?>
    </small>
    <?php endif; ?>
</h1>
<p class="mb-0 opacity-75">
    <?php if ($selected_container): ?>
        تقرير مفصل للمعاملات المرتبطة بالحاوية المحددة
    <?php else: ?>
        تقرير شامل لجميع المعاملات المالية
    <?php endif; ?>
</p>
```

## الميزات الجديدة

### 🎯 **فلترة ذكية للحاويات:**
1. **اختيار التاجر أولاً:** عند اختيار التاجر تظهر حاوياته فقط
2. **إخفاء الحاويات غير المرتبطة:** حاويات التجار الآخرين مخفية تلقائياً
3. **رسائل توضيحية:** رسائل واضحة لتوجيه المستخدم

### 🔍 **تقارير مفصلة:**
1. **تقرير شامل:** عند عدم اختيار حاوية محددة
2. **تقرير مخصص:** عند اختيار حاوية محددة
3. **معلومات الحاوية:** عرض تفاصيل الحاوية المحددة في التقرير

### 📊 **فلترة البيانات:**
- **الحاويات:** فلترة حسب الحاوية المحددة
- **المستندات المالية:** فلترة حسب الحاوية المرتبطة
- **التحويلات:** فلترة حسب الحاوية المحددة

### 🎨 **واجهة محسنة:**
- **تصميم متجاوب:** يعمل على جميع الأجهزة
- **رسائل توضيحية:** إرشادات واضحة للمستخدم
- **عرض معلومات إضافية:** تفاصيل الحاوية المحددة

## كيفية الاستخدام

### **للحصول على تقرير شامل:**
1. اذهب إلى: `index.php?page=trader_statement_select`
2. اختر التاجر من القائمة الأولى
3. اترك قائمة الحاويات فارغة
4. حدد التواريخ المطلوبة
5. اضغط "عرض كشف الحساب"

### **للحصول على تقرير مخصص لحاوية:**
1. اذهب إلى: `index.php?page=trader_statement_select`
2. اختر التاجر من القائمة الأولى
3. ستظهر حاوياته تلقائياً في القائمة الثانية
4. اختر الحاوية المطلوبة
5. حدد التواريخ المطلوبة
6. اضغط "عرض كشف الحساب"

### **البحث في القوائم:**
1. **في قائمة التجار:** اكتب اسم التاجر للبحث
2. **في قائمة الحاويات:** ابحث برقم الحاوية أو اسم الشركة

## الفوائد المحققة

### 📈 **دقة أكبر في التقارير:**
- تقارير مخصصة لحاويات محددة
- فصل المعاملات حسب الحاوية
- تتبع أفضل للأرباح والخسائر

### ⚡ **سرعة في الوصول للمعلومات:**
- فلترة تلقائية للحاويات
- عرض الحاويات ذات الصلة فقط
- بحث سريع في القوائم

### 💡 **سهولة الاستخدام:**
- تدفق منطقي وواضح
- رسائل توضيحية مفيدة
- واجهة بديهية

### 📊 **تحليل أفضل:**
- مقارنة أداء الحاويات المختلفة
- تتبع المعاملات المرتبطة بحاوية محددة
- تقارير أكثر تفصيلاً

## الاختبار والتحقق

### **نقاط الاختبار:**
1. **صفحة الاختيار:** `index.php?page=trader_statement_select`
2. **اختبار اختيار التاجر** ومشاهدة ظهور حاوياته
3. **اختبار البحث** في قائمة التجار والحاويات
4. **اختبار التقرير الشامل** (بدون اختيار حاوية)
5. **اختبار التقرير المخصص** (مع اختيار حاوية محددة)
6. **اختبار الطباعة** مع فلتر الحاوية

### **التحقق من البيانات:**
- ✅ ظهور الحاويات الصحيحة للتاجر المحدد
- ✅ إخفاء حاويات التجار الآخرين
- ✅ فلترة البيانات حسب الحاوية المحددة
- ✅ عرض معلومات الحاوية في التقرير
- ✅ عمل الرسائل التوضيحية بشكل صحيح

## الخلاصة

تم بنجاح إضافة نظام فلترة الحاويات حسب التاجر، مما يوفر:

- ✅ **فلترة تلقائية** للحاويات حسب التاجر المحدد
- ✅ **تقارير مخصصة** لحاويات محددة
- ✅ **واجهة محسنة** مع رسائل توضيحية
- ✅ **بحث متقدم** في جميع القوائم
- ✅ **عرض معلومات إضافية** للحاوية المحددة
- ✅ **تحليل أدق** للمعاملات المالية

النظام الآن يوفر مرونة أكبر في إنشاء التقارير وتحليل البيانات! 🎉

---

**تاريخ التحديث:** 2024-12-19  
**الإصدار:** 3.0  
**المطور:** Augment Agent
