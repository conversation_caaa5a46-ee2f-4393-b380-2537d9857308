<?php
// التحقق من الصلاحيات
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// التحقق من صلاحية الوصول للتحويلات
if (!hasPermission('transfers')) {
    $_SESSION['error'] = 'ليس لديك صلاحية الوصول إلى هذه الصفحة';
    header('Location: index.php?page=unauthorized');
    exit;
}

// التحقق من وجود معرف التحويل
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = 'معرف التحويل مطلوب';
    header('Location: index.php?page=transfers');
    exit;
}

$transferId = (int)$_GET['id'];

try {
    // جلب بيانات التحويل
    $transferStmt = $db->prepare("
        SELECT 
            ct.*,
            t.name as trader_name,
            t.phone as trader_phone,
            d.driver_name,
            d.phone as driver_phone,
            d.vehicle_number
        FROM container_transfers ct
        LEFT JOIN traders t ON ct.trader_id = t.id
        LEFT JOIN drivers d ON ct.driver_id = d.id
        WHERE ct.id = ?
    ");
    $transferStmt->execute([$transferId]);
    $transfer = $transferStmt->fetch();
    
    if (!$transfer) {
        $_SESSION['error'] = 'التحويل غير موجود';
        header('Location: index.php?page=transfers');
        exit;
    }
    
    // جلب الحاويات
    $containersStmt = $db->prepare("
        SELECT 
            c.container_number,
            c.content,
            c.container_type,
            tc.transfer_fee
        FROM transfer_containers tc
        JOIN containers c ON tc.container_id = c.id
        WHERE tc.transfer_id = ?
        ORDER BY c.container_number
    ");
    $containersStmt->execute([$transferId]);
    $containers = $containersStmt->fetchAll();
    
} catch (PDOException $e) {
    $_SESSION['error'] = 'حدث خطأ أثناء جلب بيانات التحويل';
    header('Location: index.php?page=transfers');
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سند تحويل - <?php echo htmlspecialchars($transfer['transfer_number']); ?></title>
    
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            background: #fff;
            padding: 20px;
        }
        
        .print-btn {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            z-index: 1000;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .header h2 {
            font-size: 18px;
            color: #666;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .info-box {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        
        .info-box h3 {
            font-size: 16px;
            margin-bottom: 10px;
            color: #007bff;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .label {
            font-weight: bold;
        }
        
        .containers-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .containers-table th,
        .containers-table td {
            border: 1px solid #333;
            padding: 10px;
            text-align: right;
        }
        
        .containers-table th {
            background: #f5f5f5;
            font-weight: bold;
        }
        
        .total-row {
            background: #f0f8ff;
            font-weight: bold;
        }
        
        .signatures {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
            margin-top: 50px;
        }
        
        .signature-box {
            text-align: center;
            border: 1px dashed #666;
            padding: 30px 10px;
            border-radius: 5px;
        }
        
        .signature-label {
            font-weight: bold;
            margin-bottom: 30px;
        }
        
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 30px;
            padding-top: 10px;
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 12px;
            color: #666;
        }
        
        @media print {
            .print-btn { display: none !important; }
            body { padding: 0; }
            .container { max-width: none; }
            @page { size: A4; margin: 15mm; }
        }
        
        @media (max-width: 768px) {
            .info-grid { grid-template-columns: 1fr; }
            .signatures { grid-template-columns: 1fr; }
            .containers-table { font-size: 12px; }
        }
    </style>
</head>

<body>
    <button class="print-btn" onclick="window.print()">طباعة</button>

    <div class="container">
        <!-- الرأس -->
        <div class="header">
            <h1><?php echo COMPANY_NAME; ?></h1>
            <h2>سند تحويل حاويات</h2>
            <p>رقم التحويل: <strong><?php echo htmlspecialchars($transfer['transfer_number']); ?></strong></p>
            <p>التاريخ: <?php echo date('Y-m-d', strtotime($transfer['transfer_date'])); ?></p>
        </div>

        <!-- معلومات التحويل -->
        <div class="info-grid">
            <div class="info-box">
                <h3>معلومات التاجر</h3>
                <div class="info-row">
                    <span class="label">الاسم:</span>
                    <span><?php echo htmlspecialchars($transfer['trader_name'] ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-row">
                    <span class="label">الهاتف:</span>
                    <span><?php echo htmlspecialchars($transfer['trader_phone'] ?? 'غير محدد'); ?></span>
                </div>
            </div>

            <div class="info-box">
                <h3>معلومات السائق</h3>
                <div class="info-row">
                    <span class="label">الاسم:</span>
                    <span><?php echo htmlspecialchars($transfer['driver_name'] ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-row">
                    <span class="label">الهاتف:</span>
                    <span><?php echo htmlspecialchars($transfer['driver_phone'] ?? 'غير محدد'); ?></span>
                </div>
                <div class="info-row">
                    <span class="label">رقم المركبة:</span>
                    <span><?php echo htmlspecialchars($transfer['vehicle_number'] ?? 'غير محدد'); ?></span>
                </div>
            </div>
        </div>

        <div class="info-grid">
            <div class="info-box">
                <h3>تفاصيل التحويل</h3>
                <div class="info-row">
                    <span class="label">عدد الحاويات:</span>
                    <span><?php echo $transfer['total_containers']; ?> حاوية</span>
                </div>
                <div class="info-row">
                    <span class="label">المبلغ الإجمالي:</span>
                    <span><?php echo number_format($transfer['total_amount'], 0); ?> د.ع</span>
                </div>
                <div class="info-row">
                    <span class="label">الحالة:</span>
                    <span>
                        <?php
                        $statusLabels = [
                            'pending' => 'قيد الانتظار',
                            'in_progress' => 'قيد التنفيذ',
                            'completed' => 'مكتمل',
                            'cancelled' => 'ملغى'
                        ];
                        echo $statusLabels[$transfer['status']] ?? $transfer['status'];
                        ?>
                    </span>
                </div>
            </div>

            <div class="info-box">
                <h3>مواقع النقل</h3>
                <div class="info-row">
                    <span class="label">موقع الاستلام:</span>
                    <span><?php echo htmlspecialchars($transfer['pickup_location']); ?></span>
                </div>
                <div class="info-row">
                    <span class="label">موقع التسليم:</span>
                    <span><?php echo htmlspecialchars($transfer['delivery_location']); ?></span>
                </div>
            </div>
        </div>

        <!-- جدول الحاويات -->
        <?php if (!empty($containers)): ?>
        <table class="containers-table">
            <thead>
                <tr>
                    <th width="5%">#</th>
                    <th width="25%">رقم الحاوية</th>
                    <th width="35%">المحتوى</th>
                    <th width="15%">النوع</th>
                    <th width="20%">رسوم النقل</th>
                </tr>
            </thead>
            <tbody>
                <?php 
                $totalFees = 0;
                foreach ($containers as $index => $container): 
                    $totalFees += $container['transfer_fee'];
                ?>
                <tr>
                    <td><?php echo $index + 1; ?></td>
                    <td><strong><?php echo htmlspecialchars($container['container_number']); ?></strong></td>
                    <td><?php echo htmlspecialchars($container['content'] ?? 'غير محدد'); ?></td>
                    <td><?php echo htmlspecialchars($container['container_type'] ?? 'غير محدد'); ?></td>
                    <td><?php echo number_format($container['transfer_fee'], 0); ?> د.ع</td>
                </tr>
                <?php endforeach; ?>
                
                <tr class="total-row">
                    <td colspan="4"><strong>الإجمالي</strong></td>
                    <td><strong><?php echo number_format($totalFees, 0); ?> د.ع</strong></td>
                </tr>
            </tbody>
        </table>
        <?php endif; ?>

        <!-- التوقيعات -->
        <div class="signatures">
            <div class="signature-box">
                <div class="signature-label">توقيع التاجر</div>
                <div class="signature-line">
                    <?php echo htmlspecialchars($transfer['trader_name'] ?? ''); ?>
                </div>
            </div>
            
            <div class="signature-box">
                <div class="signature-label">توقيع السائق</div>
                <div class="signature-line">
                    <?php echo htmlspecialchars($transfer['driver_name'] ?? ''); ?>
                </div>
            </div>
            
            <div class="signature-box">
                <div class="signature-label">توقيع المسؤول</div>
                <div class="signature-line">
                    الإدارة
                </div>
            </div>
        </div>

        <!-- التذييل -->
        <div class="footer">
            <p>تم إنشاء هذا السند في: <?php echo date('Y-m-d H:i:s'); ?></p>
            <p>هذا السند صالح قانونياً ولا يحتاج إلى توقيع إضافي</p>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند الحاجة
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('auto_print') === '1') {
            window.onload = function() {
                setTimeout(() => {
                    window.print();
                }, 1000);
            };
        }
        
        // اختصار لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>
</body>
</html>
