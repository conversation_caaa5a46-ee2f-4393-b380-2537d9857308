<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة التجار</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .info { color: blue; background: #d1ecf1; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .warning { color: orange; background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>إصلاح مشكلة التجار في نظام التحويلات</h1>
    
    <?php
    require_once 'config/config.php';
    
    try {
        $db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='step'>";
        echo "<h3>الخطوة 1: فحص الجداول الموجودة</h3>";
        
        // فحص الجداول الموجودة
        $stmt = $db->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<div class='info'>الجداول الموجودة في قاعدة البيانات:</div>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
        
        $hasTraders = in_array('traders', $tables);
        $hasCustomers = in_array('customers', $tables);
        
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h3>الخطوة 2: فحص هيكل الجداول</h3>";
        
        if ($hasCustomers) {
            echo "<div class='success'>✓ جدول customers موجود</div>";
            
            // فحص هيكل جدول customers
            $stmt = $db->query("DESCRIBE customers");
            $customerColumns = $stmt->fetchAll();
            
            echo "<h4>أعمدة جدول customers:</h4>";
            echo "<table>";
            echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            foreach ($customerColumns as $column) {
                echo "<tr>";
                echo "<td>" . $column['Field'] . "</td>";
                echo "<td>" . $column['Type'] . "</td>";
                echo "<td>" . $column['Null'] . "</td>";
                echo "<td>" . $column['Key'] . "</td>";
                echo "<td>" . $column['Default'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // عدد العملاء
            $stmt = $db->query("SELECT COUNT(*) as count FROM customers");
            $customerCount = $stmt->fetch()['count'];
            echo "<div class='info'>عدد العملاء: $customerCount</div>";
        }
        
        if ($hasTraders) {
            echo "<div class='success'>✓ جدول traders موجود</div>";
            
            // فحص هيكل جدول traders
            $stmt = $db->query("DESCRIBE traders");
            $traderColumns = $stmt->fetchAll();
            
            echo "<h4>أعمدة جدول traders:</h4>";
            echo "<table>";
            echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            foreach ($traderColumns as $column) {
                echo "<tr>";
                echo "<td>" . $column['Field'] . "</td>";
                echo "<td>" . $column['Type'] . "</td>";
                echo "<td>" . $column['Null'] . "</td>";
                echo "<td>" . $column['Key'] . "</td>";
                echo "<td>" . $column['Default'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // عدد التجار
            $stmt = $db->query("SELECT COUNT(*) as count FROM traders");
            $traderCount = $stmt->fetch()['count'];
            echo "<div class='info'>عدد التجار: $traderCount</div>";
        } else {
            echo "<div class='warning'>✗ جدول traders غير موجود</div>";
        }
        
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h3>الخطوة 3: إنشاء جدول traders إذا لم يكن موجوداً</h3>";
        
        if (!$hasTraders) {
            echo "<div class='info'>سيتم إنشاء جدول traders...</div>";
            
            $createTradersSQL = "
            CREATE TABLE IF NOT EXISTS `traders` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `name` varchar(100) NOT NULL,
              `contact_person` varchar(100) DEFAULT NULL,
              `phone` varchar(20) DEFAULT NULL,
              `email` varchar(100) DEFAULT NULL,
              `address` text DEFAULT NULL,
              `tax_number` varchar(50) DEFAULT NULL,
              `notes` text DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";
            
            try {
                $db->exec($createTradersSQL);
                echo "<div class='success'>✓ تم إنشاء جدول traders بنجاح</div>";
                
                // نسخ البيانات من customers إلى traders
                if ($hasCustomers) {
                    $copySQL = "
                    INSERT INTO traders (name, contact_person, phone, email, address, tax_number, notes)
                    SELECT name, contact_person, phone, email, address, tax_number, notes
                    FROM customers
                    ";
                    
                    $stmt = $db->prepare($copySQL);
                    $stmt->execute();
                    $copiedCount = $stmt->rowCount();
                    echo "<div class='success'>✓ تم نسخ $copiedCount سجل من customers إلى traders</div>";
                }
                
            } catch (PDOException $e) {
                echo "<div class='error'>خطأ في إنشاء جدول traders: " . $e->getMessage() . "</div>";
            }
        } else {
            echo "<div class='success'>جدول traders موجود بالفعل</div>";
            
            // التحقق من وجود بيانات
            $stmt = $db->query("SELECT COUNT(*) as count FROM traders");
            $traderCount = $stmt->fetch()['count'];
            
            if ($traderCount == 0 && $hasCustomers) {
                echo "<div class='warning'>جدول traders فارغ. سيتم نسخ البيانات من customers...</div>";
                
                $copySQL = "
                INSERT INTO traders (name, contact_person, phone, email, address, tax_number, notes)
                SELECT name, contact_person, phone, email, address, tax_number, notes
                FROM customers
                ";
                
                try {
                    $stmt = $db->prepare($copySQL);
                    $stmt->execute();
                    $copiedCount = $stmt->rowCount();
                    echo "<div class='success'>✓ تم نسخ $copiedCount سجل من customers إلى traders</div>";
                } catch (PDOException $e) {
                    echo "<div class='error'>خطأ في نسخ البيانات: " . $e->getMessage() . "</div>";
                }
            }
        }
        
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h3>الخطوة 4: إضافة بيانات تجريبية إذا لزم الأمر</h3>";
        
        $stmt = $db->query("SELECT COUNT(*) as count FROM traders");
        $finalTraderCount = $stmt->fetch()['count'];
        
        if ($finalTraderCount < 3) {
            echo "<div class='info'>عدد التجار قليل ($finalTraderCount). سيتم إضافة بيانات تجريبية...</div>";
            
            $testTraders = [
                ['شركة التجارة العراقية', 'أحمد محمد', '07901234567', '<EMAIL>', 'بغداد - الكرادة'],
                ['مؤسسة النور للاستيراد', 'خالد العبدالله', '07912345678', '<EMAIL>', 'البصرة - المعقل'],
                ['شركة المستقبل التجارية', 'سعيد الأحمد', '07923456789', '<EMAIL>', 'أربيل - عنكاوا'],
                ['مؤسسة الصفا للتجارة', 'فهد السالم', '07934567890', '<EMAIL>', 'النجف - المركز'],
                ['شركة الخليج للاستيراد', 'محمد علي', '07945678901', '<EMAIL>', 'كربلاء - المركز']
            ];
            
            $addedCount = 0;
            foreach ($testTraders as $trader) {
                try {
                    $stmt = $db->prepare("
                        INSERT IGNORE INTO traders (name, contact_person, phone, email, address) 
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    
                    $stmt->execute($trader);
                    
                    if ($stmt->rowCount() > 0) {
                        $addedCount++;
                    }
                } catch (Exception $e) {
                    echo "<div class='error'>خطأ في إضافة التاجر " . $trader[0] . ": " . $e->getMessage() . "</div>";
                }
            }
            
            echo "<div class='success'>تم إضافة $addedCount تاجر تجريبي</div>";
        } else {
            echo "<div class='success'>يوجد $finalTraderCount تاجر في النظام</div>";
        }
        
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h3>الخطوة 5: اختبار جلب التجار</h3>";
        
        try {
            $stmt = $db->prepare("SELECT id, name, contact_person, phone FROM traders ORDER BY name LIMIT 5");
            $stmt->execute();
            $traders = $stmt->fetchAll();
            
            if (count($traders) > 0) {
                echo "<div class='success'>✓ تم جلب التجار بنجاح</div>";
                echo "<table>";
                echo "<tr><th>المعرف</th><th>الاسم</th><th>الشخص المسؤول</th><th>الهاتف</th></tr>";
                foreach ($traders as $trader) {
                    echo "<tr>";
                    echo "<td>" . $trader['id'] . "</td>";
                    echo "<td>" . htmlspecialchars($trader['name']) . "</td>";
                    echo "<td>" . htmlspecialchars($trader['contact_person'] ?? 'غير محدد') . "</td>";
                    echo "<td>" . htmlspecialchars($trader['phone'] ?? 'غير محدد') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<div class='error'>لا توجد بيانات تجار</div>";
            }
        } catch (PDOException $e) {
            echo "<div class='error'>خطأ في جلب التجار: " . $e->getMessage() . "</div>";
        }
        
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h3>✅ تم إصلاح مشكلة التجار بنجاح!</h3>";
        echo "<div class='success'>يمكنك الآن استخدام نظام التحويلات</div>";
        echo "<p><a href='index.php?page=transfers&action=add' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إضافة تحويل جديد</a></p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
    }
    ?>
</body>
</html>
