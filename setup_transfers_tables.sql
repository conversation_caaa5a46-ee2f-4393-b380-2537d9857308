-- إن<PERSON>اء جداول نظام التحويلات
-- يجب تشغيل هذا الملف لإنشاء الجداول المطلوبة

-- جدول التجار (إذا لم يكن موجوداً)
CREATE TABLE IF NOT EXISTS `traders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `contact_person` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `tax_number` varchar(50) DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول السائقين
CREATE TABLE IF NOT EXISTS `drivers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `driver_name` varchar(100) NOT NULL,
  `license_number` varchar(50) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `national_id` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `vehicle_type` varchar(50) DEFAULT NULL,
  `vehicle_number` varchar(20) DEFAULT NULL,
  `license_expiry` date DEFAULT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `daily_rate` decimal(10,2) DEFAULT 0.00,
  `per_container_rate` decimal(10,2) DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `license_number` (`license_number`),
  KEY `idx_driver_status` (`status`),
  KEY `idx_driver_phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تحويلات الحاويات الرئيسي
CREATE TABLE IF NOT EXISTS `container_transfers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transfer_number` varchar(50) NOT NULL,
  `transfer_date` date NOT NULL,
  `trader_id` int(11) NOT NULL,
  `driver_id` int(11) NOT NULL,
  `pickup_location` varchar(255) NOT NULL DEFAULT 'المستودع الرئيسي',
  `delivery_location` varchar(255) NOT NULL DEFAULT 'موقع التسليم',
  `total_containers` int(11) NOT NULL DEFAULT 0,
  `total_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
  `currency` varchar(10) DEFAULT 'IQD',
  `status` enum('pending','in_progress','completed','cancelled') DEFAULT 'pending',
  `pickup_time` datetime DEFAULT NULL,
  `delivery_time` datetime DEFAULT NULL,
  `estimated_duration` int(11) DEFAULT NULL,
  `actual_duration` int(11) DEFAULT NULL,
  `fuel_cost` decimal(10,2) DEFAULT 0.00,
  `driver_payment` decimal(10,2) DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `transfer_number` (`transfer_number`),
  KEY `idx_transfer_date` (`transfer_date`),
  KEY `idx_transfer_status` (`status`),
  KEY `idx_trader_id` (`trader_id`),
  KEY `idx_driver_id` (`driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تفاصيل تحويل الحاويات
CREATE TABLE IF NOT EXISTS `transfer_containers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transfer_id` int(11) NOT NULL,
  `container_id` int(11) NOT NULL,
  `transfer_fee` decimal(10,2) NOT NULL DEFAULT 0.00,
  `pickup_time` datetime DEFAULT NULL,
  `delivery_time` datetime DEFAULT NULL,
  `container_condition` enum('good','damaged','sealed','unsealed') DEFAULT 'good',
  `notes` varchar(500) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_transfer_container` (`transfer_id`,`container_id`),
  KEY `idx_transfer_id` (`transfer_id`),
  KEY `idx_container_id` (`container_id`),
  CONSTRAINT `transfer_containers_ibfk_1` FOREIGN KEY (`transfer_id`) REFERENCES `container_transfers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `transfer_containers_ibfk_2` FOREIGN KEY (`container_id`) REFERENCES `containers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تتبع حالة التحويلات
CREATE TABLE IF NOT EXISTS `transfer_tracking` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transfer_id` int(11) NOT NULL,
  `status` enum('created','pickup_scheduled','picked_up','in_transit','delivered','delayed','cancelled') NOT NULL,
  `update_time` datetime NOT NULL,
  `location` varchar(255) DEFAULT NULL,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `updated_by` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_transfer_id` (`transfer_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `transfer_tracking_ibfk_1` FOREIGN KEY (`transfer_id`) REFERENCES `container_transfers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول مصاريف التحويل الإضافية
CREATE TABLE IF NOT EXISTS `transfer_expenses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `transfer_id` int(11) NOT NULL,
  `expense_type` enum('fuel','toll','maintenance','parking','other') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(10) DEFAULT 'IQD',
  `description` varchar(255) DEFAULT NULL,
  `receipt_number` varchar(100) DEFAULT NULL,
  `expense_date` datetime NOT NULL,
  `created_by` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_transfer_id` (`transfer_id`),
  KEY `idx_expense_type` (`expense_type`),
  CONSTRAINT `transfer_expenses_ibfk_1` FOREIGN KEY (`transfer_id`) REFERENCES `container_transfers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج بيانات تجريبية للتجار
INSERT IGNORE INTO `traders` (`id`, `name`, `contact_person`, `phone`, `email`, `address`, `status`) VALUES
(1, 'شركة التجارة العراقية', 'أحمد محمد', '07901234567', '<EMAIL>', 'بغداد - الكرادة', 'active'),
(2, 'مؤسسة النور للاستيراد', 'خالد العبدالله', '07912345678', '<EMAIL>', 'البصرة - المعقل', 'active'),
(3, 'شركة المستقبل التجارية', 'سعيد الأحمد', '07923456789', '<EMAIL>', 'أربيل - عنكاوا', 'active');

-- إدراج بيانات تجريبية للسائقين
INSERT IGNORE INTO `drivers` (`id`, `driver_name`, `license_number`, `phone`, `national_id`, `address`, `vehicle_type`, `vehicle_number`, `status`, `daily_rate`, `per_container_rate`) VALUES
(1, 'أحمد محمد علي', 'DL001234567', '07901234567', '19850101001', 'بغداد - الكرادة', 'شاحنة كبيرة', '123-بغداد', 'active', 150000.00, 25000.00),
(2, 'عمر حسن جاسم', 'DL002345678', '07912345678', '19900215002', 'البصرة - المعقل', 'شاحنة متوسطة', '456-بصرة', 'active', 120000.00, 20000.00),
(3, 'محمد عبدالله يوسف', 'DL003456789', '07923456789', '19880310003', 'أربيل - عنكاوا', 'شاحنة صغيرة', '789-أربيل', 'active', 100000.00, 15000.00);

-- إضافة صلاحيات التحويلات إذا لم تكن موجودة
INSERT IGNORE INTO `permissions` (`name`, `permission_key`, `description`) VALUES
('إدارة التحويلات', 'transfers', 'إدارة تحويلات الحاويات وتتبعها'),
('إدارة السائقين', 'drivers', 'إدارة بيانات السائقين ومركباتهم');

-- منح الصلاحيات للمدير
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) 
SELECT 1, id FROM permissions WHERE permission_key IN ('transfers', 'drivers');

-- منح الصلاحيات للمدير العادي
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) 
SELECT 2, id FROM permissions WHERE permission_key IN ('transfers', 'drivers');
