# نظام كشف حساب التاجر

## نظرة عامة
تم إنشاء نظام شامل لكشف حساب التاجر يتضمن جميع المعاملات المالية والعمليات المتعلقة بالتاجر من حاويات ومستندات مالية وتحويلات.

## الملفات المنشأة

### 1. صفحة كشف الحساب (`pages/trader_statement.php`)
صفحة شاملة تعرض:
- **بيانات التاجر الأساسية**
- **الرصيد الافتتاحي** (من حقل `price` في جدول `traders`)
- **جميع المعاملات المالية** مرتبة حسب التاريخ
- **الرصيد الجاري** بعد كل معاملة
- **ملخص الحسابات** والإحصائيات

### 2. صفحة قائمة التجار (`pages/traders_list.php`)
صفحة محسنة لعرض التجار مع:
- **إحصائيات شاملة** للتجار
- **روابط مباشرة** لكشف الحساب
- **فلاتر متقدمة** للبحث والترتيب
- **معلومات تفصيلية** لكل تاجر

## هيكل البيانات

### جدول التجار (`traders`)
```sql
- id: معرف التاجر
- name: اسم التاجر
- contact_person: الشخص المسؤول
- phone: رقم الهاتف
- email: البريد الإلكتروني
- address: العنوان
- tax_number: الرقم الضريبي
- price: الدائن على التاجر (الرصيد الافتتاحي)
- active: حالة التاجر (نشط/غير نشط)
- created_at: تاريخ الإنشاء
```

### المعاملات المالية المتضمنة

#### 1. الحاويات (`containers`)
```sql
- container_number: رقم الحاوية
- purchase_price: سعر الشراء (مدين)
- selling_price: سعر البيع (دائن)
- content: محتوى الحاوية
- status: حالة الحاوية
- entry_date: تاريخ الدخول
```

#### 2. المستندات المالية (`financial_documents`)
```sql
- document_number: رقم المستند
- document_type: نوع المستند
  * receipt: سند قبض (دائن)
  * payment: سند دفع (مدين)
  * expense: سند صرف (مدين)
  * transfer: حوالة مالية (دائن)
  * capital: رأس مال (دائن)
- amount: المبلغ
- description: الوصف
```

#### 3. التحويلات (`container_transfers`)
```sql
- transfer_number: رقم التحويل
- total_amount: إجمالي المبلغ (مدين)
- total_containers: عدد الحاويات
- driver_name: اسم السائق
- transfer_date: تاريخ التحويل
```

## منطق الحسابات

### الرصيد الافتتاحي
```php
$opening_balance = (float)$trader['price']; // من جدول traders
```

### حساب المدين والدائن
```php
// الحاويات
$debit = $container['purchase_price'];   // تكلفة الشراء
$credit = $container['selling_price'];   // سعر البيع

// المستندات المالية
switch ($document_type) {
    case 'receipt':  $credit = $amount; break;  // سند قبض
    case 'payment':  $debit = $amount; break;   // سند دفع
    case 'expense':  $debit = $amount; break;   // سند صرف
    case 'transfer': $credit = $amount; break;  // حوالة مالية
    case 'capital':  $credit = $amount; break;  // رأس مال
}

// التحويلات
$debit = $transfer['total_amount']; // تكلفة التحويل
```

### حساب الرصيد الجاري
```php
$running_balance = $opening_balance + $credit - $debit;
```

## الميزات الرئيسية

### 1. كشف الحساب الشامل
- ✅ **عرض جميع المعاملات** في جدول واحد مرتب حسب التاريخ
- ✅ **الرصيد الجاري** بعد كل معاملة
- ✅ **تصنيف المعاملات** بألوان مختلفة
- ✅ **ملخص الحسابات** مع الإجماليات
- ✅ **معلومات التاجر** الكاملة

### 2. الفلترة والبحث
- ✅ **اختيار التاجر** من قائمة منسدلة
- ✅ **تحديد الفترة الزمنية** (من تاريخ - إلى تاريخ)
- ✅ **فلترة متقدمة** في قائمة التجار
- ✅ **بحث شامل** في بيانات التجار

### 3. الطباعة والتصدير
- ✅ **وضع الطباعة** المحسن
- ✅ **تصميم مناسب للطباعة** بدون عناصر التنقل
- ✅ **طباعة تلقائية** عند فتح الرابط
- ✅ **تنسيق احترافي** للتقارير

### 4. التصميم والواجهة
- ✅ **تصميم متجاوب** لجميع الأجهزة
- ✅ **ألوان مميزة** لأنواع المعاملات المختلفة
- ✅ **أيقونات واضحة** ومعبرة
- ✅ **تأثيرات بصرية** جذابة

## طريقة الاستخدام

### 1. الوصول لكشف الحساب
```
http://localhost/ccis_appis/index.php?page=trader_statement&trader_id=1
```

### 2. طباعة كشف الحساب
```
http://localhost/ccis_appis/index.php?page=trader_statement&trader_id=1&print=1
```

### 3. عرض قائمة التجار
```
http://localhost/ccis_appis/index.php?page=traders_list
```

### 4. فلترة حسب الفترة الزمنية
```
http://localhost/ccis_appis/index.php?page=trader_statement&trader_id=1&date_from=2024-01-01&date_to=2024-12-31
```

## أمثلة على المعاملات

### مثال 1: حاوية
```
التاريخ: 2024-01-15
النوع: حاوية
الوصف: حاوية رقم CONT-001
المدين: 1,000,000 د.ع (سعر الشراء)
الدائن: 1,200,000 د.ع (سعر البيع)
الرصيد: 200,000 د.ع (ربح)
```

### مثال 2: سند قبض
```
التاريخ: 2024-01-20
النوع: مستند مالي
الوصف: سند قبض رقم REC-001
المدين: 0
الدائن: 500,000 د.ع
الرصيد: 700,000 د.ع
```

### مثال 3: تحويل
```
التاريخ: 2024-01-25
النوع: تحويل
الوصف: تحويل رقم TR-001
المدين: 50,000 د.ع (تكلفة التحويل)
الدائن: 0
الرصيد: 650,000 د.ع
```

## الإحصائيات المتاحة

### في كشف الحساب
- **الرصيد الافتتاحي**: المبلغ المرحل من الفترات السابقة
- **إجمالي الدائن**: مجموع جميع المبالغ الدائنة
- **إجمالي المدين**: مجموع جميع المبالغ المدينة
- **الرصيد النهائي**: الرصيد بعد جميع المعاملات
- **عدد الحاويات**: إجمالي الحاويات في الفترة
- **عدد التحويلات**: إجمالي التحويلات في الفترة

### في قائمة التجار
- **إجمالي التجار**: العدد الكلي للتجار
- **التجار النشطين**: التجار بحالة نشط
- **التجار غير النشطين**: التجار بحالة غير نشط
- **إجمالي الديون**: مجموع المبالغ المستحقة على جميع التجار

## التحسينات المستقبلية

### 1. ميزات إضافية
- [ ] تصدير إلى Excel/PDF
- [ ] إرسال كشف الحساب بالبريد الإلكتروني
- [ ] مقارنة كشوف حساب متعددة
- [ ] رسوم بيانية للمعاملات

### 2. تحسينات الأداء
- [ ] تخزين مؤقت للبيانات
- [ ] فهرسة محسنة لقاعدة البيانات
- [ ] تحميل البيانات بشكل تدريجي

### 3. ميزات متقدمة
- [ ] تنبيهات للمستحقات
- [ ] تقارير دورية تلقائية
- [ ] ربط مع أنظمة المحاسبة الخارجية

## الخلاصة

تم إنشاء نظام شامل ومتكامل لكشف حساب التاجر يوفر:

✅ **رؤية شاملة** لجميع المعاملات المالية
✅ **حسابات دقيقة** للأرصدة والمستحقات  
✅ **تصميم احترافي** قابل للطباعة
✅ **سهولة في الاستخدام** والتنقل
✅ **مرونة في الفلترة** والبحث
✅ **تناسق مع تصميم النظام** العام

النظام جاهز للاستخدام ويمكن الوصول إليه من خلال الروابط المذكورة أعلاه! 🎉
