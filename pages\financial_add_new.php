<?php
// معالجة طلبات AJAX - يجب أن تكون في أول الملف
if (isset($_GET['ajax']) && $_GET['ajax'] == 'get_containers' && isset($_GET['customer_id'])) {
    // تضمين ملف الإعدادات
    require_once '../config/config.php';

    // تنظيف أي محتوى قد يكون موجودًا
    if (ob_get_length()) ob_clean();

    $traderId = (int)$_GET['customer_id']; // customer_id هنا يشير إلى trader_id
    
    try {
        // بناء الاستعلام الأساسي
        $sql = "SELECT id, container_number, entry_date, exit_date, purchase_price, selling_price, 
                       content, status, size, notes, created_at
                FROM containers 
                WHERE trader_id = ? AND status != 'cancelled'";
        
        $params = [$traderId];
        
        // إضافة فلاتر إضافية إذا كانت موجودة
        if (!empty($_GET['search'])) {
            $sql .= " AND container_number LIKE ?";
            $params[] = '%' . $_GET['search'] . '%';
        }
        
        if (!empty($_GET['date_from'])) {
            $sql .= " AND entry_date >= ?";
            $params[] = $_GET['date_from'];
        }
        
        if (!empty($_GET['date_to'])) {
            $sql .= " AND entry_date <= ?";
            $params[] = $_GET['date_to'];
        }
        
        if (!empty($_GET['status'])) {
            $sql .= " AND status = ?";
            $params[] = $_GET['status'];
        }
        
        $sql .= " ORDER BY entry_date DESC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $containers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تنسيق البيانات
        foreach ($containers as &$container) {
            $container['id'] = (int)$container['id'];
            $container['purchase_price'] = (float)$container['purchase_price'];
            $container['selling_price'] = (float)$container['selling_price'];
            $container['entry_date'] = $container['entry_date'] ?: null;
            $container['exit_date'] = $container['exit_date'] ?: null;
        }
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'trader_id' => $traderId,
            'containers' => $containers,
            'count' => count($containers),
            'query_params' => $params,
            'sql' => $sql
        ]);
        exit;
    } catch (PDOException $e) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false, 
            'error' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
            'code' => $e->getCode()
        ]);
        exit;
    }
}

// التحقق من الصلاحيات
if (!hasPermission('financial')) {
    $_SESSION['error'] = 'ليس لديك صلاحية الوصول إلى هذه الصفحة';
    echo '<script>window.location.href = "index.php?page=unauthorized";</script>';
    return;
}

// تحديد نوع المستند
$documentType = isset($_GET['type']) ? $_GET['type'] : '';
$validTypes = ['receipt', 'payment', 'expense', 'transfer', 'capital', 'profit_distribution'];
if (!in_array($documentType, $validTypes)) {
    $_SESSION['error'] = 'نوع المستند غير صحيح';
    echo '<script>window.location.href = "index.php?page=financial";</script>';
    return;
}

// دالة لإنشاء رقم مستند جديد
function generateDocumentNumber($db, $type) {
    $prefix = '';

    switch ($type) {
        case 'receipt':
            $prefix = 'REC';
            break;
        case 'payment':
            $prefix = 'PAY';
            break;
        case 'expense':
            $prefix = 'EXP';
            break;
        case 'transfer':
            $prefix = 'TRF';
            break;
        case 'capital':
            $prefix = 'CAP';
            break;
        case 'profit_distribution':
            $prefix = 'PRF';
            break;
        default:
            $prefix = 'DOC';
            break;
    }

    // الحصول على آخر رقم مستند
    $stmt = $db->prepare("
        SELECT document_number
        FROM financial_documents
        WHERE document_type = :type
        ORDER BY id DESC
        LIMIT 1
    ");
    $stmt->execute(['type' => $type]);
    $lastNumber = $stmt->fetch()['document_number'] ?? '';

    // استخراج الرقم من آخر مستند
    $lastSerial = 0;
    if (!empty($lastNumber)) {
        preg_match('/' . $prefix . '(\d+)/', $lastNumber, $matches);
        if (isset($matches[1])) {
            $lastSerial = (int)$matches[1];
        }
    }

    // إنشاء رقم جديد
    $newSerial = $lastSerial + 1;
    $paddedSerial = str_pad($newSerial, 6, '0', STR_PAD_LEFT);

    return $prefix . $paddedSerial;
}

// معالجة النموذج عند الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $document = [
        'document_type' => $documentType,
        'amount' => !empty($_POST['amount']) ? (float)$_POST['amount'] : 0,
        'document_date' => !empty($_POST['document_date']) ? $_POST['document_date'] : '',
        'customer_id' => !empty($_POST['customer_id']) ? (int)$_POST['customer_id'] : null,
        'description' => $_POST['description'] ?? '',
        'reference_number' => $_POST['reference_number'] ?? ''
    ];

    // الحصول على الحاويات المحددة
    $selectedContainers = isset($_POST['container_ids']) && is_array($_POST['container_ids']) 
                         ? array_map('intval', $_POST['container_ids']) 
                         : [];

    $errors = [];

    // التحقق من صحة البيانات
    if (empty($document['amount']) || $document['amount'] <= 0) {
        $errors['amount'] = 'يرجى إدخال مبلغ صحيح';
    }

    if (empty($document['document_date'])) {
        $errors['document_date'] = 'يرجى إدخال التاريخ';
    }

    if (empty($document['description'])) {
        $errors['description'] = 'يرجى إدخال الوصف';
    }

    // إذا لم تكن هناك أخطاء، حفظ البيانات
    if (empty($errors)) {
        try {
            $db->beginTransaction();

            // إنشاء رقم مستند جديد
            $document['document_number'] = generateDocumentNumber($db, $document['document_type']);

            // إضافة مستند جديد (بدون container_id في الجدول الرئيسي)
            $stmt = $db->prepare("
                INSERT INTO financial_documents (
                    document_number, document_type, amount, document_date, customer_id,
                    description, reference_number, created_by, created_at
                ) VALUES (
                    :document_number, :document_type, :amount, :document_date, :customer_id,
                    :description, :reference_number, :created_by, NOW()
                )
            ");

            $params = $document;
            $params['created_by'] = getCurrentUserId();
            $stmt->execute($params);

            // الحصول على معرف المستند المضاف
            $documentId = $db->lastInsertId();

            // ربط المستند بالحاويات المحددة
            if (!empty($selectedContainers)) {
                $containerStmt = $db->prepare("
                    INSERT INTO document_containers (document_id, container_id)
                    VALUES (:document_id, :container_id)
                ");

                foreach ($selectedContainers as $containerId) {
                    $containerStmt->execute([
                        'document_id' => $documentId,
                        'container_id' => $containerId
                    ]);
                }
            }

            // تسجيل النشاط
            $activityStmt = $db->prepare("
                INSERT INTO activity_log (user_id, activity_type, description, ip_address)
                VALUES (:user_id, 'add_document', :description, :ip_address)
            ");
            $activityStmt->execute([
                'user_id' => getCurrentUserId(),
                'description' => 'تمت إضافة مستند جديد: ' . $document['document_number'] . 
                               (count($selectedContainers) > 0 ? ' مع ' . count($selectedContainers) . ' حاوية' : ''),
                'ip_address' => $_SERVER['REMOTE_ADDR']
            ]);

            $db->commit();
            $_SESSION['success'] = 'تمت إضافة المستند بنجاح مع ' . count($selectedContainers) . ' حاوية';

            // إعادة التوجيه إلى قائمة المستندات
            echo '<script>window.location.href = "index.php?page=financial";</script>';
            return;
        } catch (PDOException $e) {
            $db->rollback();
            $errors['general'] = 'حدث خطأ أثناء حفظ البيانات: ' . $e->getMessage();
        }
    }
}

// الحصول على قائمة الشركات
$selectedCustomerId = isset($_GET['customer_id']) ? (int)$_GET['customer_id'] : null;

try {
    $customersStmt = $db->prepare("SELECT id, name FROM traders ORDER BY name");
    $customersStmt->execute();
    $customers = $customersStmt->fetchAll();
} catch (PDOException $e) {
    $customers = [];
}

// تحديد عنوان الصفحة ونوع المستند
$documentTypeText = '';
switch ($documentType) {
    case 'receipt':
        $documentTypeText = 'سند قبض';
        break;
    case 'payment':
        $documentTypeText = 'سند دفع';
        break;
    case 'expense':
        $documentTypeText = 'سند صرف';
        break;
    case 'transfer':
        $documentTypeText = 'حوالة مالية';
        break;
    case 'capital':
        $documentTypeText = 'رأس المال';
        break;
    case 'profit_distribution':
        $documentTypeText = 'توزيع أرباح';
        break;
}
?>

<div class="page-header d-flex justify-content-between align-items-center">
    <h1 class="h3 mb-0">إضافة <?php echo $documentTypeText; ?></h1>
    <a href="index.php?page=financial" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i> العودة إلى القائمة
    </a>
</div>

<?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">بيانات المستند: <?php echo $documentTypeText; ?></h5>
    </div>
    <div class="card-body">
        <form method="post" action="index.php?page=financial&action=add_new&type=<?php echo $documentType; ?>" class="needs-validation" novalidate>
            <input type="hidden" name="document_type" value="<?php echo $documentType; ?>">
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="amount" class="form-label">المبلغ *</label>
                    <input type="number" step="0.01" class="form-control <?php echo isset($errors['amount']) ? 'is-invalid' : ''; ?>" 
                           id="amount" name="amount" value="<?php echo isset($document['amount']) ? $document['amount'] : ''; ?>" required>
                    <?php if (isset($errors['amount'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['amount']; ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="col-md-6">
                    <label for="document_date" class="form-label">تاريخ المستند *</label>
                    <input type="date" class="form-control <?php echo isset($errors['document_date']) ? 'is-invalid' : ''; ?>" 
                           id="document_date" name="document_date" value="<?php echo isset($document['document_date']) ? $document['document_date'] : date('Y-m-d'); ?>" required>
                    <?php if (isset($errors['document_date'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['document_date']; ?></div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="customer_id" class="form-label">التاجر</label>
                    <select class="form-control form-select" id="customer_id" name="customer_id">
                        <option value="">اختر التاجر</option>
                        <?php foreach ($customers as $customer): ?>
                            <option value="<?php echo $customer['id']; ?>" 
                                    <?php echo (isset($document['customer_id']) && $document['customer_id'] == $customer['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($customer['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-6">
                    <label for="reference_number" class="form-label">رقم المرجع</label>
                    <input type="text" class="form-control" id="reference_number" name="reference_number" 
                           value="<?php echo isset($document['reference_number']) ? htmlspecialchars($document['reference_number']) : ''; ?>">
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="description" class="form-label">الوصف *</label>
                    <textarea class="form-control <?php echo isset($errors['description']) ? 'is-invalid' : ''; ?>" 
                              id="description" name="description" rows="3" required placeholder="وصف المستند المالي..."><?php echo isset($document['description']) ? htmlspecialchars($document['description']) : ''; ?></textarea>
                    <?php if (isset($errors['description'])): ?>
                        <div class="invalid-feedback"><?php echo $errors['description']; ?></div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> حفظ المستند
                    </button>
                    <a href="index.php?page=financial" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قسم الحاويات -->
<div class="card mt-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <h5 class="mb-0">
                <i class="fas fa-shipping-fast text-primary"></i>
                إدارة الحاويات المرتبطة (اختياري)
            </h5>
            <small id="selected-count" class="text-muted">
                لم يتم اختيار أي حاوية
            </small>
        </div>
        <div class="card-tools">
            <button type="button" class="btn btn-sm btn-outline-danger" id="test-load-btn">
                <i class="fas fa-bug"></i> اختبار تحميل
            </button>
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllContainers()">
                <i class="fas fa-check-double"></i> تحديد الكل
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="deselectAllContainers()">
                <i class="fas fa-times"></i> إلغاء التحديد
            </button>
        </div>
    </div>
    
    <!-- فلاتر البحث -->
    <div class="card-body border-bottom" id="containers-filters" style="display: none;">
        <div class="row mb-3">
            <div class="col-md-3">
                <label for="container_search" class="form-label">البحث برقم الحاوية</label>
                <input type="text" class="form-control" id="container_search" placeholder="ادخل رقم الحاوية">
            </div>
            <div class="col-md-3">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="date_from">
            </div>
            <div class="col-md-3">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="date_to">
            </div>
            <div class="col-md-3">
                <label for="status_filter" class="form-label">حالة الحاوية</label>
                <select class="form-control" id="status_filter">
                    <option value="">جميع الحالات</option>
                    <option value="pending">قيد الانتظار</option>
                    <option value="in_progress">قيد التنفيذ</option>
                    <option value="completed">مكتمل</option>
                </select>
            </div>
        </div>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-info btn-sm" onclick="applyFilters()">
                <i class="fas fa-search"></i> تطبيق الفلاتر
            </button>
            <button type="button" class="btn btn-secondary btn-sm" onclick="clearFilters()">
                <i class="fas fa-undo"></i> مسح الفلاتر
            </button>
        </div>
    </div>
    
    <!-- قائمة الحاويات -->
    <div class="card-body">
        <div id="containers-section">
            <div class="text-center py-5">
                <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                <p class="text-muted">اختر تاجر أولاً لعرض الحاويات المتاحة</p>
            </div>
        </div>
    </div>
</div>

<script>
let allContainers = [];
let documentContainerIds = []; // فارغ في صفحة الإضافة

console.log('=== Add New Document Page Initialized ===');

// تعريف دالة الاختبار
function testLoadContainers() {
    console.log('=== TEST LOAD CONTAINERS ===');
    const customerSelect = document.getElementById('customer_id');
    if (customerSelect && customerSelect.value) {
        console.log('Testing with customer ID:', customerSelect.value);
        loadContainers(customerSelect.value);
    } else {
        console.log('No customer selected');
        alert('يرجى اختيار تاجر أولاً');
    }
}

// تعريف دالة تحميل الحاويات
function loadContainers(customerId) {
    console.log('Loading containers from:', `index.php?page=financial&action=add_new&type=<?php echo $documentType; ?>&ajax=get_containers&customer_id=${customerId}`);
    console.log('loadContainers called with ID:', customerId);
    
    const containersSection = document.getElementById('containers-section');
    const filtersSection = document.getElementById('containers-filters');
    
    if (!customerId || customerId === '' || customerId === '0') {
        console.log('Invalid customer ID, not loading containers');
        containersSection.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                <p class="text-muted">اختر تاجر أولاً لعرض الحاويات المتاحة</p>
            </div>`;
        return;
    }
    
    containersSection.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري تحميل الحاويات...</div>';
    
    // استخدام ملف AJAX منفصل
    const url = `ajax/get_financial_containers.php?customer_id=${customerId}`;

    console.log('Loading containers from:', url);
    
    fetch(url)
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            console.log('Response URL:', response.url);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            // التحقق من نوع المحتوى
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                return response.text().then(text => {
                    console.error('Expected JSON but got:', text);
                    throw new Error('Response is not JSON: ' + text.substring(0, 100));
                });
            }
            
            return response.json();
        })
        .then(data => {
            console.log('Parsed data:', data);
            console.log('Success:', data.success);
            console.log('Containers count:', data.containers ? data.containers.length : 0);
            
            if (data.success) {
                console.log('Success is true, checking containers...');
                if (data.containers && data.containers.length > 0) {
                    console.log('*** CALLING displayContainers with data:', data.containers);
                    allContainers = data.containers;
                    displayContainers(data.containers);
                    filtersSection.style.display = 'block';

                    // إضافة إحصائيات
                    updateContainerStats(data.containers);
                    console.log('*** displayContainers called successfully');
                } else {
                    console.log('No containers found - containers is:', data.containers);
                    console.log('Trader ID used:', data.trader_id);
                    console.log('SQL query:', data.sql);
                    console.log('Query params:', data.query_params);
                    containersSection.innerHTML = `
                        <div class="text-center py-4">
                            <i class="fas fa-shipping-fast fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد حاويات متاحة لهذا التاجر</p>
                            <small class="text-muted">التاجر: ${customerId}</small>
                        </div>`;
                    filtersSection.style.display = 'none';
                }
            } else {
                console.log('API returned error:', data.error);
                containersSection.innerHTML = `
                    <div class="alert alert-danger">
                        خطأ: ${data.error || 'حدث خطأ غير معروف'}
                    </div>`;
                filtersSection.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Fetch Error:', error);
            containersSection.innerHTML = `<div class="alert alert-danger">خطأ في تحميل الحاويات: ${error.message}</div>`;
            filtersSection.style.display = 'none';
        });
}

// ربط الدالة بالنطاق العام
window.testLoadContainers = testLoadContainers;
window.loadContainers = loadContainers;

document.addEventListener('DOMContentLoaded', function() {
    console.log('*** DOMContentLoaded event fired ***');
    
    const customerSelect = document.getElementById('customer_id');
    const containersSection = document.getElementById('containers-section');
    const testLoadBtn = document.getElementById('test-load-btn');
    
    console.log('customerSelect element:', customerSelect);
    console.log('containersSection element:', containersSection);
    console.log('Initial customer value:', customerSelect ? customerSelect.value : 'undefined');
    
    if (!customerSelect) {
        console.error('customer_id element not found!');
        return;
    }
    
    // إضافة مستمع لزر الاختبار
    if (testLoadBtn) {
        testLoadBtn.addEventListener('click', function() {
            console.log('Test button clicked!');
            testLoadContainers();
        });
        console.log('Test button event listener added');
    } else {
        console.error('Test load button not found!');
    }
    
    customerSelect.addEventListener('change', function() {
        console.log('*** Customer select changed ***');
        const customerId = this.value;
        console.log('New customer ID:', customerId);
        
        if (customerId) {
            console.log('*** Calling loadContainers ***');
            loadContainers(customerId);
        } else {
            console.log('No customer selected, showing message');
            containersSection.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                    <p class="text-muted">اختر تاجر أولاً لعرض الحاويات المتاحة</p>
                </div>`;
            document.getElementById('containers-filters').style.display = 'none';
        }
    });
    
    // إضافة مستمعين للفلاتر
    setTimeout(function() {
        const searchInput = document.getElementById('container_search');
        const dateFrom = document.getElementById('date_from');
        const dateTo = document.getElementById('date_to');
        const statusFilter = document.getElementById('status_filter');
        
        if (searchInput) searchInput.addEventListener('input', applyFilters);
        if (dateFrom) dateFrom.addEventListener('change', applyFilters);
        if (dateTo) dateTo.addEventListener('change', applyFilters);
        if (statusFilter) statusFilter.addEventListener('change', applyFilters);
    }, 100);
});

function displayContainers(containers) {
    console.log('displayContainers called with:', containers);
    
    const containersSection = document.getElementById('containers-section');
    console.log('containersSection element:', containersSection);
    
    if (!containersSection) {
        console.error('containers-section element not found!');
        return;
    }
    
    if (!containers || containers.length === 0) {
        console.log('No containers to display');
        containersSection.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-shipping-fast fa-3x text-muted mb-3"></i>
                <p class="text-muted">لا توجد حاويات متاحة</p>
            </div>`;
        return;
    }
    
    console.log('Building table for', containers.length, 'containers');
    
    // إنشاء جدول الحاويات
    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" class="form-check-input">
                        </th>
                        <th><i class="fas fa-shipping-fast me-1"></i>رقم الحاوية</th>
                        <th><i class="fas fa-box me-1"></i>المحتوى</th>
                        <th><i class="fas fa-calendar me-1"></i>تاريخ الدخول</th>
                        <th><i class="fas fa-dollar-sign me-1"></i>سعر الشراء</th>
                        <th><i class="fas fa-tag me-1"></i>سعر البيع</th>
                        <th>الحالة</th>
                        <th width="100">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    containers.forEach((container, index) => {
        console.log('Processing container', index, ':', container);
        
        // في صفحة الإضافة، لا توجد حاويات محددة مسبقاً
        const isSelected = false;
        const rowClass = '';
        
        html += `
            <tr class="${rowClass}" data-container-id="${container.id}">
                <td>
                    <input type="checkbox" 
                           name="container_ids[]" 
                           value="${container.id}" 
                           class="form-check-input container-checkbox"
                           data-price="${container.selling_price || 0}"
                           onchange="updateContainerSelection(this)">
                </td>
                <td>
                    <strong class="text-primary">${container.container_number || 'غير محدد'}</strong>
                </td>
                <td>${container.content || 'غير محدد'}</td>
                <td>
                    ${container.entry_date || 'غير محدد'}
                </td>
                <td>
                    <span class="text-success fw-bold">${formatNumber(container.purchase_price || 0)} د.ع</span>
                </td>
                <td>
                    <span class="text-primary fw-bold">${formatNumber(container.selling_price || 0)} د.ع</span>
                </td>
                <td>
                    <span class="badge ${getStatusBadgeClass(container.status)}">
                        ${getStatusText(container.status)}
                    </span>
                </td>
                <td>
                    <button type="button" 
                            class="btn btn-sm btn-outline-success"
                            onclick="toggleContainer(${container.id}, this)">
                        <i class="fas fa-plus"></i>
                        إضافة
                    </button>
                </td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
        
        <div class="row mt-3">
            <div class="col-md-8">
                <div class="alert alert-info mb-0">
                    <i class="fas fa-info-circle"></i>
                    <strong>معلومات:</strong> استخدم صناديق الاختيار لربط الحاويات بهذا المستند
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h6 class="card-title">إجمالي الحاويات المحددة</h6>
                        <span id="selected-containers-count" class="badge badge-primary fs-6">0</span>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    console.log('Setting table HTML content');
    containersSection.innerHTML = html;
    
    // تحديث العدادات
    updateAmount();
    
    console.log('displayContainers completed with table view');
}

// دوال مساعدة
function formatNumber(number) {
    return new Intl.NumberFormat('ar-SA').format(number || 0);
}

function getStatusBadgeClass(status) {
    const statusClasses = {
        'pending': 'bg-warning text-dark',
        'in_progress': 'bg-info text-white',
        'completed': 'bg-success text-white',
        'delayed': 'bg-danger text-white',
        'cancelled': 'bg-secondary text-white'
    };
    
    return statusClasses[status] || 'bg-light text-dark';
}

function getStatusText(status) {
    const statusMap = {
        'pending': 'قيد الانتظار',
        'in_progress': 'قيد التنفيذ',
        'completed': 'مكتمل',
        'delayed': 'متأخر',
        'cancelled': 'ملغي'
    };
    
    return statusMap[status] || status || 'غير محدد';
}

function applyFilters() {
    const customerSelect = document.getElementById('customer_id');
    const customerId = customerSelect.value;
    
    if (!customerId) {
        alert('يرجى اختيار تاجر أولاً');
        return;
    }
    
    const searchTerm = document.getElementById('container_search')?.value || '';
    const dateFrom = document.getElementById('date_from')?.value || '';
    const dateTo = document.getElementById('date_to')?.value || '';
    const statusFilter = document.getElementById('status_filter')?.value || '';
    
    // بناء URL مع الفلاتر
    let url = `ajax/get_financial_containers.php?customer_id=${customerId}`;

    if (searchTerm) url += `&search=${encodeURIComponent(searchTerm)}`;
    if (dateFrom) url += `&date_from=${dateFrom}`;
    if (dateTo) url += `&date_to=${dateTo}`;
    if (statusFilter) url += `&status_filter=${statusFilter}`;
    
    console.log('Applying filters with URL:', url);
    
    const containersSection = document.getElementById('containers-section');
    containersSection.innerHTML = `
        <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin"></i> 
            جاري تطبيق الفلاتر...
        </div>`;
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.containers) {
                allContainers = data.containers;
                displayContainers(data.containers);
            } else {
                containersSection.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لم يتم العثور على حاويات مطابقة للفلاتر المحددة</p>
                    </div>`;
            }
        })
        .catch(error => {
            console.error('Filter Error:', error);
            containersSection.innerHTML = `
                <div class="alert alert-danger">
                    خطأ في تطبيق الفلاتر: ${error.message}
                </div>`;
        });
}

function clearFilters() {
    // مسح جميع الفلاتر
    const searchInput = document.getElementById('container_search');
    const dateFrom = document.getElementById('date_from');
    const dateTo = document.getElementById('date_to');
    const statusFilter = document.getElementById('status_filter');

    if (searchInput) searchInput.value = '';
    if (dateFrom) dateFrom.value = '';
    if (dateTo) dateTo.value = '';
    if (statusFilter) statusFilter.value = '';

    // إعادة تحميل الحاويات بدون فلاتر
    const customerSelect = document.getElementById('customer_id');
    const customerId = customerSelect.value;

    if (customerId) {
        loadContainers(customerId);
    }
}

// دالة تحديث إحصائيات الحاويات
function updateContainerStats(containers) {
    if (!containers || containers.length === 0) return;

    // حساب الإحصائيات
    const stats = {
        total: containers.length,
        pending: containers.filter(c => c.status === 'pending').length,
        in_progress: containers.filter(c => c.status === 'in_progress').length,
        completed: containers.filter(c => c.status === 'completed').length,
        totalValue: containers.reduce((sum, c) => sum + (parseFloat(c.selling_price) || 0), 0)
    };

    // تحديث عداد الحاويات في العنوان
    const selectedCountElement = document.getElementById('selected-count');
    if (selectedCountElement) {
        selectedCountElement.innerHTML = `
            <i class="fas fa-boxes text-primary"></i>
            إجمالي الحاويات: <strong>${stats.total}</strong> |
            متاحة: <strong class="text-success">${stats.pending}</strong> |
            قيمة إجمالية: <strong class="text-info">${stats.totalValue.toLocaleString()} د.ع</strong>
        `;
    }

    console.log('Container Stats:', stats);
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.container-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
        updateRowStyle(checkbox, selectAll.checked);
    });
    
    updateAmount();
}

// وظائف إدارة الحاويات الجديدة
function selectAllContainers() {
    const checkboxes = document.querySelectorAll('.container-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
        updateRowStyle(checkbox, true);
    });
    
    const selectAll = document.getElementById('selectAll');
    if (selectAll) selectAll.checked = true;
    
    updateAmount();
}

function deselectAllContainers() {
    const checkboxes = document.querySelectorAll('.container-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
        updateRowStyle(checkbox, false);
    });
    
    const selectAll = document.getElementById('selectAll');
    if (selectAll) selectAll.checked = false;
    
    updateAmount();
}

function toggleContainer(containerId, button) {
    const checkbox = document.querySelector(`input[value="${containerId}"]`);
    if (checkbox) {
        checkbox.checked = !checkbox.checked;
        updateContainerSelection(checkbox);
        updateButtonText(button, checkbox.checked);
    }
}

function updateContainerSelection(checkbox) {
    const isSelected = checkbox.checked;
    updateRowStyle(checkbox, isSelected);
    
    // تحديث زر الإجراء في نفس الصف
    const containerId = checkbox.value;
    const button = checkbox.closest('tr').querySelector(`button[onclick*="${containerId}"]`);
    if (button) {
        updateButtonText(button, isSelected);
    }
    
    updateAmount();
}

function updateRowStyle(checkbox, isSelected) {
    const row = checkbox.closest('tr');
    if (row) {
        if (isSelected) {
            row.classList.add('table-primary');
        } else {
            row.classList.remove('table-primary');
        }
    }
}

function updateButtonText(button, isSelected) {
    if (isSelected) {
        button.className = 'btn btn-sm btn-outline-danger';
        button.innerHTML = '<i class="fas fa-times"></i> إزالة';
    } else {
        button.className = 'btn btn-sm btn-outline-success';
        button.innerHTML = '<i class="fas fa-plus"></i> إضافة';
    }
}

function updateAmount() {
    const checkboxes = document.querySelectorAll('.container-checkbox:checked');
    let totalAmount = 0;
    
    checkboxes.forEach(checkbox => {
        totalAmount += parseFloat(checkbox.dataset.price || 0);
    });
    
    // تحديث عداد الحاويات
    const countDisplay = document.getElementById('selected-containers-count');
    if (countDisplay) {
        countDisplay.textContent = checkboxes.length;
    }
    
    // تحديث النص التوضيحي
    const selectedCount = document.getElementById('selected-count');
    if (selectedCount) {
        if (checkboxes.length > 0) {
            selectedCount.innerHTML = `
                <i class="fas fa-check-circle text-success"></i>
                تم اختيار ${checkboxes.length} حاوية - إجمالي: ${formatNumber(totalAmount)} د.ع
            `;
        } else {
            selectedCount.innerHTML = `
                <i class="fas fa-info-circle text-muted"></i>
                لم يتم اختيار أي حاوية
            `;
        }
    }
    
    // تحديث حالة "تحديد الكل"
    const selectAllCheckbox = document.getElementById('selectAll');
    const allCheckboxes = document.querySelectorAll('.container-checkbox');
    if (selectAllCheckbox && allCheckboxes.length > 0) {
        const checkedCount = checkboxes.length;
        const totalCount = allCheckboxes.length;
        
        selectAllCheckbox.checked = checkedCount === totalCount;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
    }
}

// إضافة مستمع لحفظ النموذج
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form[method="post"]');
    if (form) {
        form.addEventListener('submit', function(e) {
            console.log('Form submission started');
            
            // إزالة حقول الحاويات المخفية السابقة
            const existingInputs = this.querySelectorAll('input[name="container_ids[]"]');
            existingInputs.forEach(input => {
                if (input.type === 'hidden') {
                    input.remove();
                }
            });
            
            // إضافة الحاويات المحددة كحقول مخفية
            const selectedCheckboxes = document.querySelectorAll('.container-checkbox:checked');
            selectedCheckboxes.forEach(checkbox => {
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'container_ids[]';
                hiddenInput.value = checkbox.value;
                this.appendChild(hiddenInput);
            });
            
            console.log('Form submitted with', selectedCheckboxes.length, 'selected containers');
        });
    }
});
</script>

