<?php
// التحقق من الصلاحيات
if (!hasPermission('reports')) {
    header('Location: index.php?page=unauthorized');
    exit;
}

// استعلام البحث
$dateFrom = isset($_GET['date_from']) && !empty($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-01'); // أول الشهر الحالي
$dateTo = isset($_GET['date_to']) && !empty($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-t'); // آخر الشهر الحالي
$customerFilter = isset($_GET['customer_id']) ? (int)$_GET['customer_id'] : 0;
$containerFilter = isset($_GET['container_id']) ? (int)$_GET['container_id'] : 0;

// بناء استعلام البحث
$query = "
    SELECT fd.*,
           c.name as customer_name,
           cn.container_number
    FROM financial_documents fd
    LEFT JOIN customers c ON fd.customer_id = c.id
    LEFT JOIN containers cn ON fd.container_id = cn.id
    WHERE fd.document_date BETWEEN :date_from AND :date_to
";
$params = [
    'date_from' => $dateFrom,
    'date_to' => $dateTo
];

if ($customerFilter > 0) {
    $query .= " AND fd.customer_id = :customer_id";
    $params['customer_id'] = $customerFilter;
}

if ($containerFilter > 0) {
    $query .= " AND fd.container_id = :container_id";
    $params['container_id'] = $containerFilter;
}

$query .= " ORDER BY fd.document_date, fd.document_type";

try {
    // تنفيذ الاستعلام
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $documents = $stmt->fetchAll();

    // الحصول على قائمة الشركات للفلتر
    $customersStmt = $db->query("SELECT id, name FROM customers ORDER BY name");
    $customers = $customersStmt->fetchAll();

    // الحصول على قائمة الحاويات للفلتر
    $containersStmt = $db->query("
        SELECT c.id, c.container_number, cu.name as customer_name
        FROM containers c
        JOIN customers cu ON c.customer_id = cu.id
        ORDER BY c.container_number
    ");
    $containers = $containersStmt->fetchAll();

    // حساب الإجماليات
    $totalReceipts = 0;
    $totalPayments = 0;
    $totalExpenses = 0;
    $totalTransfers = 0;
    $totalCapital = 0;

    foreach ($documents as $doc) {
        switch ($doc['document_type']) {
            case 'receipt':
                $totalReceipts += $doc['amount'];
                break;
            case 'payment':
                $totalPayments += $doc['amount'];
                break;
            case 'expense':
                $totalExpenses += $doc['amount'];
                break;
            case 'transfer':
                $totalTransfers += $doc['amount'];
                break;
            case 'capital':
                $totalCapital += $doc['amount'];
                break;
        }
    }

    $totalIncome = $totalReceipts + $totalCapital;
    $totalOutcome = $totalPayments + $totalExpenses;
    $balance = $totalIncome - $totalOutcome;

} catch (PDOException $e) {
    $_SESSION['error'] = 'حدث خطأ أثناء استرجاع بيانات التقرير: ' . $e->getMessage();
    $documents = [];
    $customers = [];
    $containers = [];
    $totalReceipts = 0;
    $totalPayments = 0;
    $totalExpenses = 0;
    $totalTransfers = 0;
    $totalCapital = 0;
    $totalIncome = 0;
    $totalOutcome = 0;
    $balance = 0;
}
?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">التقارير المالية</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="index.php">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php?page=reports">التقارير</a></li>
                    <li class="breadcrumb-item active">التقارير المالية</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- Navigation Tabs -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="btn-group" role="group">
                    <a href="index.php?page=reports&type=financial" class="btn btn-primary">
                        <i class="fas fa-chart-line"></i> التقارير المالية
                    </a>
                    <a href="index.php?page=reports&type=cash" class="btn btn-outline-primary">
                        <i class="fas fa-money-bill-wave"></i> التقارير النقدية
                    </a>
                    <a href="index.php?page=reports&type=regulatory" class="btn btn-outline-primary">
                        <i class="fas fa-clipboard-check"></i> التقارير الرقابية
                    </a>
                    <a href="index.php?page=reports&type=penalties" class="btn btn-outline-primary">
                        <i class="fas fa-exclamation-triangle"></i> تقارير الغرامات
                    </a>
                </div>
            </div>
        </div>

        <!-- Alerts -->
        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <h5><i class="icon fas fa-ban"></i> خطأ!</h5>
                <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>

        <!-- Search and Filter Card -->
        <div class="card card-primary card-outline">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter"></i> معايير التقرير
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form method="get" action="index.php">
                    <input type="hidden" name="page" value="reports">
                    <input type="hidden" name="type" value="financial">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="date_from">
                                    <i class="fas fa-calendar-alt"></i> من تاريخ
                                </label>
                                <input type="date" class="form-control" id="date_from" name="date_from"
                                       value="<?php echo $dateFrom; ?>">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="date_to">
                                    <i class="fas fa-calendar-alt"></i> إلى تاريخ
                                </label>
                                <input type="date" class="form-control" id="date_to" name="date_to"
                                       value="<?php echo $dateTo; ?>">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="customer_id">
                                    <i class="fas fa-user"></i> الشركة
                                </label>
                                <div class="searchable-select-wrapper">
                                    <select class="form-control searchable-select" id="customer_id" name="customer_id" data-placeholder="اختر الشركة أو ابحث...">
                                        <option value="">جميع الشركات</option>
                                        <?php foreach ($customers as $customer): ?>
                                            <option value="<?php echo $customer['id']; ?>" <?php echo $customerFilter == $customer['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($customer['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="container_id">
                                    <i class="fas fa-box"></i> الحاوية
                                </label>
                                <div class="searchable-select-wrapper">
                                    <select class="form-control searchable-select" id="container_id" name="container_id" data-placeholder="اختر الحاوية أو ابحث...">
                                        <option value="">جميع الحاويات</option>
                                        <?php foreach ($containers as $container): ?>
                                            <option value="<?php echo $container['id']; ?>" <?php echo $containerFilter == $container['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($container['container_number']); ?> - <?php echo htmlspecialchars($container['customer_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> عرض التقرير
                                </button>
                                <a href="index.php?page=reports&type=financial" class="btn btn-default">
                                    <i class="fas fa-redo"></i> إعادة تعيين
                                </a>
                                <button type="button" class="btn btn-success" onclick="printReport()">
                                    <i class="fas fa-print"></i> طباعة التقرير
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-lg-4 col-6">
                <!-- small box -->
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3><?php echo number_format($totalIncome, 0); ?></h3>
                        <p>إجمالي الإيرادات</p>
                        <small>سندات قبض: <?php echo number_format($totalReceipts, 0); ?> | رأس المال: <?php echo number_format($totalCapital, 0); ?></small>
                    </div>
                    <div class="icon">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <a href="#receipts-section" class="small-box-footer">
                        المزيد من التفاصيل <i class="fas fa-arrow-circle-right"></i>
                    </a>
                </div>
            </div>
            <!-- ./col -->
            <div class="col-lg-4 col-6">
                <!-- small box -->
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3><?php echo number_format($totalOutcome, 0); ?></h3>
                        <p>إجمالي المصروفات</p>
                        <small>سندات دفع: <?php echo number_format($totalPayments, 0); ?> | سندات صرف: <?php echo number_format($totalExpenses, 0); ?></small>
                    </div>
                    <div class="icon">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <a href="#expenses-section" class="small-box-footer">
                        المزيد من التفاصيل <i class="fas fa-arrow-circle-right"></i>
                    </a>
                </div>
            </div>
            <!-- ./col -->
            <div class="col-lg-4 col-6">
                <!-- small box -->
                <div class="small-box <?php echo $balance >= 0 ? 'bg-info' : 'bg-warning'; ?>">
                    <div class="inner">
                        <h3><?php echo number_format(abs($balance), 0); ?></h3>
                        <p>الرصيد <?php echo $balance >= 0 ? '(ربح)' : '(خسارة)'; ?></p>
                        <small>الحوالات المالية: <?php echo number_format($totalTransfers, 0); ?></small>
                    </div>
                    <div class="icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <a href="#summary-section" class="small-box-footer">
                        عرض التفاصيل <i class="fas fa-arrow-circle-right"></i>
                    </a>
                </div>
            </div>
            <!-- ./col -->
        </div>
        <!-- /.row -->

        <!-- تفاصيل التقرير -->
        <div class="card" id="reportContent">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-file-invoice-dollar"></i> تفاصيل التقرير المالي
                </h3>
                <div class="card-tools">
                    <span class="badge badge-info">الفترة: <?php echo $dateFrom; ?> - <?php echo $dateTo; ?></span>
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-tool" data-card-widget="maximize">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>
            <div class="card-body table-responsive p-0">
                <table class="table table-hover text-nowrap">
                    <thead>
                        <tr>
                            <th style="width: 50px">#</th>
                            <th>رقم المستند</th>
                            <th>النوع</th>
                            <th>المبلغ</th>
                            <th>التاريخ</th>
                            <th>الشركة</th>
                            <th>الحاوية</th>
                            <th>طريقة الدفع</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                <tbody>
                    <?php if (empty($documents)): ?>
                        <tr>
                            <td colspan="9" class="text-center py-4">لا توجد بيانات للعرض</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($documents as $index => $document): ?>
                            <tr>
                                <td><?php echo $index + 1; ?></td>
                                <td><?php echo htmlspecialchars($document['document_number']); ?></td>
                                <td>
                                    <?php
                                    $typeClass = '';
                                    $typeText = '';

                                    switch ($document['document_type']) {
                                        case 'receipt':
                                            $typeClass = 'success';
                                            $typeText = 'سند قبض';
                                            break;
                                        case 'payment':
                                            $typeClass = 'danger';
                                            $typeText = 'سند دفع';
                                            break;
                                        case 'expense':
                                            $typeClass = 'warning';
                                            $typeText = 'سند صرف';
                                            break;
                                        case 'transfer':
                                            $typeClass = 'info';
                                            $typeText = 'حوالة مالية';
                                            break;
                                        case 'capital':
                                            $typeClass = 'primary';
                                            $typeText = 'رأس المال';
                                            break;
                                    }
                                    ?>
                                    <span class="badge badge-<?php echo $typeClass; ?>"><?php echo $typeText; ?></span>
                                </td>
                                <td class="number"><?php echo number_format($document['amount'], 2); ?></td>
                                <td><?php echo date('d/m/Y', strtotime($document['document_date'])); ?></td>
                                <td><?php echo $document['customer_id'] ? htmlspecialchars($document['customer_name']) : '-'; ?></td>
                                <td><?php echo $document['container_id'] ? htmlspecialchars($document['container_number']) : '-'; ?></td>
                                <td>
                                    <?php
                                    $methodText = '';

                                    switch ($document['payment_method']) {
                                        case 'cash':
                                            $methodText = 'نقدي';
                                            break;
                                        case 'bank_transfer':
                                            $methodText = 'تحويل بنكي';
                                            break;
                                        case 'check':
                                            $methodText = 'شيك';
                                            break;
                                        case 'other':
                                            $methodText = 'أخرى';
                                            break;
                                    }
                                    ?>
                                    <?php echo $methodText; ?>
                                </td>
                                <td><?php echo htmlspecialchars($document['description']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
                <tfoot>
                    <tr class="table-light">
                        <th colspan="3">الإجمالي</th>
                        <th class="number"><?php echo number_format($totalIncome - $totalOutcome, 2); ?></th>
                        <th colspan="5"></th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->

<script>
function printReport() {
    // طباعة المحتوى الحالي
    var printContent = document.getElementById('reportContent').innerHTML;
    var originalContent = document.body.innerHTML;

    // إنشاء محتوى الطباعة
    var printHTML = '<html><head><title>التقرير المالي</title>';
    printHTML += '<style>';
    printHTML += 'body { font-family: Arial, sans-serif; direction: rtl; }';
    printHTML += 'table { width: 100%; border-collapse: collapse; }';
    printHTML += 'th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }';
    printHTML += 'th { background-color: #f2f2f2; }';
    printHTML += '.number { direction: ltr; text-align: right; }';
    printHTML += '@media print { @page { size: landscape; } }';
    printHTML += '</style></head><body>';
    printHTML += '<h1 style="text-align: center;">التقرير المالي</h1>';
    printHTML += '<p style="text-align: center;">الفترة: <?php echo $dateFrom; ?> - <?php echo $dateTo; ?></p>';
    printHTML += printContent;
    printHTML += '</body></html>';

    // فتح نافذة جديدة للطباعة
    var printWindow = window.open('', '_blank');
    printWindow.document.write(printHTML);
    printWindow.document.close();
    printWindow.print();
}
</script>
