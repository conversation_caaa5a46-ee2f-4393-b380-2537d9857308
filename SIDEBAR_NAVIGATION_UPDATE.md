# تحديث التنقل في الـ Sidebar

## المشكلة
صفحة قائمة التجار (`pages/traders_list.php`) وصفحة كشف حساب التاجر (`pages/trader_statement.php`) لم تظهر في الـ sidebar للتنقل.

## الحل المطبق

### 1. تصحيح رابط التجار في الـ Sidebar
```php
<!-- قبل التصحيح -->
<li class="nav-item">
  <a href="index.php?page=traders" class="nav-link <?php echo $page == 'traders' ? 'active' : ''; ?>">
    <i class="nav-icon fas fa-users"></i>
    <p>إدارة التجار</p>
  </a>
</li>

<!-- بعد التصحيح -->
<li class="nav-item">
  <a href="index.php?page=traders_list" class="nav-link <?php echo $page == 'traders_list' ? 'active' : ''; ?>">
    <i class="nav-icon fas fa-user-tie"></i>
    <p>التجار</p>
  </a>
</li>
```

### 2. إضافة رابط كشف حساب التاجر في قسم التقارير
```php
<!-- Trader Account Statement -->
<li class="nav-item">
  <a href="index.php?page=trader_statement" class="nav-link <?php echo $page == 'trader_statement' ? 'active' : ''; ?>">
    <i class="nav-icon fas fa-file-invoice-dollar"></i>
    <p>كشف حساب تاجر</p>
  </a>
</li>
```

### 3. إضافة رابط التجار في الـ Navbar العلوي
```php
<!-- Traders -->
<li class="nav-item">
  <a href="index.php?page=traders_list" class="nav-link <?php echo $page == 'traders_list' ? 'active' : ''; ?>">
    <i class="fas fa-user-tie"></i>
    <span class="nav-text">التجار</span>
  </a>
</li>
```

### 4. إضافة عناوين الصفحات
```php
$pageTitle = '';
switch($page) {
  case 'dashboard': $pageTitle = 'لوحة التحكم'; break;
  case 'containers': $pageTitle = 'إدارة الحاويات'; break;
  case 'customers': $pageTitle = 'إدارة الشركات'; break;
  case 'traders_list': $pageTitle = 'التجار'; break;           // جديد
  case 'trader_statement': $pageTitle = 'كشف حساب تاجر'; break; // جديد
  case 'financial': $pageTitle = 'الإدارة المالية'; break;
  // ...
}
```

## التحديثات المطبقة في `includes/header.php`

### 1. قسم الـ Sidebar الجانبي
- **السطر 428-433**: تصحيح رابط التجار من `page=traders` إلى `page=traders_list`
- **السطر 568-574**: إضافة رابط كشف حساب التاجر في قسم التقارير

### 2. قسم الـ Navbar العلوي
- **السطر 122-128**: إضافة رابط التجار في navbar العلوي (يظهر عند طي الـ sidebar)

### 3. قسم العناوين
- **السطر 636-637**: إضافة عناوين الصفحات الجديدة

## هيكل التنقل الجديد

### في الـ Sidebar:
```
📁 الشركات والتجار
├── 🏢 إدارة الشركات (customers)
└── 👔 التجار (traders_list)

📁 التقارير
├── 📊 التقارير المالية
├── 💰 التقارير النقدية
├── 📋 التقارير الرقابية
├── ⚠️ تقارير الغرامات
├── ✅ كشف حساب شركة
└── 💼 كشف حساب تاجر (جديد)
```

### في الـ Navbar العلوي (عند طي الـ sidebar):
```
🏠 لوحة التحكم | 📦 الحاويات | 🏢 الشركات | 👔 التجار | 💰 المالية | 📊 التقارير | 👥 المستخدمين | ⚙️ الإعدادات
```

## الروابط المتاحة الآن

### 1. قائمة التجار:
```
http://localhost/ccis_appis/index.php?page=traders_list
```
- **الموقع في الـ sidebar**: الشركات والتجار → التجار
- **الأيقونة**: `fas fa-user-tie`
- **الوصف**: عرض قائمة جميع التجار مع إحصائياتهم

### 2. كشف حساب التاجر:
```
http://localhost/ccis_appis/index.php?page=trader_statement
```
- **الموقع في الـ sidebar**: التقارير → كشف حساب تاجر
- **الأيقونة**: `fas fa-file-invoice-dollar`
- **الوصف**: عرض كشف حساب شامل لتاجر محدد

### 3. كشف حساب تاجر محدد:
```
http://localhost/ccis_appis/index.php?page=trader_statement&trader_id=1
```

### 4. طباعة كشف الحساب:
```
http://localhost/ccis_appis/index.php?page=trader_statement&trader_id=1&print=1
```

## الميزات الجديدة في التنقل

### 1. تنقل متسق:
- ✅ الروابط تعمل في الـ sidebar والـ navbar
- ✅ حالة "active" تعمل بشكل صحيح
- ✅ الأيقونات متناسقة ومعبرة

### 2. تنظيم منطقي:
- ✅ التجار في قسم "الشركات والتجار"
- ✅ كشف الحساب في قسم "التقارير"
- ✅ ترتيب منطقي للعناصر

### 3. سهولة الوصول:
- ✅ روابط مباشرة من قائمة التجار لكشف الحساب
- ✅ أزرار طباعة مباشرة
- ✅ تنقل سريع بين الصفحات

## اختبار التحديثات

### 1. اختبار الـ Sidebar:
1. افتح الصفحة الرئيسية
2. تأكد من ظهور "التجار" في قسم "الشركات والتجار"
3. تأكد من ظهور "كشف حساب تاجر" في قسم "التقارير"
4. اختبر النقر على الروابط

### 2. اختبار الـ Navbar:
1. اطوِ الـ sidebar
2. تأكد من ظهور "التجار" في الـ navbar العلوي
3. اختبر النقر على الرابط

### 3. اختبار حالة Active:
1. انتقل لصفحة التجار
2. تأكد من تمييز الرابط بحالة "active"
3. كرر نفس الاختبار لكشف الحساب

## الخلاصة

تم بنجاح:
- ✅ **إصلاح رابط التجار** في الـ sidebar
- ✅ **إضافة رابط كشف حساب التاجر** في قسم التقارير
- ✅ **إضافة رابط التجار** في الـ navbar العلوي
- ✅ **إضافة عناوين الصفحات** الجديدة
- ✅ **تحسين تنظيم التنقل** بشكل عام

الآن يمكن الوصول لجميع صفحات التجار بسهولة من خلال الـ sidebar والـ navbar! 🎉

## ملاحظات مهمة

### الصلاحيات:
- صفحة التجار تتطلب صلاحية `customers`
- كشف الحساب يتطلب صلاحية `financial`

### التوافق:
- التحديثات متوافقة مع التصميم الحالي
- لا تؤثر على الصفحات الأخرى
- تحافظ على نفس نمط التنقل المستخدم

### المرونة:
- يمكن إضافة المزيد من روابط التجار بسهولة
- يمكن تخصيص الأيقونات والعناوين
- يمكن إعادة ترتيب العناصر حسب الحاجة
